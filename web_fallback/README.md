# QR Code Web Fallback Setup

This folder contains the web fallback system for QR codes when users don't have the mobile app installed.

## How it Works

1. **QR Code Generation**: The app generates QR codes with URLs like:
   ```
   https://ghanshyammurtibhandar.com/order/ORDER_ID?verify=HASH&t=TIMESTAMP
   ```

2. **User Scans QR Code**:
   - **With App**: The app intercepts the URL and opens the order details directly
   - **Without App**: The web browser opens the fallback page

3. **Web Fallback**: Shows a beautiful landing page with options to:
   - Open in mobile app (if installed)
   - View on web application
   - Download the app from stores

## Setup Instructions

### Option 1: Host on Your Website (Recommended)

1. Upload the `order.html` file to your website at:
   ```
   https://ghanshyammurtibhandar.com/order/
   ```

2. Configure your web server to serve `order.html` for any path matching:
   ```
   /order/*
   ```

3. **For Apache (.htaccess)**:
   ```apache
   RewriteEngine On
   RewriteRule ^order/([^/]+)/?$ /order.html [L]
   ```

4. **For Nginx**:
   ```nginx
   location ~ ^/order/([^/]+)/?$ {
       try_files /order.html =404;
   }
   ```

### Option 2: Use Flutter Web App

1. Build your Flutter app for web:
   ```bash
   flutter build web
   ```

2. Deploy the web build to:
   ```
   https://ghanshyammurtibhandar.com/
   ```

3. The Flutter web app will handle the `/order/:orderId` route automatically.

### Option 3: Redirect to Flutter Web App

If you want to use the existing Flutter web app, modify the `order.html` file to immediately redirect:

```javascript
// Add this to the script section
window.location.href = `https://your-flutter-web-app.com/order/${orderId}?verify=${verify}&t=${timestamp}`;
```

## Testing

1. Generate a QR code from the app
2. Scan it with a device that doesn't have the app
3. Verify the web fallback works correctly

## Customization

### Update App Store Links

In `order.html`, update these lines with your actual app store URLs:

```javascript
// Play Store
window.open('https://play.google.com/store/apps/details?id=com.ghanshyammurtibhandar.app', '_blank');

// App Store  
window.open('https://apps.apple.com/app/ghanshyam-murti-bhandar/id123456789', '_blank');
```

### Update Deep Link Scheme

In `order.html`, update the app URL scheme:

```javascript
const appUrl = `ghanshyammurtibhandar://order/${orderId}?verify=${verify}&t=${timestamp}`;
```

Make sure this matches your app's deep link configuration.

### Styling

The `order.html` file includes responsive CSS that matches your app's design. You can customize:

- Colors (currently using red theme)
- Fonts
- Layout
- Animations

## Security

The QR codes include:
- **Order ID**: Public identifier
- **Verify Hash**: Security hash to prevent tampering
- **Timestamp**: When the QR was generated

The web fallback should validate these parameters before showing order details.

## Analytics

Consider adding analytics to track:
- QR code scans
- App vs web usage
- Conversion rates
- User behavior

Example with Google Analytics:

```javascript
// Add to order.html
gtag('event', 'qr_scan', {
  'event_category': 'engagement',
  'event_label': 'order_qr',
  'order_id': orderId
});
```

## Troubleshooting

### QR Code Not Working
1. Check the URL format in QR Service
2. Verify web server configuration
3. Test the fallback URL directly

### App Not Opening
1. Verify deep link scheme is registered
2. Check app installation
3. Test on different devices

### Web App Not Loading
1. Check Flutter web build
2. Verify routing configuration
3. Check browser console for errors

## Support

For technical support with the QR system:
1. Check the app logs for QR generation
2. Test the web fallback URL manually
3. Verify server configuration
4. Contact development team if issues persist
