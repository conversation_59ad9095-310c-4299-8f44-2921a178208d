<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details - <PERSON><PERSON><PERSON><PERSON> Murti <PERSON>ar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .logo .red { color: #e74c3c; }
        .logo .black { color: #2c3e50; }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            background: #3498db;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 24px;
        }
        
        p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-direction: column;
        }
        
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #e74c3c;
            color: white;
        }
        
        .btn-primary:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .btn-secondary:hover {
            background: #d5dbdb;
            transform: translateY(-2px);
        }
        
        .app-stores {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .app-stores .btn {
            flex: 1;
            font-size: 14px;
            padding: 12px 15px;
        }
        
        .loading {
            display: none;
            color: #3498db;
            margin-top: 20px;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            .buttons {
                gap: 12px;
            }
            
            .app-stores {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <span class="red">Ghanshyam</span><span class="black">Murti</span>
        </div>
        <div class="subtitle">Premium Religious Artifacts</div>
        
        <div class="icon">📦</div>
        
        <h1>Order Details</h1>
        <p>To view your complete order details with real-time tracking, product images, and more features, please use our mobile app or web application.</p>
        
        <div class="buttons">
            <a href="#" class="btn btn-primary" id="openApp">
                📱 Open in App
            </a>
            <a href="#" class="btn btn-secondary" id="openWeb">
                🌐 View on Web
            </a>
        </div>
        
        <div class="app-stores">
            <a href="#" class="btn btn-secondary" id="playStore">
                📱 Play Store
            </a>
            <a href="#" class="btn btn-secondary" id="appStore">
                🍎 App Store
            </a>
        </div>
        
        <div class="loading" id="loading">
            Loading order details...
        </div>
    </div>

    <script>
        // Get order ID from URL
        const urlPath = window.location.pathname;
        const orderId = urlPath.split('/').pop();
        const urlParams = new URLSearchParams(window.location.search);
        const verify = urlParams.get('verify');
        const timestamp = urlParams.get('t');
        
        // Update links with order information
        document.addEventListener('DOMContentLoaded', function() {
            const webAppUrl = `https://ghanshyammurtibhandar.com/order/${orderId}?verify=${verify}&t=${timestamp}`;
            const appUrl = `ghanshyammurtibhandar://order/${orderId}?verify=${verify}&t=${timestamp}`;
            
            // Open in app button
            document.getElementById('openApp').addEventListener('click', function(e) {
                e.preventDefault();
                
                // Try to open the app
                window.location.href = appUrl;
                
                // Fallback to web app after a delay
                setTimeout(function() {
                    window.location.href = webAppUrl;
                }, 2000);
            });
            
            // Open in web button
            document.getElementById('openWeb').addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = webAppUrl;
            });
            
            // App store links (you can update these with actual store URLs)
            document.getElementById('playStore').addEventListener('click', function(e) {
                e.preventDefault();
                alert('Play Store link coming soon!');
                // window.open('https://play.google.com/store/apps/details?id=com.ghanshyammurtibhandar.app', '_blank');
            });
            
            document.getElementById('appStore').addEventListener('click', function(e) {
                e.preventDefault();
                alert('App Store link coming soon!');
                // window.open('https://apps.apple.com/app/ghanshyam-murti-bhandar/id123456789', '_blank');
            });
            
            // Auto-redirect to web app after 5 seconds
            setTimeout(function() {
                document.getElementById('loading').style.display = 'block';
                setTimeout(function() {
                    window.location.href = webAppUrl;
                }, 2000);
            }, 5000);
        });
        
        // Handle app installation detection
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            deferredPrompt = e;
        });
    </script>
</body>
</html>
