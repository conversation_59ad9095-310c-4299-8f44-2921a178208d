import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/services/injection_container.dart';
import 'package:ghanshyam_murti_bhandar/core/services/router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/singletons/cache.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_config.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/auth_guard.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logging service first
  LoggingService.instance;
  Log.lifecycle('App starting', context: {
    'timestamp': DateTime.now().toIso8601String(),
  });

  // Initialize existing services
  await init();
  Log.lifecycle('Existing services initialized');

  // Initialize API services
  await ApiService.instance.initialize();
  ApiServiceProvider.initializeServices();
  ApiConfig.printCurrentEnvironment(); // Show which API environment is being used
  Log.lifecycle('API services initialized');

  // Initialize authentication guard
  AuthGuard.instance;
  Log.lifecycle('Authentication guard initialized');

  Log.lifecycle('App ready to launch');
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    final lightTheme = ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colours.lightThemePrimaryColour,
        brightness: Brightness.light,
      ),
      fontFamily: 'Switzer',
      scaffoldBackgroundColor: Colours.lightThemeTintStockColour,
      appBarTheme: AppBarTheme(
        backgroundColor: Colours.lightThemeTintStockColour,
        foregroundColor: Colours.lightThemePrimaryTextColour,
      ),
      useMaterial3: true,
    );

    final darkTheme = ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colours.lightThemePrimaryColour,
        brightness: Brightness.dark,
      ),
      fontFamily: 'Switzer',
      scaffoldBackgroundColor: Colours.darkThemeBGDark,
      appBarTheme: AppBarTheme(
        backgroundColor: Colours.darkThemeBGDark,
        foregroundColor: Colours.lightThemeWhiteColour,
      ),
      useMaterial3: true,
    );

    return ValueListenableBuilder<ThemeMode>(
      valueListenable: Cache.instance.themeModeNotifier,
      builder: (context, themeMode, child) {
        return MaterialApp.router(
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: themeMode,
          routerConfig: router,
          title: 'Ghanshyam Murti Bhandar',
        );
      },
    );
  }
}
