import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/ecommerce_logo.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  static const path = '/login';

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  bool _hasCheckedForPrefilledEmail = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasCheckedForPrefilledEmail) {
      _checkForPrefilledEmail();
      _hasCheckedForPrefilledEmail = true;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Check if email is passed from signup and pre-fill it
  void _checkForPrefilledEmail() {
    try {
      final uri = Uri.parse(ModalRoute.of(context)?.settings.name ?? '');
      final email = uri.queryParameters['email'];
      if (email != null && email.isNotEmpty) {
        _emailController.text = email;
        Log.ui('Email pre-filled from signup', screen: 'LoginView', context: {
          'email': email,
        });
      }
    } catch (e) {
      // If there's any issue with getting the route, just continue without pre-filling
      Log.ui('Could not pre-fill email from route', screen: 'LoginView', context: {
        'error': e.toString(),
      });
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  Future<void> _handleLogin() async {
    // Log login attempt start
    Log.auth('Login attempt started', action: 'LOGIN_START', data: {
      'email': _emailController.text.trim(),
    });

    if (!_formKey.currentState!.validate()) {
      Log.auth('Login validation failed', action: 'VALIDATION_FAILED');
      return;
    }

    Log.auth('Starting login API call', action: 'API_CALL_START');
    setState(() {
      _isLoading = true;
    });

    final stopwatch = Stopwatch()..start();

    try {
      // Call the login API
      Log.api('Calling login endpoint', endpoint: 'auth/login', data: {
        'email': _emailController.text.trim(),
        'passwordLength': _passwordController.text.length,
      });

      final response = await ApiService.instance.auth.login(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      stopwatch.stop();
      Log.performance('Login API call', stopwatch.elapsed, data: {
        'success': response.isSuccess,
        'statusCode': response.statusCode,
      });

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          Log.auth('Login successful', action: 'LOGIN_SUCCESS', data: {
            'userId': response.data?.user.id,
            'userName': response.data?.user.name,
            'userEmail': response.data?.user.email,
            'hasToken': response.data?.token != null,
          });

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Welcome back, ${response.data?.user.name}!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );

          // Navigate to home on successful login
          Log.userAction('Navigate to home after login', context: {
            'from': 'login',
            'userId': response.data?.user.id,
          });
          context.go('/home');
        } else {
          // Handle specific error cases - prioritize API message
          String errorMessage = response.error ?? 'Login failed';
          Color backgroundColor = Colors.red;

          // Only override with custom messages if API message is empty or generic
          if (errorMessage.isEmpty ||
              errorMessage.toLowerCase().contains('login failed') ||
              errorMessage.toLowerCase().contains('error occurred')) {

            if (response.statusCode == 401) {
              errorMessage = 'Invalid email or password. Please check your credentials and try again.';
            } else if (response.statusCode == 404) {
              errorMessage = 'Account not found. Please check your email or sign up for a new account.';
              backgroundColor = Colors.orange;
            } else {
              errorMessage = 'Login failed. Please try again.';
            }
          }

          // Special styling for account not found
          if (response.statusCode == 404) {
            backgroundColor = Colors.orange;
          }

          Log.auth('Login API failed', action: 'LOGIN_API_FAILED', data: {
            'error': response.error,
            'statusCode': response.statusCode,
            'message': response.message,
            'displayedToUser': errorMessage,
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: backgroundColor,
              duration: Duration(seconds: 4),
              action: response.statusCode == 404 ? SnackBarAction(
                label: 'Sign Up',
                textColor: Colors.white,
                onPressed: () {
                  Log.userAction('Navigate to signup from login error', context: {
                    'reason': 'account_not_found',
                    'email': _emailController.text.trim(),
                  });
                  context.go('/signup');
                },
              ) : null,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      stopwatch.stop();
      Log.e('Login process failed with exception',
            tag: 'LOGIN_EXCEPTION',
            error: e,
            stackTrace: stackTrace);

      Log.performance('Failed login API call', stopwatch.elapsed, data: {
        'error': e.toString(),
      });

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show user-friendly error message
        final errorMessage = ApiErrorHandler.getUserFriendlyMessage(e);
        Log.ui('Showing error message to user', screen: 'LoginView', context: {
          'errorMessage': errorMessage,
          'originalError': e.toString(),
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 4),
          ),
        );
      }
    }

    Log.auth('Login process completed', action: 'LOGIN_END', data: {
      'duration': stopwatch.elapsed.inMilliseconds,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Sign In", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: 32),
                // Logo
                EcommerceLogo(
                  style: TextStyles.appLogo.copyWith(
                    color: Colours.lightThemePrimaryColour,
                    fontSize: 28,
                  ),
                ),
                SizedBox(height: 48),
                // Welcome text
                Text(
                  'Welcome Back!',
                  style: TextStyles.headingMedium1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'Sign in to your account to continue',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 32),
                // Email field
                CustomTextField(
                  controller: _emailController,
                  hintText: 'Enter your email',
                  labelText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: Icon(
                    Icons.email_outlined,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validateEmail,
                ),
                SizedBox(height: 16),
                // Password field
                CustomTextField(
                  controller: _passwordController,
                  hintText: 'Enter your password',
                  labelText: 'Password',
                  obscureText: true,
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validatePassword,
                ),
                SizedBox(height: 12),
                // Forgot password
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      context.push('/forgot-password');
                    },
                    child: Text(
                      'Forgot Password?',
                      style: TextStyles.paragraphSubTextRegular3.copyWith(
                        color: Colours.lightThemePrimaryColour,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 24),
                // Login button
                CustomButton(
                  onPressed: _handleLogin,
                  text: 'Sign In',
                  isLoading: _isLoading,
                ),
                SizedBox(height: 24),
                // Divider
                Row(
                  children: [
                    Expanded(
                      child: Divider(
                        color: Colours.lightThemeStockColour,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'OR',
                        style: TextStyles.paragraphSubTextRegular2.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Divider(
                        color: Colours.lightThemeStockColour,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24),
                // Sign up link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Don't have an account? ",
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        context.push('/signup');
                      },
                      child: Text(
                        'Sign Up',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemePrimaryColour,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
