import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class ResetPasswordView extends StatefulWidget {
  final String email;
  final String token;

  const ResetPasswordView({
    super.key,
    required this.email,
    required this.token,
  });

  static const path = '/reset-password';

  @override
  State<ResetPasswordView> createState() => _ResetPasswordViewState();
}

class _ResetPasswordViewState extends State<ResetPasswordView> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _passwordReset = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain uppercase, lowercase and number';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Log.ui(
        'Resetting password with token',
        screen: 'ResetPasswordView',
        context: {'email': widget.email, 'hasToken': widget.token.isNotEmpty},
      );

      final response = await ApiService.instance.auth.resetPassword(
        email: widget.email,
        token: widget.token,
        password: _passwordController.text,
        passwordConfirmation: _confirmPasswordController.text,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          setState(() {
            _passwordReset = true;
          });

          Log.ui('Password reset successfully', screen: 'ResetPasswordView');
        } else {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to reset password'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      Log.e('Error resetting password', tag: 'RESET_PASSWORD_VIEW', error: e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reset password. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleBackToLogin() {
    context.go('/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Reset Password", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Animation
            Center(
              child: Lottie.asset(
                Media.loading,
                height: 120,
                width: 120,
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(height: 32),

            if (!_passwordReset) ...[
              // Reset password form
              Text(
                'Create New Password',
                style: TextStyles.headingMedium3.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'Enter your new password below',
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 32),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    CustomTextField(
                      controller: _passwordController,
                      hintText: 'Enter new password',
                      labelText: 'New Password',
                      obscureText: _obscurePassword,
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      validator: _validatePassword,
                    ),
                    SizedBox(height: 16),
                    CustomTextField(
                      controller: _confirmPasswordController,
                      hintText: 'Confirm new password',
                      labelText: 'Confirm Password',
                      obscureText: _obscureConfirmPassword,
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                      validator: _validateConfirmPassword,
                    ),
                    SizedBox(height: 24),
                    CustomButton(
                      onPressed: _handleResetPassword,
                      text: 'Reset Password',
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // Success state
              Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 64,
                      color: Colors.green,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Password Reset Successfully!',
                      style: TextStyles.headingMedium3.copyWith(
                        color: Colors.green,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Your password has been reset successfully. You can now sign in with your new password.',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24),
              CustomButton(onPressed: _handleBackToLogin, text: 'Sign In'),
            ],

            SizedBox(height: 32),
            // Back to login link
            if (!_passwordReset)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Remember your password? ",
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemeSecondaryTextColour,
                    ),
                  ),
                  TextButton(
                    onPressed: _handleBackToLogin,
                    child: Text(
                      'Sign In',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemePrimaryColour,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
