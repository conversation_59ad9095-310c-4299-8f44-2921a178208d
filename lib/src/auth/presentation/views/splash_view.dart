import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/ecommerce_logo.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/auth_guard.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    Log.lifecycle('Splash screen started', context: {
      'timestamp': DateTime.now().toIso8601String(),
    });

    try {
      // Add a minimum splash duration for better UX
      final stopwatch = Stopwatch()..start();

      // Check authentication state
      final authGuard = AuthGuard.instance;
      final isAuthenticated = await authGuard.isAuthenticated();
      final isFirstTime = await authGuard.isFirstTime();

      stopwatch.stop();

      Log.auth('Authentication check completed', action: 'SPLASH_AUTH_CHECK', data: {
        'isAuthenticated': isAuthenticated,
        'isFirstTime': isFirstTime,
        'checkDuration': stopwatch.elapsed.inMilliseconds,
      });

      // Ensure minimum splash duration (1 second for better UX)
      const minSplashDuration = Duration(milliseconds: 1500);
      if (stopwatch.elapsed < minSplashDuration) {
        final remainingTime = minSplashDuration - stopwatch.elapsed;
        await Future.delayed(remainingTime);
      }

      if (!mounted) return;

      // Navigate based on authentication state
      String targetRoute;
      if (isFirstTime) {
        targetRoute = '/onboarding';
      } else if (isAuthenticated) {
        targetRoute = '/home';
      } else {
        targetRoute = '/login';
      }

      Log.userAction('Navigate from splash', context: {
        'targetRoute': targetRoute,
        'reason': isFirstTime ? 'first_time' : isAuthenticated ? 'authenticated' : 'not_authenticated',
      });

      context.go(targetRoute);

    } catch (e, stackTrace) {
      Log.e('Splash initialization failed',
            tag: 'SPLASH_VIEW',
            error: e,
            stackTrace: stackTrace);

      if (mounted) {
        // On error, default to login
        context.go('/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final screenHeight = MediaQuery.of(context).size.height;


    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark
                ? [
                    Colours.darkThemeBGDark,
                    Colours.darkThemeDarkSharpColor,
                    Colours.darkThemeDarkNavBarColour,
                  ]
                : [
                    Colours.lightThemePrimaryColour,
                    Colours.lightThemeSecondaryColour,
                    Colours.lightThemePrimaryTint,
                  ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Background decorative elements
              Positioned(
                top: screenHeight * 0.1,
                right: -50,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (isDark ? Colors.white : Colors.white)
                        .withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                bottom: screenHeight * 0.2,
                left: -30,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (isDark ? Colors.white : Colors.white)
                        .withValues(alpha: 0.08),
                  ),
                ),
              ),

              // Main content
              Column(
                children: [
                  // Top spacer
                  SizedBox(height: screenHeight * 0.15),

                  // Logo section with animation
                  Expanded(
                    flex: 3,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo container with subtle shadow
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withValues(alpha: 0.15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: EcommerceLogo(
                            style: TextStyles.appLogo.copyWith(
                              color: Colors.white,
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1.2,
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // App name with elegant styling
                        Text(
                          'Ghanshyam',
                          style: TextStyles.headingMedium1.copyWith(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.w300,
                            letterSpacing: 2.0,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        Text(
                          'Murti Bhandar',
                          style: TextStyles.headingMedium1.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            letterSpacing: 4.0,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 16),

                        // Tagline
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            'Divine Sculptures & Spiritual Art',
                            style: TextStyles.paragraphSubTextRegular1.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 12,
                              letterSpacing: 1.0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Loading section
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Custom loading animation
                        const SizedBox(
                          width: 60,
                          height: 60,
                          child: LoadingWidget(
                            size: 60,
                            showMessage: false,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Loading text
                        Text(
                          'Loading...',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 14,
                            letterSpacing: 1.0,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom section
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: Column(
                      children: [
                        // Version or copyright
                        Text(
                          'Version 1.0.0',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.white.withValues(alpha: 0.5),
                            fontSize: 11,
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Powered by
                        Text(
                          'Crafted with ❤️ for devotees',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.white.withValues(alpha: 0.6),
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
