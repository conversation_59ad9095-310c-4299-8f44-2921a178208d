import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class ForgotPasswordView extends StatefulWidget {
  const ForgotPasswordView({super.key});

  static const path = '/forgot-password';

  @override
  State<ForgotPasswordView> createState() => _ForgotPasswordViewState();
}

class _ForgotPasswordViewState extends State<ForgotPasswordView> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Log.ui(
        'Sending forgot password request',
        screen: 'ForgotPasswordView',
        context: {'email': _emailController.text.trim()},
      );

      final response = await ApiService.instance.auth.forgotPassword(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          setState(() {
            _emailSent = true;
          });

          Log.ui(
            'Forgot password email sent successfully',
            screen: 'ForgotPasswordView',
          );
        } else {
          // Check if it's a "route not found" error (backend not implemented)
          final errorMessage = response.error ?? 'Failed to send reset email';
          final isRouteNotFound = errorMessage.toLowerCase().contains(
            'route not found',
          );

          // Show appropriate error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRouteNotFound
                    ? 'Forgot password feature is not available yet. Please contact support.'
                    : errorMessage,
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      Log.e(
        'Error sending forgot password request',
        tag: 'FORGOT_PASSWORD_VIEW',
        error: e,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Check if it's an API exception with route not found
        final errorMessage = e.toString();
        final isRouteNotFound = errorMessage.toLowerCase().contains(
          'route not found',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRouteNotFound
                  ? 'Forgot password feature is not available yet. Please contact support.'
                  : 'Failed to send reset email. Please try again.',
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _handleBackToLogin() {
    context.go('/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Reset Password", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: 32),
              // Illustration
              SizedBox(
                height: 200,
                child: Lottie.asset(Media.search, fit: BoxFit.contain),
              ),
              SizedBox(height: 32),
              if (!_emailSent) ...[
                // Reset password form
                Text(
                  'Forgot Your Password?',
                  style: TextStyles.headingMedium1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'Enter your email address and we\'ll send you a link to reset your password.',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 32),
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: _emailController,
                        hintText: 'Enter your email',
                        labelText: 'Email',
                        keyboardType: TextInputType.emailAddress,
                        prefixIcon: Icon(
                          Icons.email_outlined,
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                        validator: _validateEmail,
                      ),
                      SizedBox(height: 24),
                      CustomButton(
                        onPressed: _handleResetPassword,
                        text: 'Send Reset Link',
                        isLoading: _isLoading,
                      ),

                      SizedBox(height: 16),

                      // Note about feature availability
                      Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.orange.withValues(alpha: 0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.orange,
                              size: 20,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'If you\'re unable to reset your password, please contact our support team for assistance.',
                                style: TextStyles.paragraphSubTextRegular2
                                    .copyWith(color: Colors.orange.shade700),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                // Success state
                Container(
                  padding: EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.green.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 64,
                        color: Colors.green,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Email Sent!',
                        style: TextStyles.headingMedium3.copyWith(
                          color: Colors.green,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'We\'ve sent a password reset link to ${_emailController.text}',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24),
                CustomButton(
                  onPressed: _handleBackToLogin,
                  text: 'Back to Sign In',
                ),
              ],
              SizedBox(height: 32),
              // Back to login link
              if (!_emailSent)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Remember your password? ",
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    TextButton(
                      onPressed: _handleBackToLogin,
                      child: Text(
                        'Sign In',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemePrimaryColour,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              // Resend email option
              if (_emailSent) ...[
                SizedBox(height: 16),
                Text(
                  "Didn't receive the email?",
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
                TextButton(
                  onPressed:
                      _isLoading
                          ? null
                          : () {
                            setState(() {
                              _emailSent = false;
                            });
                            // Trigger the forgot password request again
                            _handleResetPassword();
                          },
                  child: Text(
                    'Resend',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemePrimaryColour,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
