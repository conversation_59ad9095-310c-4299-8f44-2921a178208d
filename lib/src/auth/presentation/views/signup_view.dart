import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/ecommerce_logo.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class SignupView extends StatefulWidget {
  const SignupView({super.key});

  static const path = '/signup';

  @override
  State<SignupView> createState() => _SignupViewState();
}

class _SignupViewState extends State<SignupView> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _agreeToTerms = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateFirstName(String? value) {
    if (value == null || value.isEmpty) {
      return 'First name is required';
    }
    if (value.length < 2) {
      return 'First name must be at least 2 characters';
    }
    return null;
  }

  String? _validateLastName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Last name is required';
    }
    if (value.length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(
      r'^[0-9]{10}$',
    ).hasMatch(value.replaceAll(RegExp(r'[^\d]'), ''))) {
      return 'Please enter a valid 10-digit phone number';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  Future<void> _handleSignup() async {
    // Log signup attempt start
    Log.auth(
      'Signup attempt started',
      action: 'SIGNUP_START',
      data: {
        'email': _emailController.text.trim(),
        'firstName': _firstNameController.text.trim(),
        'lastName': _lastNameController.text.trim(),
        'hasPhone': _phoneController.text.trim().isNotEmpty,
      },
    );

    if (!_formKey.currentState!.validate()) {
      Log.auth('Signup validation failed', action: 'VALIDATION_FAILED');
      return;
    }

    if (!_agreeToTerms) {
      Log.auth('Signup failed - terms not agreed', action: 'TERMS_NOT_AGREED');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please agree to the Terms and Conditions'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Log.auth('Starting signup API call', action: 'API_CALL_START');
    setState(() {
      _isLoading = true;
    });

    final stopwatch = Stopwatch()..start();

    try {
      // Call the signup API
      Log.api(
        'Calling signup endpoint',
        endpoint: 'auth/signup',
        data: {
          'firstName': _firstNameController.text.trim(),
          'lastName': _lastNameController.text.trim(),
          'email': _emailController.text.trim(),
          'passwordLength': _passwordController.text.length,
        },
      );

      final response = await ApiService.instance.auth.signup(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      );

      stopwatch.stop();
      Log.performance(
        'Signup API call',
        stopwatch.elapsed,
        data: {
          'success': response.isSuccess,
          'statusCode': response.statusCode,
        },
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          Log.auth(
            'Signup successful',
            action: 'SIGNUP_SUCCESS',
            data: {
              'userId': response.data?.user.id,
              'userName': response.data?.user.name,
              'userEmail': response.data?.user.email,
              'hasToken': response.data?.token != null,
            },
          );

          // Clear the auth token since we want user to login manually
          try {
            final logoutResponse = await ApiService.instance.auth.logout();
            if (logoutResponse.isSuccess) {
              Log.auth(
                'Auth token cleared after signup',
                action: 'TOKEN_CLEARED',
              );
            } else {
              Log.auth(
                'Logout API failed but token cleared',
                action: 'TOKEN_CLEARED_LOCAL',
              );
            }
          } catch (e) {
            Log.auth(
              'Failed to clear auth token after signup',
              action: 'TOKEN_CLEAR_FAILED',
              data: {'error': e.toString()},
            );
          }

          // Skip phone update since user needs to login first
          // Phone can be updated after login in the profile section

          // Check if widget is still mounted before using context
          if (mounted) {
            Log.ui(
              'Showing signup success message and navigating to login',
              screen: 'SignupView',
            );

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Account created successfully! Please sign in to continue.',
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );

            // Navigate to login screen for user to sign in
            Log.userAction(
              'Navigate to login after successful signup',
              context: {
                'from': 'signup',
                'userId': response.data?.user.id,
                'userEmail': response.data?.user.email,
              },
            );

            // Navigate to login with email parameter if possible
            final email = _emailController.text.trim();
            context.go('/login?email=${Uri.encodeComponent(email)}');
          }
        } else {
          Log.auth(
            'Signup API failed',
            action: 'SIGNUP_API_FAILED',
            data: {
              'error': response.error,
              'statusCode': response.statusCode,
              'message': response.message,
            },
          );

          // Handle specific error cases
          String errorMessage = response.error ?? 'Failed to create account';
          Color backgroundColor = Colors.red;

          // Special handling for 409 (email already exists)
          if (response.statusCode == 409) {
            errorMessage =
                'This email is already registered. Would you like to login instead?';
            backgroundColor = Colors.orange;

            // Show error with login option
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: backgroundColor,
                duration: Duration(seconds: 6),
                action: SnackBarAction(
                  label: 'Login',
                  textColor: Colors.white,
                  onPressed: () {
                    Log.userAction(
                      'Navigate to login from signup error',
                      context: {
                        'reason': 'email_already_exists',
                        'email': _emailController.text.trim(),
                      },
                    );
                    context.go('/login');
                  },
                ),
              ),
            );
          } else {
            // Show regular error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: backgroundColor,
                duration: Duration(seconds: 4),
              ),
            );
          }
        }
      }
    } catch (e, stackTrace) {
      stopwatch.stop();
      Log.e(
        'Signup process failed with exception',
        tag: 'SIGNUP_EXCEPTION',
        error: e,
        stackTrace: stackTrace,
      );

      Log.performance(
        'Failed signup API call',
        stopwatch.elapsed,
        data: {'error': e.toString()},
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show user-friendly error message
        final errorMessage = ApiErrorHandler.getUserFriendlyMessage(e);
        Log.ui(
          'Showing error message to user',
          screen: 'SignupView',
          context: {
            'errorMessage': errorMessage,
            'originalError': e.toString(),
          },
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 4),
          ),
        );
      }
    }

    Log.auth(
      'Signup process completed',
      action: 'SIGNUP_END',
      data: {'duration': stopwatch.elapsed.inMilliseconds},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Create Account", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // SizedBox(height: 16),
                // Logo
                EcommerceLogo(
                  style: TextStyles.appLogo.copyWith(
                    color: Colours.lightThemePrimaryColour,
                    fontSize: 24,
                  ),
                ),
                SizedBox(height: 5),
                // Welcome text
                Text(
                  'Join Us Today!',
                  style: TextStyles.headingMedium1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4),
                Text(
                  'Create your account to get started',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20),
                // First Name field
                CustomTextField(
                  controller: _firstNameController,
                  hintText: 'Enter your first name',
                  labelText: 'First Name',
                  keyboardType: TextInputType.name,
                  prefixIcon: Icon(
                    Icons.person_outline,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validateFirstName,
                ),
                SizedBox(height: 12),
                // Last Name field
                CustomTextField(
                  controller: _lastNameController,
                  hintText: 'Enter your last name',
                  labelText: 'Last Name',
                  keyboardType: TextInputType.name,
                  prefixIcon: Icon(
                    Icons.person_outline,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validateLastName,
                ),
                SizedBox(height: 12),
                // Email field
                CustomTextField(
                  controller: _emailController,
                  hintText: 'Enter your email',
                  labelText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: Icon(
                    Icons.email_outlined,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validateEmail,
                ),
                SizedBox(height: 12),
                // Phone field
                CustomTextField(
                  controller: _phoneController,
                  hintText: 'Enter your phone number',
                  labelText: 'Phone Number',
                  keyboardType: TextInputType.phone,
                  prefixIcon: Icon(
                    Icons.phone_outlined,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validatePhone,
                ),
                SizedBox(height: 12),
                // Password field
                CustomTextField(
                  controller: _passwordController,
                  hintText: 'Enter your password',
                  labelText: 'Password',
                  obscureText: true,
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validatePassword,
                ),
                SizedBox(height: 12),
                // Confirm Password field
                CustomTextField(
                  controller: _confirmPasswordController,
                  hintText: 'Confirm your password',
                  labelText: 'Confirm Password',
                  obscureText: true,
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  validator: _validateConfirmPassword,
                ),
                SizedBox(height: 12),
                // Terms and conditions checkbox
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                      activeColor: Colours.lightThemePrimaryColour,
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(top: 12),
                        child: Text.rich(
                          TextSpan(
                            text: 'I agree to the ',
                            style: TextStyles.paragraphSubTextRegular3.copyWith(
                              color: Colours.lightThemeSecondaryTextColour,
                            ),
                            children: [
                              TextSpan(
                                text: 'Terms and Conditions',
                                style: TextStyle(
                                  color: Colours.lightThemePrimaryColour,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                              TextSpan(text: ' and '),
                              TextSpan(
                                text: 'Privacy Policy',
                                style: TextStyle(
                                  color: Colours.lightThemePrimaryColour,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
                // Signup button
                CustomButton(
                  onPressed: _handleSignup,
                  text: 'Create Account',
                  isLoading: _isLoading,
                ),
                SizedBox(height: 24),
                // Sign in link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Already have an account? ",
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        context.go('/login');
                      },
                      child: Text(
                        'Sign In',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemePrimaryColour,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
