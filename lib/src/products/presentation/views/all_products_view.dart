import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';
import 'package:ghanshyam_murti_bhandar/src/product_details/presentation/views/product_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/search/presentation/widgets/filter_widgets.dart';

class AllProductsView extends StatefulWidget {
  const AllProductsView({super.key});

  static const path = '/all-products';

  @override
  State<AllProductsView> createState() => _AllProductsViewState();
}

class _AllProductsViewState extends State<AllProductsView> {
  bool _isLoading = true;
  List<ProductModel> _products = [];
  List<ProductModel> _filteredProducts = [];
  Map<String, bool> _wishlistStates = {}; // Track wishlist states separately

  // Filter options
  String _selectedCategory = 'All';
  String _searchQuery = '';

  final List<String> _categories = [
    'All',
    'Electronics',
    'Clothing',
    'Home & Garden',
    'Sports',
  ];

  @override
  void initState() {
    super.initState();
    _loadAllProducts();
  }

  Future<void> _loadAllProducts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load all products from API
      final response = await ApiService.instance.products.getProducts(
        page: 1,
        limit: 100, // Load more products for "all products" view
      );

      if (response.isSuccess && response.data != null) {
        _products = response.data!.products;
        _filteredProducts = List.from(_products);

        // Initialize wishlist states from product data
        _wishlistStates.clear();
        for (final product in _products) {
          _wishlistStates[product.id] = product.isWishlisted;
        }

        debugPrint('Loaded ${_products.length} products');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load products: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredProducts =
          _products.where((product) {
            // Search filter
            if (_searchQuery.isNotEmpty &&
                !product.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) &&
                !product.description.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                )) {
              return false;
            }

            // No price filter applied

            return true;
          }).toList();

      // No sorting applied - products shown in default order
    });
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder:
          (context) => FilterBottomSheet(
            categories: _categories,
            selectedCategory: _selectedCategory,
            onApplyFilters: (category) {
              setState(() {
                _selectedCategory = category ?? 'All';
              });
              _applyFilters();
            },
          ),
    );
  }

  Future<void> _toggleFavorite(ProductModel product) async {
    // Get current state (from our map or product default)
    final currentState = _wishlistStates[product.id] ?? product.isWishlisted;
    final previousState = currentState;

    // Optimistically update UI immediately
    setState(() {
      _wishlistStates[product.id] = !currentState;
    });

    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(
        product.id,
      );

      if (response.isSuccess && response.data != null) {
        // Update with actual server response
        setState(() {
          _wishlistStates[product.id] = response.data!.isWishlisted;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data!.wasAdded
                    ? 'Added to favorites'
                    : 'Removed from favorites',
              ),
              backgroundColor:
                  response.data!.wasAdded ? Colors.green : Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Revert to previous state on failure
        setState(() {
          _wishlistStates[product.id] = previousState;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update favorites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Revert to previous state on error
      setState(() {
        _wishlistStates[product.id] = previousState;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CoreUtils.adaptiveColour(
        context,
        lightModeColour: Colours.lightThemeWhiteColour,
        darkModeColour: Colours.darkThemeBGDark,
      ),
      appBar: AppBar(
        backgroundColor: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeBGDark,
        ),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colours.classAdaptiveTextColour(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'All Products',
          style: TextStyles.headingSemiBold1.copyWith(
            color: Colours.classAdaptiveTextColour(context),
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: Colours.classAdaptiveTextColour(context),
            ),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                _searchQuery = value;
                _applyFilters();
              },
              decoration: InputDecoration(
                hintText: 'Search products...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Color(0xFFe53e3e)),
                ),
              ),
            ),
          ),

          // Results count and sort
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '${_filteredProducts.length} products found',
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ),

          SizedBox(height: 16),

          // Products Grid
          Expanded(
            child:
                _isLoading
                    ? Center(child: LoadingWidget())
                    : _filteredProducts.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No products found',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Try adjusting your search or filters',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                    : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.65,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                        itemCount: _filteredProducts.length,
                        itemBuilder: (context, index) {
                          final product = _filteredProducts[index];
                          return _buildProductCard(product);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(ProductModel product) {
    return GestureDetector(
      onTap: () {
        // Navigate to product details
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailsView(product: product),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colors.white,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colors.grey.withValues(alpha: 0.2),
              darkModeColour: Colors.grey.withValues(alpha: 0.4),
            ),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colors.grey[100],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child:
                          product.imageUrl.isNotEmpty
                              ? Image.network(
                                product.imageUrl,
                                width: double.infinity,
                                height: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[200],
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey[400],
                                      size: 40,
                                    ),
                                  );
                                },
                              )
                              : Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.image,
                                  color: Colors.grey[400],
                                  size: 40,
                                ),
                              ),
                    ),

                    // Discount Badge
                    if (product.originalPrice != null &&
                        product.originalPrice! > product.price)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${product.discountPercentage.round()}% OFF',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                    // Favorite Icon
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () => _toggleFavorite(product),
                        child: Container(
                          padding: EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            (_wishlistStates[product.id] ??
                                    product.isWishlisted)
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                (_wishlistStates[product.id] ??
                                        product.isWishlisted)
                                    ? Colors.red
                                    : Colors.grey[600],
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Product Details
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Name
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Product Description
                    if (product.description.isNotEmpty)
                      Text(
                        product.description,
                        style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    SizedBox(height: 4),

                    // Rating
                    if (product.rating > 0)
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 12),
                          SizedBox(width: 2),
                          Text(
                            '${product.rating}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                          if (product.reviewCount > 0)
                            Text(
                              ' (${product.reviewCount})',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),

                    Spacer(),

                    // Price
                    Row(
                      children: [
                        Text(
                          '₹${product.price.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFFe53e3e),
                          ),
                        ),
                        if (product.originalPrice != null &&
                            product.originalPrice! > product.price) ...[
                          SizedBox(width: 4),
                          Text(
                            '₹${product.originalPrice!.toStringAsFixed(0)}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[500],
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
