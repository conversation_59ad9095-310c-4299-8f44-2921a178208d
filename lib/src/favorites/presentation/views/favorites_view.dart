import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/favorites/presentation/widgets/favorite_item_widget.dart';
import 'package:ghanshyam_murti_bhandar/src/product_details/presentation/views/product_details_view.dart';

import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/wishlist_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';

class FavoritesView extends StatefulWidget {
  const FavoritesView({super.key});

  static const path = '/favorites';

  @override
  State<FavoritesView> createState() => _FavoritesViewState();
}

class _FavoritesViewState extends State<FavoritesView> {
  WishlistModel? _wishlist;
  List<FavoriteItem> _favoriteItems = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadWishlist();
  }

  Future<void> _loadWishlist() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await ApiService.instance.wishlist.getWishlist();

      if (response.isSuccess && response.data != null) {
        _wishlist = response.data!;

        // Convert WishlistItemModel to FavoriteItem for UI compatibility
        _favoriteItems = _wishlist!.items.map((item) => FavoriteItem(
          id: item.product.id,
          name: item.product.name,
          image: item.product.imageUrl,
          price: item.product.price,
          originalPrice: item.product.originalPrice,
          category: item.product.category?.name ?? 'Unknown',
          isInStock: item.product.isInStock,
        )).toList();
      } else {
        _error = response.error ?? 'Failed to load wishlist';
        _favoriteItems = [];
      }
    } catch (e) {
      _error = 'Error loading wishlist: $e';
      _favoriteItems = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _removeFromFavorites(String itemId) async {
    try {
      final response = await ApiService.instance.wishlist.removeFromWishlist(itemId);

      if (response.isSuccess) {
        // Reload wishlist to get updated data
        await _loadWishlist();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Removed from favorites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to remove from favorites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error removing from favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addToCart(String itemId) async {
    try {
      final item = _favoriteItems.firstWhere((item) => item.id == itemId);

      final response = await ApiService.instance.cart.addToCart(
        productId: itemId,
        quantity: 1,
      );

      if (response.isSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${item.name} added to cart'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View Cart',
              textColor: Colors.white,
              onPressed: () {
                // Navigate to cart tab
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/dashboard',
                  (route) => false,
                  arguments: {'initialIndex': 3}, // Cart tab index
                );
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Failed to add to cart'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearAllFavorites() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear Favorites'),
        content: Text('Are you sure you want to remove all items from favorites?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                final response = await ApiService.instance.wishlist.clearWishlist();

                if (response.isSuccess) {
                  await _loadWishlist();

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('All favorites cleared'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to clear favorites'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error clearing favorites'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text('Clear All'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Favorites", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          if (_favoriteItems.isNotEmpty)
            IconButton(
              onPressed: _clearAllFavorites,
              icon: Icon(Icons.clear_all_outlined),
              tooltip: 'Clear all favorites',
            ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(_error!, style: TextStyle(color: Colors.grey)),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWishlist,
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _favoriteItems.isEmpty
                  ? EmptyStateWidget(
                      icon: Icons.favorite_outline,
                      title: 'No favorites yet',
                      description: 'Add items to your favorites to see them here',
                      buttonText: 'Start Shopping',
                      onButtonPressed: () {
                        // Navigate to home or products
                      },
                    )
          : RefreshIndicator(
              onRefresh: _loadWishlist,
              child: Column(
                children: [
                // Header with count
                Container(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${_favoriteItems.length} ${_favoriteItems.length == 1 ? 'item' : 'items'}',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            'Sort by: ',
                            style: TextStyles.paragraphSubTextRegular1.copyWith(
                              color: Colours.lightThemeSecondaryTextColour,
                            ),
                          ),
                          DropdownButton<String>(
                            value: 'Recent',
                            underline: SizedBox(),
                            style: TextStyles.paragraphSubTextRegular1.copyWith(
                              color: Colours.lightThemePrimaryColour,
                            ),
                            items: ['Recent', 'Price: Low to High', 'Price: High to Low', 'Name']
                                .map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              // Implement sorting logic
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Favorites Grid
                Expanded(
                  child: GridView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: ResponsiveUtils.getGridCrossAxisCount(context),
                      childAspectRatio: ResponsiveUtils.getGridAspectRatio(context),
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: _favoriteItems.length,
                    itemBuilder: (context, index) {
                      final item = _favoriteItems[index];
                      return FavoriteItemWidget(
                        item: item,
                        onRemove: () => _removeFromFavorites(item.id),
                        onAddToCart: () => _addToCart(item.id),
                        onTap: () {
                          // Convert FavoriteItem to ProductModel for navigation
                          final product = ProductModel(
                            id: item.id,
                            name: item.name,
                            description: 'Favorite product',
                            price: item.price.toDouble(),
                            originalPrice: item.originalPrice?.toDouble(),
                            images: [item.image],
                            rating: 4.5,
                            reviewCount: 100,
                            stock: 10,
                            isWishlisted: true,
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          );
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ProductDetailsView(product: product),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
