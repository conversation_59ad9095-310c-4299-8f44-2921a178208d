import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class FavoriteItem {
  final String id;
  final String name;
  final String image;
  final double price;
  final double? originalPrice;
  final String category;
  final bool isInStock;

  FavoriteItem({
    required this.id,
    required this.name,
    required this.image,
    required this.price,
    this.originalPrice,
    required this.category,
    this.isInStock = true,
  });
}

class FavoriteItemWidget extends StatelessWidget {
  const FavoriteItemWidget({
    super.key,
    required this.item,
    required this.onRemove,
    required this.onAddToCart,
    required this.onTap,
  });

  final FavoriteItem item;
  final VoidCallback onRemove;
  final VoidCallback onAddToCart;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colours.lightThemeStockColour, width: 0.5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 1,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colours.lightThemeTintStockColour,
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      image: CoreUtils.isValidImageUrl(item.image)
                              ? DecorationImage(
                                  image: NetworkImage(item.image),
                                  fit: BoxFit.cover,
                                  onError: (exception, stackTrace) {},
                                )
                              : null,
                    ),
                    child: !CoreUtils.isValidImageUrl(item.image)
                            ? Center(
                                child: Icon(
                                  Icons.image_outlined,
                                  color: Colours.lightThemeSecondaryTextColour,
                                  size: 32,
                                ),
                              )
                            : null,
                  ),
                  // Favorite Button
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                      width: 24,
                      height: 24,
                      child: IconButton(
                        onPressed: onRemove,
                        icon: Icon(Icons.favorite, color: Colors.red, size: 12),
                        constraints: BoxConstraints(
                          minWidth: 24,
                          minHeight: 24,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                  // Stock Status
                  if (!item.isInStock)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Out of Stock',
                          style: TextStyles.paragraphSubTextRegular.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Product Details
            Expanded(
              flex: 1,
              child: Padding(
                padding: EdgeInsets.all(3),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 3,
                  children: [
                    // Category - very small
                    Text(
                      item.category,
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                        fontSize: 10,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Product name - compact
                    Text(
                      item.name,
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Price row - compact
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '₹${item.price.toStringAsFixed(0)}',
                            style: TextStyles.headingSemiBold1.copyWith(
                              color: Colours.lightThemePrimaryColour,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        if (item.originalPrice != null)
                          Text(
                            '₹${item.originalPrice!.toStringAsFixed(0)}',
                            style: TextStyles.paragraphSubTextRegular2.copyWith(
                              color: Colours.lightThemeSecondaryTextColour,
                              decoration: TextDecoration.lineThrough,
                              fontSize: 8,
                            ),
                          ),
                      ],
                    ),
                    Spacer(),
                    SizedBox(
                      width: double.infinity,
                      height: 30,
                      child: ElevatedButton(
                        onPressed: item.isInStock ? onAddToCart : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              item.isInStock
                                  ? Colours.lightThemePrimaryColour
                                  : Colours.lightThemeSecondaryTextColour,
                          padding: EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          minimumSize: Size.zero,
                          elevation: 0,
                        ),
                        child: Text(
                          item.isInStock ? 'Add to Cart' : 'Out of Stock',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 8,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    SizedBox(height: 3),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
