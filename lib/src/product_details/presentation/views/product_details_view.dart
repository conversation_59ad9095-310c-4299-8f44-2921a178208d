import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/payment/presentation/views/payment_view.dart';
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/review_model.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';

class ProductDetailsView extends StatefulWidget {
  const ProductDetailsView({
    super.key,
    required this.product,
  });

  final ProductModel product;
  static const path = '/product-details';

  @override
  State<ProductDetailsView> createState() => _ProductDetailsViewState();
}

class _ProductDetailsViewState extends State<ProductDetailsView> {
  int _selectedImageIndex = 0;
  int _quantity = 1;
  bool _isLoading = false;
  bool _isFavorite = false;

  // Reviews state
  ProductReviewsModel? _reviews;
  bool _isLoadingReviews = false;

  List<String> get _productImages {
    if (widget.product.images.isNotEmpty) {
      return widget.product.images.map((image) {
        // Check if the image URL is already complete (starts with http/https)
        if (image.startsWith('http://') || image.startsWith('https://')) {
          return image;
        }
        // If not complete, prepend the base URL
        return 'https://ghanshyambackend.onrender.com/$image';
      }).toList();
    }
    return [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      'https://images.unsplash.com/photo-1596422846543-75c6fc197f07?w=400',
    ];
  }

  final List<ProductModel> _relatedProducts = [];

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.product.isWishlisted;
    _checkWishlistStatus();
    _loadReviews();
  }

  Future<void> _checkWishlistStatus() async {
    try {
      final response = await ApiService.instance.wishlist.checkWishlist(widget.product.id);
      if (response.isSuccess && response.data != null) {
        setState(() {
          _isFavorite = response.data!;
        });
      }
    } catch (e) {
      // Ignore errors, keep current state
    }
  }

  Future<void> _loadReviews() async {
    setState(() {
      _isLoadingReviews = true;
    });

    try {
      final response = await ApiService.instance.reviews.getProductReviews(
        widget.product.id,
        page: 1,
        limit: 5, // Load first 5 reviews
      );

      if (response.isSuccess && response.data != null) {
        setState(() {
          _reviews = response.data!;
        });
      }
    } catch (e) {
      // Handle error silently for now
      debugPrint('Error loading reviews: $e');
    } finally {
      setState(() {
        _isLoadingReviews = false;
      });
    }
  }

  Future<void> _toggleFavorite() async {
    // Optimistically update UI immediately for better UX
    final previousState = _isFavorite;
    setState(() {
      _isFavorite = !_isFavorite;
    });

    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(widget.product.id);

      if (response.isSuccess && response.data != null) {
        // Update with actual server response
        if (mounted) {
          setState(() {
            _isFavorite = response.data!.isWishlisted;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.data!.wasAdded
                  ? 'Added to favorites'
                  : 'Removed from favorites'),
              backgroundColor: response.data!.wasAdded
                  ? Colors.green
                  : Colors.red,
            ),
          );
        }
      } else {
        // Revert to previous state on failure
        if (mounted) {
          setState(() {
            _isFavorite = previousState;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update favorites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Revert to previous state on error
      setState(() {
        _isFavorite = previousState;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addToCart() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await ApiService.instance.cart.addToCart(
        productId: widget.product.id,
        quantity: _quantity,
      );

      if (response.isSuccess && mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${widget.product.name} added to cart'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View Cart',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pushNamed(context, '/cart');
              },
            ),
          ),
        );
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to add to cart'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Product Details", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _toggleFavorite,
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_outline,
              color: _isFavorite ? Colors.red : null,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Images
                  _buildImageSection(),
                  
                  // Product Info
                  _buildProductInfo(),
                  
                  // Description
                  _buildDescription(),
                  
                  // Specifications
                  _buildSpecifications(),
                  
                  // Reviews
                  _buildReviews(),
                  
                  // Related Products
                  _buildRelatedProducts(),
                  
                  SizedBox(height: 100), // Space for bottom bar
                ],
              ),
            ),
          ),
          
          // Bottom Action Bar
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      height: 400,
      child: Column(
        children: [
          // Main Image
          Expanded(
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colours.lightThemeTintStockColour,
                borderRadius: BorderRadius.circular(12),
                image: _productImages.isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(_productImages[_selectedImageIndex]),
                        fit: BoxFit.cover,
                        onError: (exception, stackTrace) {},
                      )
                    : null,
              ),
              child: _productImages.isEmpty
                  ? Center(
                      child: Icon(
                        Icons.image_outlined,
                        color: Colours.lightThemeSecondaryTextColour,
                        size: 64,
                      ),
                    )
                  : null,
            ),
          ),
          
          // Image Thumbnails
          if (_productImages.length > 1)
            Container(
              height: 80,
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _productImages.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedImageIndex = index;
                      });
                    },
                    child: Container(
                      width: 70,
                      height: 70,
                      margin: EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: Colours.lightThemeTintStockColour,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _selectedImageIndex == index
                              ? Colours.lightThemePrimaryColour
                              : Colors.transparent,
                          width: 2,
                        ),
                        image: DecorationImage(
                          image: NetworkImage(_productImages[index]),
                          fit: BoxFit.cover,
                          onError: (exception, stackTrace) {},
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProductInfo() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category
          Text(
            widget.product.categoryName,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),
          SizedBox(height: 8),
          
          // Product Name
          Text(
            widget.product.name,
            style: TextStyles.headingMedium2.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 12),
          
          // Rating and Reviews
          Row(
            children: [
              Icon(
                Icons.star,
                color: Colours.lightThemeYellowTint,
                size: 20,
              ),
              SizedBox(width: 4),
              Text(
                '${widget.product.rating}',
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 8),
              Text(
                '(${widget.product.reviewCount} reviews)',
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          
          // Price
          Row(
            children: [
              Text(
                '₹${widget.product.price.toStringAsFixed(0)}',
                style: TextStyles.headingMedium1.copyWith(
                  color: Colours.lightThemePrimaryColour,
                ),
              ),
              if (widget.product.originalPrice != null) ...[
                SizedBox(width: 12),
                Text(
                  '₹${widget.product.originalPrice!.toStringAsFixed(0)}',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colours.lightThemeSecondaryColour,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${(((widget.product.originalPrice! - widget.product.price) / widget.product.originalPrice!) * 100).round()}% OFF',
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 16),
          
          // Stock Status with Count
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: widget.product.stock > 0
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: widget.product.stock > 0
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.red.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.product.stock > 0 ? Icons.check_circle_outline : Icons.error_outline,
                  color: widget.product.stock > 0 ? Colors.green : Colors.red,
                  size: 18,
                ),
                SizedBox(width: 8),
                Text(
                  widget.product.stock > 0
                      ? '${widget.product.stock} items available'
                      : 'Out of stock',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: widget.product.stock > 0 ? Colors.green.shade700 : Colors.red.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (widget.product.stock > 0 && widget.product.stock <= 5) ...[
                  SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Low Stock',
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 12),
          Text(
            'This exquisite ${widget.product.name} is handcrafted with premium quality materials. Each piece is carefully designed to bring divine blessings to your home. The intricate details and traditional craftsmanship make this murti a perfect addition to your prayer room or home temple.',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecifications() {
    // Filter out empty values and additionalInfo
    final specs = widget.product.specifications.entries
        .where((entry) =>
            entry.key != 'additionalInfo' &&
            entry.value != null &&
            entry.value.toString().trim().isNotEmpty)
        .toList();

    if (specs.isEmpty) {
      return SizedBox.shrink(); // Don't show section if no specs
    }

    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Specifications',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 12),
          ...specs.map((spec) => _buildSpecRow(
            _formatSpecLabel(spec.key),
            _formatSpecValue(spec.value),
          )),
        ],
      ),
    );
  }

  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
            ),
          ),
          Text(
            ': ',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviews() {
    final reviewCount = _reviews?.totalReviews ?? widget.product.reviewCount;

    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Reviews ($reviewCount)',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              if (reviewCount > 0)
                TextButton(
                  onPressed: () {
                    // Navigate to all reviews
                  },
                  child: Text(
                    'View All',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemePrimaryColour,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 12),

          if (_isLoadingReviews)
            Center(
              child: CircularProgressIndicator(
                color: Colours.lightThemePrimaryColour,
              ),
            )
          else if (_reviews != null && _reviews!.reviews.isNotEmpty)
            ..._reviews!.reviews.take(3).map((review) => _buildReviewItem(review))
          else
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: CoreUtils.adaptiveColour(
                  context,
                  lightModeColour: Colours.lightThemeTintStockColour,
                  darkModeColour: Colours.darkThemeDarkSharpColor,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'No reviews yet. Be the first to review this product!',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(ReviewModel review) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeTintStockColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                review.userName,
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (review.isVerifiedPurchase) ...[
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Verified',
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
              Spacer(),
              Text(
                review.timeAgo,
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Row(
            children: [
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < review.rating ? Icons.star : Icons.star_outline,
                    color: Colours.lightThemeYellowTint,
                    size: 16,
                  );
                }),
              ),
              SizedBox(width: 8),
              Text(
                '${review.rating}/5',
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          if (review.title != null && review.title!.isNotEmpty) ...[
            SizedBox(height: 8),
            Text(
              review.title!,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          SizedBox(height: 8),
          Text(
            review.comment,
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedProducts() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Related Products',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 12),
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _relatedProducts.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 160,
                  margin: EdgeInsets.only(right: 12),
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Related Product ${index + 1}'),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Quantity Selector
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colours.lightThemeStockColour,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: _quantity > 1 ? () {
                      setState(() {
                        _quantity--;
                      });
                    } : null,
                    icon: Icon(Icons.remove),
                    constraints: BoxConstraints(minWidth: 40, minHeight: 40),
                  ),
                  Container(
                    width: 40,
                    alignment: Alignment.center,
                    child: Text(
                      '$_quantity',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _quantity < widget.product.stock ? () {
                      setState(() {
                        _quantity++;
                      });
                    } : null,
                    icon: Icon(Icons.add),
                    constraints: BoxConstraints(minWidth: 40, minHeight: 40),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),

            // Add to Cart Button
            Expanded(
              child: ElevatedButton(
                onPressed: widget.product.isInStock ? _addToCart : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.product.isInStock
                      ? Colours.lightThemePrimaryColour
                      : Colours.lightThemeSecondaryTextColour,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        widget.product.isInStock ? 'Add to Cart' : 'Out of Stock',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Format specification label for display
  String _formatSpecLabel(String key) {
    // Handle special cases first
    switch (key.toLowerCase()) {
      case 'careinstructions':
        return 'Care Instructions';
      case 'additionalinfo':
        return 'Additional Info';
      case 'material':
        return 'Material';
      case 'height':
        return 'Height';
      case 'width':
        return 'Width';
      case 'weight':
        return 'Weight';
      case 'finish':
        return 'Finish';
      case 'origin':
        return 'Origin';
      case 'color':
        return 'Color';
      case 'style':
        return 'Style';
      case 'occasion':
        return 'Occasion';
      default:
        // Convert camelCase to Title Case
        final formatted = key.replaceAllMapped(
          RegExp(r'([A-Z])'),
          (match) => ' ${match.group(1)}',
        );

        // Capitalize first letter of each word
        final words = formatted.split(' ');
        return words.map((word) {
          if (word.isEmpty) return word;
          return word[0].toUpperCase() + word.substring(1).toLowerCase();
        }).join(' ').trim();
    }
  }

  /// Format specification value for display
  String _formatSpecValue(dynamic value) {
    if (value == null) return 'N/A';

    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return 'N/A';

    // Capitalize first letter for better presentation
    return stringValue[0].toUpperCase() + stringValue.substring(1);
  }
}
