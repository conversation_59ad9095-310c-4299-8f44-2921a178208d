import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/add_address_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/edit_address_view.dart';

class AddressesView extends StatefulWidget {
  const AddressesView({super.key});

  static const path = '/profile/addresses';

  @override
  State<AddressesView> createState() => _AddressesViewState();
}

class _AddressesViewState extends State<AddressesView> {
  bool _isLoading = true;
  List<AddressModel> _addresses = [];
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    Log.ui('Loading user addresses', screen: 'AddressesView');

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final response = await ApiService.instance.user.getAddresses();

      if (response.isSuccess && response.data != null && mounted) {
        // Convert API response to AddressModel list
        final addressList = response.data!
            .map((addressData) => AddressModel.fromJson(addressData))
            .toList();

        Log.ui('Addresses loaded successfully', screen: 'AddressesView', context: {
          'count': addressList.length,
        });

        setState(() {
          _addresses = addressList;
          _isLoading = false;
        });
      } else if (mounted) {
        Log.ui('Failed to load addresses', screen: 'AddressesView', context: {
          'error': response.error,
        });

        setState(() {
          _isLoading = false;
          _errorMessage = response.error ?? 'Failed to load addresses';
        });
      }
    } catch (e, stackTrace) {
      Log.e('Error loading addresses', tag: 'ADDRESSES_VIEW', error: e, stackTrace: stackTrace);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load addresses. Please try again.';
        });
      }
    }
  }

  void _addNewAddress() async {
    final result = await context.push(AddAddressView.path);
    if (result == true) {
      // Refresh the address list
      _loadAddresses();
    }
  }

  void _editAddress(AddressModel address) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditAddressView(address: address),
      ),
    );

    if (result == true) {
      // Refresh the address list
      _loadAddresses();
    }
  }

  void _deleteAddress(AddressModel address) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Address'),
        content: Text('Are you sure you want to delete this address?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              // Store ScaffoldMessenger reference before async operation
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              try {
                Log.ui('Deleting address', screen: 'AddressesView', context: {
                  'addressId': address.id,
                });

                final response = await ApiService.instance.user.deleteAddress(address.id ?? '');

                if (mounted) {
                  if (response.isSuccess) {
                    // Reload addresses after successful deletion
                    _loadAddresses();

                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('Address deleted successfully'),
                        backgroundColor: Colours.lightThemePrimaryColour,
                      ),
                    );
                  } else {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(response.error ?? 'Failed to delete address'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } catch (e) {
                // Log error without context dependency
                print('Error deleting address: $e');

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete address. Please try again.'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _setDefaultAddress(AddressModel address) async {
    try {
      Log.ui('Setting default address', screen: 'AddressesView', context: {
        'addressId': address.id,
      });

      final response = await ApiService.instance.user.setDefaultAddress(address.id ?? '');

      if (response.isSuccess && mounted) {
        // Refresh the address list to get updated default status
        _loadAddresses();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Default address updated'),
            backgroundColor: Colours.lightThemePrimaryColour,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Failed to update default address'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      Log.e('Error setting default address', tag: 'ADDRESSES_VIEW', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update default address. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("My Addresses", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _addNewAddress,
            icon: Icon(Icons.add),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadAddresses,
        child: _isLoading
            ? LoadingWidget(
                message: 'Loading addresses...',
                size: 100,
              )
            : _errorMessage.isNotEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 60,
                          color: Colors.red,
                        ),
                        SizedBox(height: 16),
                        Text(
                          _errorMessage,
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadAddresses,
                          child: Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : _addresses.isEmpty
                    ? EmptyStateWidget(
                        lottieAsset: Media.search,
                        title: 'No addresses found',
                        description: 'Add your first address to get started',
                        buttonText: 'Add Address',
                        onButtonPressed: _addNewAddress,
                      )
                    : Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        padding: ResponsiveUtils.getResponsivePadding(context),
                        itemCount: _addresses.length,
                        itemBuilder: (context, index) {
                          final address = _addresses[index];
                          return _buildAddressCard(address);
                        },
                      ),
                    ),
                    
                    // Add New Address Button
                    Container(
                      padding: ResponsiveUtils.getResponsivePadding(context),
                      child: CustomButton(
                        onPressed: _addNewAddress,
                        text: 'Add New Address',
                        icon: Icon(Icons.add, color: Colors.white),
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  Widget _buildAddressCard(AddressModel address) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: address.isDefault 
              ? Colours.lightThemePrimaryColour 
              : Colours.lightThemeStockColour,
          width: address.isDefault ? 1 : 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getAddressTypeColor(address.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getAddressTypeIcon(address.type),
                      size: 16,
                      color: _getAddressTypeColor(address.type),
                    ),
                    SizedBox(width: 4),
                    Text(
                      _getAddressTypeText(address.type),
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: _getAddressTypeColor(address.type),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              
              if (address.isDefault) ...[
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Default',
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colours.lightThemePrimaryColour,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
              
              Spacer(),
              
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _editAddress(address);
                      break;
                    case 'default':
                      _setDefaultAddress(address);
                      break;
                    case 'delete':
                      _deleteAddress(address);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  if (!address.isDefault)
                    PopupMenuItem(
                      value: 'default',
                      child: Row(
                        children: [
                          Icon(Icons.star, size: 16),
                          SizedBox(width: 8),
                          Text('Set as Default'),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          SizedBox(height: 12),
          
          // Name and Phone
          Text(
            address.name ?? 'No Name',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 4),
          Text(
            address.phone ?? 'No Phone',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),

          SizedBox(height: 8),

          // Address
          Text(
            address.addressLine1,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          if (address.addressLine2 != null && address.addressLine2!.isNotEmpty) ...[
            Text(
              address.addressLine2!,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
          ],
          Text(
            '${address.city}, ${address.state} - ${address.postalCode}',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          Text(
            address.country,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
        ],
      ),
    );
  }

  Color _getAddressTypeColor(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return Colors.green;
      case 'office':
        return Colors.blue;
      case 'other':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getAddressTypeIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return Icons.home;
      case 'office':
        return Icons.business;
      case 'other':
        return Icons.location_on;
      default:
        return Icons.location_on;
    }
  }

  String _getAddressTypeText(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return 'Home';
      case 'office':
        return 'Office';
      case 'other':
        return 'Other';
      default:
        return 'Address';
    }
  }
}
