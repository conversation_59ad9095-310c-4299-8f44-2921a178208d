import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';

class AboutView extends StatefulWidget {
  const AboutView({super.key});

  static const path = '/profile/about';

  @override
  State<AboutView> createState() => _AboutViewState();
}

class _AboutViewState extends State<AboutView> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAboutData();
  }

  Future<void> _loadAboutData() async {
    // Simulate loading about data
    await Future.delayed(Duration(seconds: 1));
    
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("About", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading about information...',
              size: 100,
            )
          : SingleChildScrollView(
              padding: ResponsiveUtils.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // App Logo and Name
                  Center(
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            Icons.temple_hindu,
                            size: 60,
                            color: Colours.lightThemePrimaryColour,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Ghanshyam Murti Bhandar',
                          style: TextStyles.headingSemiBold.copyWith(
                            color: Colours.classAdaptiveTextColour(context),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Version 1.0.0',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                  
                  // About Us Section
                  _buildSectionCard(
                    title: 'About Us',
                    content: 'Ghanshyam Murti Bhandar has been serving devotees for over 25 years, providing authentic and beautifully crafted religious idols and artifacts. Our commitment to quality and devotion has made us a trusted name in the spiritual community.\n\nWe specialize in handcrafted murtis of various Hindu deities, made by skilled artisans using traditional techniques passed down through generations. Each piece is created with love, devotion, and attention to detail.',
                    icon: Icons.info_outline,
                  ),
                  
                  SizedBox(height: 16),
                  
                  // Our Mission Section
                  _buildSectionCard(
                    title: 'Our Mission',
                    content: 'To bring divine blessings to every home by providing authentic, high-quality religious artifacts that inspire devotion and spiritual growth. We aim to preserve traditional craftsmanship while making it accessible to devotees worldwide.',
                    icon: Icons.flag_outlined,
                  ),
                  
                  SizedBox(height: 16),
                  
                  // Our Values Section
                  _buildSectionCard(
                    title: 'Our Values',
                    content: '• Authenticity: Every product is genuine and crafted with traditional methods\n• Quality: We maintain the highest standards in materials and craftsmanship\n• Devotion: Our work is guided by spiritual values and respect for traditions\n• Customer Service: We are committed to serving our customers with dedication\n• Sustainability: We support eco-friendly practices and fair trade',
                    icon: Icons.favorite_outline,
                  ),
                  
                  SizedBox(height: 16),
                  
                  // Contact Information Section
                  _buildSectionCard(
                    title: 'Contact Information',
                    content: 'Address: 123, Temple Street, Spiritual District, Mumbai - 400001\n\nPhone: +91 98765 43210\nEmail: <EMAIL>\nWebsite: www.ghanshyammurtibhandar.com\n\nBusiness Hours:\nMonday - Saturday: 9:00 AM - 6:00 PM\nSunday: 10:00 AM - 4:00 PM',
                    icon: Icons.contact_mail_outlined,
                  ),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                  
                  // Legal Links Section
                  _buildLegalLinksSection(),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                  
                  // Social Media Section
                  _buildSocialMediaSection(),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                  
                  // Copyright
                  Center(
                    child: Column(
                      children: [
                        Text(
                          '© 2024 Ghanshyam Murti Bhandar',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'All rights reserved',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Colours.lightThemePrimaryColour,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                title,
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Text(
            content,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegalLinksSection() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colours.lightThemeSecondaryColour.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.gavel,
                  color: Colours.lightThemeSecondaryColour,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Legal',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          
          _buildLegalLink('Terms of Service', () {
            _showLegalDialog('Terms of Service', 'Terms of Service content will be displayed here.');
          }),
          
          SizedBox(height: 12),
          
          _buildLegalLink('Privacy Policy', () {
            _showLegalDialog('Privacy Policy', 'Privacy Policy content will be displayed here.');
          }),
          
          SizedBox(height: 12),
          
          _buildLegalLink('Return & Refund Policy', () {
            _showLegalDialog('Return & Refund Policy', 'Return & Refund Policy content will be displayed here.');
          }),
          
          SizedBox(height: 12),
          
          _buildLegalLink('Shipping Policy', () {
            _showLegalDialog('Shipping Policy', 'Shipping Policy content will be displayed here.');
          }),
        ],
      ),
    );
  }

  Widget _buildLegalLink(String title, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemePrimaryColour,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colours.lightThemePrimaryColour,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialMediaSection() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.share,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Follow Us',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSocialButton(
                icon: Icons.facebook,
                label: 'Facebook',
                color: Color(0xFF1877F2),
                onTap: () => _openSocialMedia('Facebook'),
              ),
              _buildSocialButton(
                icon: Icons.camera_alt,
                label: 'Instagram',
                color: Color(0xFFE4405F),
                onTap: () => _openSocialMedia('Instagram'),
              ),
              _buildSocialButton(
                icon: Icons.alternate_email,
                label: 'Twitter',
                color: Color(0xFF1DA1F2),
                onTap: () => _openSocialMedia('Twitter'),
              ),
              _buildSocialButton(
                icon: Icons.video_library,
                label: 'YouTube',
                color: Color(0xFFFF0000),
                onTap: () => _openSocialMedia('YouTube'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              label,
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLegalDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _openSocialMedia(String platform) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening $platform...'),
        backgroundColor: Colours.lightThemePrimaryColour,
      ),
    );
  }
}
