import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/order_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';
import 'package:ghanshyam_murti_bhandar/src/orders/presentation/views/order_details_view.dart' as detailed;

class MyOrdersView extends StatefulWidget {
  const MyOrdersView({super.key});

  static const path = '/profile/orders';

  @override
  State<MyOrdersView> createState() => _MyOrdersViewState();
}

class _MyOrdersViewState extends State<MyOrdersView> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _error;

  final List<OrderModel> _allOrders = [];
  final List<OrderModel> _activeOrders = [];
  final List<OrderModel> _completedOrders = [];
  final List<OrderModel> _cancelledOrders = [];

  // Pagination
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  Future<void> _loadOrders({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _allOrders.clear();
      _activeOrders.clear();
      _completedOrders.clear();
      _cancelledOrders.clear();
    }

    if (!_hasMoreData && !isRefresh) return;

    try {
      setState(() {
        if (isRefresh) {
          _isLoading = true;
          _error = null;
        } else {
          _isLoadingMore = true;
        }
      });

      final response = await ApiService.instance.orders.getMyOrders(
        page: _currentPage,
        limit: 20,
      );

      if (response.isSuccess && response.data != null) {
        final orderListResponse = response.data!;
        final newOrders = orderListResponse.orders;

        if (mounted) {
          setState(() {
            if (isRefresh) {
              _allOrders.clear();
              _activeOrders.clear();
              _completedOrders.clear();
              _cancelledOrders.clear();
            }

            _allOrders.addAll(newOrders);

            // Categorize orders by status
            for (var order in newOrders) {
              final status = order.status.toLowerCase();
              switch (status) {
                case 'cancelled':
                case 'refunded':
                  _cancelledOrders.add(order);
                  break;
                case 'delivered':
                  // Completed means delivered
                  _completedOrders.add(order);
                  break;
                default:
                  // All other statuses (pending, processing, shipped, etc.) are considered active
                  _activeOrders.add(order);
                  break;
              }
            }

            _currentPage++;
            _hasMoreData = newOrders.length >= 20;
            _isLoading = false;
            _isLoadingMore = false;
            _error = null;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _error = response.error ?? 'Failed to load orders';
            _isLoading = false;
            _isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error loading orders: $e';
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    }
  }



  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("My Orders", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading orders...',
              size: 100,
            )
          : _error != null
              ? _buildErrorState()
              : Column(
              children: [
                // Tab Bar
                Container(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: Colours.lightThemePrimaryColour,
                    unselectedLabelColor: Colours.lightThemeSecondaryTextColour,
                    indicatorColor: Colours.lightThemePrimaryColour,
                    labelStyle: TextStyles.paragraphSubTextRegular1.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: TextStyles.paragraphSubTextRegular1,
                    tabs: [
                      Tab(text: 'All (${_allOrders.length})'),
                      Tab(text: 'Active (${_activeOrders.length})'),
                      Tab(text: 'Delivered (${_completedOrders.length})'),
                      Tab(text: 'Cancelled (${_cancelledOrders.length})'),
                    ],
                  ),
                ),
                
                // Tab Views
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOrdersList(_allOrders),
                      _buildOrdersList(_activeOrders),
                      _buildOrdersList(_completedOrders),
                      _buildOrdersList(_cancelledOrders),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildOrdersList(List<OrderModel> orders) {
    if (orders.isEmpty) {
      return EmptyStateWidget(
        lottieAsset: Media.emptyCart,
        title: 'No orders found',
        description: 'You haven\'t placed any orders yet',
        buttonText: 'Start Shopping',
        onButtonPressed: () {
          Navigator.pop(context);
        },
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadOrders(isRefresh: true),
      child: ListView.builder(
        padding: ResponsiveUtils.getResponsivePadding(context),
        itemCount: orders.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == orders.length) {
            return _buildLoadingMoreIndicator();
          }

          final order = orders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Order #${order.orderNumber.isNotEmpty ? order.orderNumber : order.id}',
                  style: TextStyles.headingSemiBold1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8),
              _buildStatusChip(order.status),
            ],
          ),
          SizedBox(height: 8),
          
          Text(
            'Ordered on ${_formatDate(order.createdAt)}',
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),

          if (order.isDelivered && order.updatedAt != null) ...[
            Text(
              'Delivered on ${_formatDate(order.updatedAt!)}',
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colors.green,
              ),
            ),
          ],
          
          SizedBox(height: 12),
          
          // Order Items
          ...order.items.map((item) => Padding(
            padding: EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${item.quantity}x ${item.product.name}',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.classAdaptiveTextColour(context),
                    ),
                  ),
                ),
                Text(
                  '₹${item.price * item.quantity}',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          )),
          
          Divider(height: 24),
          
          // Order Total and Actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total: ₹${order.total}',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.lightThemePrimaryColour,
                ),
              ),
              Row(
                children: [
                  if (!order.isDelivered && !order.isCancelled) ...[
                    TextButton(
                      onPressed: () {
                        _showTrackingDialog(order);
                      },
                      child: Text(
                        'Track Order',
                        style: TextStyle(color: Colours.lightThemePrimaryColour),
                      ),
                    ),
                  ],
                  TextButton(
                    onPressed: () {
                      _showOrderDetails(order);
                    },
                    child: Text(
                      'View Details',
                      style: TextStyle(color: Colours.lightThemePrimaryColour),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    debugPrint('🏷️ Building status chip for status: "$status"');

    Color backgroundColor;
    Color textColor;
    String text;

    switch (status.toLowerCase()) {
      case 'pending':
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        text = 'Pending';
        break;
      case 'confirmed':
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        text = 'Confirmed';
        break;
      case 'processing':
        backgroundColor = Colors.purple.withValues(alpha: 0.1);
        textColor = Colors.purple;
        text = 'Processing';
        break;
      case 'shipped':
        backgroundColor = Colors.indigo.withValues(alpha: 0.1);
        textColor = Colors.indigo;
        text = 'Shipped';
        break;
      case 'delivered':
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        text = 'Delivered';
        break;
      case 'cancelled':
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        text = 'Cancelled';
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        text = status.toUpperCase();
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: textColor.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        text,
        style: TextStyles.paragraphSubTextRegular2.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildOrderTimeline(OrderModel order) {
    final status = order.status.toLowerCase();

    // Define the order timeline steps
    final timelineSteps = [
      {'status': 'pending', 'title': 'Order Placed', 'description': 'Your order has been placed'},
      {'status': 'confirmed', 'title': 'Order Confirmed', 'description': 'Your order has been confirmed'},
      {'status': 'processing', 'title': 'Processing', 'description': 'Your order is being prepared'},
      {'status': 'shipped', 'title': 'Shipped', 'description': 'Your order has been shipped'},
      {'status': 'delivered', 'title': 'Delivered', 'description': 'Your order has been delivered'},
    ];

    // Find current step index
    int currentStepIndex = 0;
    for (int i = 0; i < timelineSteps.length; i++) {
      if (timelineSteps[i]['status'] == status) {
        currentStepIndex = i;
        break;
      }
    }

    return Column(
      children: timelineSteps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isCompleted = index <= currentStepIndex;
        final isCurrent = index == currentStepIndex;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline indicator
            Column(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCompleted
                        ? Colours.lightThemePrimaryColour
                        : Colors.grey.withValues(alpha: 0.3),
                    border: Border.all(
                      color: isCompleted
                          ? Colours.lightThemePrimaryColour
                          : Colors.grey,
                      width: 2,
                    ),
                  ),
                  child: isCompleted
                      ? Icon(
                          Icons.check,
                          size: 12,
                          color: Colors.white,
                        )
                      : null,
                ),
                if (index < timelineSteps.length - 1)
                  Container(
                    width: 2,
                    height: 30,
                    color: isCompleted
                        ? Colours.lightThemePrimaryColour
                        : Colors.grey.withValues(alpha: 0.3),
                  ),
              ],
            ),
            SizedBox(width: 12),

            // Timeline content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    step['title']!,
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w500,
                      color: isCompleted
                          ? Colours.classAdaptiveTextColour(context)
                          : Colors.grey,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    step['description']!,
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: isCompleted
                          ? Colours.lightThemeSecondaryTextColour
                          : Colors.grey,
                    ),
                  ),
                  if (isCurrent && step['status'] == 'pending')
                    Padding(
                      padding: EdgeInsets.only(top: 4),
                      child: Text(
                        _formatDate(order.createdAt),
                        style: TextStyles.paragraphSubTextRegular2.copyWith(
                          color: Colours.lightThemePrimaryColour,
                          fontSize: 11,
                        ),
                      ),
                    ),
                  SizedBox(height: index < timelineSteps.length - 1 ? 16 : 0),
                ],
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  void _showTrackingDialog(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Track Order #${order.orderNumber.isNotEmpty ? order.orderNumber : order.id}',
          style: TextStyles.headingSemiBold1.copyWith(fontSize: 16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Status
            Row(
              children: [
                Text(
                  'Current Status: ',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                _buildStatusChip(order.status),
              ],
            ),
            SizedBox(height: 16),

            // Order Timeline
            Text(
              'Order Timeline:',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 12),

            _buildOrderTimeline(order),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(color: Colours.lightThemePrimaryColour),
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderDetails(OrderModel order) {
    // Convert OrderModel to detailed order model
    final detailedOrderItems = order.items.map((item) => detailed.OrderItem(
      id: item.id ?? 'unknown',
      name: item.product.name,
      image: item.product.images.isNotEmpty ? item.product.images.first : '',
      price: item.price,
      quantity: item.quantity,
      category: item.product.categoryName,
    )).toList();

    final detailedOrder = detailed.Order(
      id: order.id,
      status: order.status,
      orderDate: order.createdAt,
      deliveryDate: order.isDelivered ? order.updatedAt : null,
      items: detailedOrderItems,
      subtotal: order.subtotal,
      shippingCost: order.shipping,
      tax: order.tax,
      total: order.total,
      shippingAddress: _getFormattedAddress(order.shippingAddress),
      paymentMethod: order.paymentMethod ?? 'Unknown',
      trackingNumber: order.orderNumber,
    );

    // Navigate to detailed order page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => detailed.OrderDetailsView(order: detailedOrder),
      ),
    );
  }



  String _getFormattedAddress(AddressModel address) {
    final parts = <String>[];
    parts.add(address.addressLine1);
    if (address.addressLine2 != null && address.addressLine2!.isNotEmpty) {
      parts.add(address.addressLine2!);
    }
    parts.add('${address.city}, ${address.state} - ${address.postalCode}');
    parts.add(address.country);
    return parts.join('\n');
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          SizedBox(height: 16),
          Text(
            'Error Loading Orders',
            style: TextStyles.headingSemiBold1,
          ),
          SizedBox(height: 8),
          Text(
            _error ?? 'Something went wrong',
            style: TextStyles.paragraphSubTextRegular1,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _loadOrders(isRefresh: true),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(16),
      alignment: Alignment.center,
      child: CircularProgressIndicator(),
    );
  }
}

// Data Models
class Order {
  final String id;
  final List<OrderItem> items;
  final OrderStatus status;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final double total;

  Order({
    required this.id,
    required this.items,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    required this.total,
  });
}

class OrderItem {
  final String name;
  final int quantity;
  final double price;

  OrderItem({
    required this.name,
    required this.quantity,
    required this.price,
  });
}

enum OrderStatus {
  processing,
  shipped,
  delivered,
  cancelled,
}
