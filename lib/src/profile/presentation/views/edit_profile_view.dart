import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';

class EditProfileView extends StatefulWidget {
  const EditProfileView({super.key});

  static const path = '/profile/edit';

  @override
  State<EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<EditProfileView> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  
  bool _isLoading = false;
  bool _isLoadingData = true;
  String _selectedGender = 'Male';
  final List<String> _genderOptions = ['Male', 'Female', 'Other'];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    Log.ui('Loading user profile data', screen: 'EditProfileView');

    try {
      // Get current user from storage first (faster)
      final currentUser = await ApiService.instance.user.getCurrentUser();

      if (currentUser != null && mounted) {
        setState(() {
          // Split name into first and last name
          final nameParts = currentUser.name.split(' ');
          _firstNameController.text = nameParts.isNotEmpty ? nameParts.first : '';
          _lastNameController.text = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

          _emailController.text = currentUser.email;
          _phoneController.text = currentUser.phone ?? '';
          _dateOfBirthController.text = currentUser.dateOfBirth ?? '';
          _selectedGender = currentUser.gender ?? 'Male';
        });
      }

      // Then fetch fresh data from API
      final response = await ApiService.instance.auth.getProfile();

      if (response.isSuccess && response.data != null && mounted) {
        final user = response.data!;

        Log.ui('User profile loaded successfully', screen: 'EditProfileView', context: {
          'userId': user.id,
          'userName': user.name,
          'userEmail': user.email,
        });

        setState(() {
          // Split name into first and last name
          final nameParts = user.name.split(' ');
          _firstNameController.text = nameParts.isNotEmpty ? nameParts.first : '';
          _lastNameController.text = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

          _emailController.text = user.email;
          _phoneController.text = user.phone ?? '';
          _dateOfBirthController.text = user.dateOfBirth ?? '';
          _selectedGender = user.gender ?? 'Male';
          _isLoadingData = false;
        });
      } else {
        Log.ui('Failed to load user profile', screen: 'EditProfileView', context: {
          'error': response.error,
          'statusCode': response.statusCode,
        });

        if (mounted) {
          setState(() {
            _isLoadingData = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to load profile data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      Log.e('Error loading user profile', tag: 'EDIT_PROFILE_VIEW', error: e, stackTrace: stackTrace);

      if (mounted) {
        setState(() {
          _isLoadingData = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load profile data. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime(1990, 8, 15),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colours.lightThemePrimaryColour,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        _dateOfBirthController.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    Log.ui('Starting profile update', screen: 'EditProfileView');

    setState(() {
      _isLoading = true;
    });

    try {
      // Combine first and last name
      final fullName = '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}'.trim();

      // Call the update profile API
      final response = await ApiService.instance.auth.updateProfile(
        name: fullName.isNotEmpty ? fullName : null,
        email: _emailController.text.trim().isNotEmpty ? _emailController.text.trim() : null,
        phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
        dateOfBirth: _dateOfBirthController.text.trim().isNotEmpty ? _dateOfBirthController.text.trim() : null,
        gender: _selectedGender,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          Log.ui('Profile updated successfully', screen: 'EditProfileView', context: {
            'userId': response.data?.id,
            'userName': response.data?.name,
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message.isNotEmpty ? response.message : 'Profile updated successfully!'),
              backgroundColor: Colours.lightThemePrimaryColour,
              behavior: SnackBarBehavior.floating,
            ),
          );

          Navigator.pop(context);
        } else {
          Log.ui('Profile update failed', screen: 'EditProfileView', context: {
            'error': response.error,
            'statusCode': response.statusCode,
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to update profile. Please try again.'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      Log.e('Profile update exception', tag: 'EDIT_PROFILE_VIEW', error: e, stackTrace: stackTrace);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update profile. Please check your connection and try again.'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _dateOfBirthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Edit Profile", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoadingData
          ? LoadingWidget(
              message: 'Loading profile...',
              size: 100,
            )
          : SingleChildScrollView(
              padding: ResponsiveUtils.getResponsivePadding(context),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Picture Section - Commented out as not needed
                    // Center(
                    //   child: Column(
                    //     children: [
                    //       Stack(
                    //         children: [
                    //           CircleAvatar(
                    //             radius: 60,
                    //             backgroundColor: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                    //             child: Icon(
                    //               Icons.person,
                    //               size: 60,
                    //               color: Colours.lightThemePrimaryColour,
                    //             ),
                    //           ),
                    //           Positioned(
                    //             bottom: 0,
                    //             right: 0,
                    //             child: Container(
                    //               decoration: BoxDecoration(
                    //                 color: Colours.lightThemePrimaryColour,
                    //                 shape: BoxShape.circle,
                    //                 border: Border.all(
                    //                   color: Colors.white,
                    //                   width: 2,
                    //                 ),
                    //               ),
                    //               child: IconButton(
                    //                 onPressed: () {
                    //                   // Handle image selection
                    //                   ScaffoldMessenger.of(context).showSnackBar(
                    //                     SnackBar(
                    //                       content: Text('Image selection coming soon!'),
                    //                       backgroundColor: Colours.lightThemePrimaryColour,
                    //                     ),
                    //                   );
                    //                 },
                    //                 icon: Icon(
                    //                   Icons.camera_alt,
                    //                   color: Colors.white,
                    //                   size: 20,
                    //                 ),
                    //                 constraints: BoxConstraints(
                    //                   minWidth: 36,
                    //                   minHeight: 36,
                    //                 ),
                    //                 padding: EdgeInsets.zero,
                    //               ),
                    //             ),
                    //           ),
                    //         ],
                    //       ),
                    //       SizedBox(height: 8),
                    //       Text(
                    //         'Tap to change profile picture',
                    //         style: TextStyles.paragraphSubTextRegular1.copyWith(
                    //           color: Colours.lightThemeSecondaryTextColour,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                    
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                    
                    // Personal Information Section
                    Text(
                      'Personal Information',
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // First Name
                    CustomTextField(
                      controller: _firstNameController,
                      labelText: 'First Name',
                      hintText: 'Enter your first name',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'First name is required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // Last Name
                    CustomTextField(
                      controller: _lastNameController,
                      labelText: 'Last Name',
                      hintText: 'Enter your last name',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Last name is required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // Email
                    CustomTextField(
                      controller: _emailController,
                      labelText: 'Email Address',
                      hintText: 'Enter your email address',
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Email is required';
                        }
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                          return 'Please enter a valid email address';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // Phone Number
                    CustomTextField(
                      controller: _phoneController,
                      labelText: 'Phone Number',
                      hintText: 'Enter your phone number',
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Phone number is required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // Date of Birth
                    CustomTextField(
                      controller: _dateOfBirthController,
                      labelText: 'Date of Birth',
                      hintText: 'Select your date of birth',
                      readOnly: true,
                      onTap: _selectDate,
                      suffixIcon: Icon(
                        Icons.calendar_today,
                        color: Colours.lightThemePrimaryColour,
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                    
                    // Gender Selection
                    Text(
                      'Gender',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colours.lightThemeStockColour),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedGender,
                          isExpanded: true,
                          items: _genderOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _selectedGender = newValue;
                              });
                            }
                          },
                        ),
                      ),
                    ),
                    
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
                    
                    // Save Button
                    CustomButton(
                      onPressed: _saveProfile,
                      text: 'Save Changes',
                      isLoading: _isLoading,
                    ),
                    
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                  ],
                ),
              ),
            ),
    );
  }
}
