import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/edit_profile_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/change_password_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/my_orders_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/addresses_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/payment_methods_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/notifications_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/help_support_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/about_view.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/settings_view.dart';

class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  static const path = '/profile';

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileView> {
  UserModel? _currentUser;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    Log.ui('Loading user profile data', screen: 'ProfileView');

    try {
      // Get current user from storage first (faster)
      final currentUser = await ApiService.instance.user.getCurrentUser();

      if (currentUser != null && mounted) {
        setState(() {
          _currentUser = currentUser;
          _isLoading = false;
        });
      }

      // Then fetch fresh data from API
      final response = await ApiService.instance.auth.getProfile();

      if (response.isSuccess && response.data != null && mounted) {
        Log.ui(
          'User profile loaded successfully',
          screen: 'ProfileView',
          context: {
            'userId': response.data!.id,
            'userName': response.data!.name,
          },
        );

        setState(() {
          _currentUser = response.data;
          _isLoading = false;
          _errorMessage = '';
        });
      } else if (mounted) {
        Log.ui(
          'Failed to load user profile',
          screen: 'ProfileView',
          context: {'error': response.error},
        );

        setState(() {
          _isLoading = false;
          _errorMessage = response.error ?? 'Failed to load profile';
        });
      }
    } catch (e, stackTrace) {
      Log.e(
        'Error loading user profile',
        tag: 'PROFILE_VIEW',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load profile. Please try again.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Profile", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: _loadUserData,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          physics: AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Profile Header
              Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colours.lightThemeStockColour,
                    width: 0.5,
                  ),
                ),
                child:
                    _isLoading
                        ? const LoadingWidget(
                          message: 'Loading profile...',
                          size: 60,
                        )
                        : _errorMessage.isNotEmpty
                        ? Column(
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 60,
                              color: Colors.red,
                            ),
                            SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyles.paragraphSubTextRegular1
                                  .copyWith(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadUserData,
                              child: Text('Retry'),
                            ),
                          ],
                        )
                        : Column(
                          children: [
                            CircleAvatar(
                              radius: 50,
                              backgroundColor: Colours.lightThemePrimaryColour
                                  .withValues(alpha: 0.1),
                              child: Icon(
                                Icons.person,
                                size: 50,
                                color: Colours.lightThemePrimaryColour,
                              ),
                            ),
                            SizedBox(height: 16),
                            Text(
                              _currentUser?.name ?? 'Unknown User',
                              style: TextStyles.headingMedium3.copyWith(
                                color: Colours.classAdaptiveTextColour(context),
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              _currentUser?.email ?? 'No email',
                              style: TextStyles.paragraphSubTextRegular1
                                  .copyWith(
                                    color:
                                        Colours.lightThemeSecondaryTextColour,
                                  ),
                            ),
                            SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                _buildStatItem(context, 'Orders', '12'),
                                _buildStatItem(context, 'Favorites', '8'),
                                _buildStatItem(context, 'Reviews', '5'),
                              ],
                            ),
                          ],
                        ),
              ),
              SizedBox(height: 24),
              // Menu Items
              _buildMenuItem(
                context,
                icon: Icons.person_outline,
                title: 'Edit Profile',
                onTap: () {
                  context.push(EditProfileView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.lock_outline,
                title: 'Change Password',
                onTap: () {
                  context.push(ChangePasswordView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.shopping_bag_outlined,
                title: 'My Orders',
                onTap: () {
                  context.push(MyOrdersView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.location_on_outlined,
                title: 'Addresses',
                onTap: () {
                  context.push(AddressesView.path);
                },
              ),
              // _buildMenuItem(
              //   context,
              //   icon: Icons.payment_outlined,
              //   title: 'Payment Methods',
              //   onTap: () {
              //     context.push(PaymentMethodsView.path);
              //   },
              // ),
              // _buildMenuItem(
              //   context,
              //   icon: Icons.notifications_outlined,
              //   title: 'Notifications',
              //   onTap: () {
              //     context.push(NotificationsView.path);
              //   },
              // ),
              _buildMenuItem(
                context,
                icon: Icons.settings_outlined,
                title: 'Settings',
                onTap: () {
                  context.push(SettingsView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.help_outline,
                title: 'Help & Support',
                onTap: () {
                  context.push(HelpSupportView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.info_outline,
                title: 'About',
                onTap: () {
                  context.push(AboutView.path);
                },
              ),
              _buildMenuItem(
                context,
                icon: Icons.logout,
                title: 'Logout',
                isDestructive: true,
                onTap: () {
                  _showLogoutDialog(context);
                },
              ),
              SizedBox(height: 32),
              Text(
                'Version 1.0.0',
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyles.headingMedium3.copyWith(
            color: Colours.lightThemePrimaryColour,
          ),
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyles.paragraphSubTextRegular2.copyWith(
            color: Colours.lightThemeSecondaryTextColour,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colours.lightThemeStockColour, width: 1),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              isDestructive
                  ? Colors.red
                  : Colours.lightThemeSecondaryTextColour,
        ),
        title: Text(
          title,
          style: TextStyles.paragraphSubTextRegular1.copyWith(
            color:
                isDestructive
                    ? Colors.red
                    : Colours.classAdaptiveTextColour(context),
          ),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colours.lightThemeSecondaryTextColour,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Logout'),
            content: Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performLogout(context);
                },
                child: Text('Logout', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  Future<void> _performLogout(BuildContext context) async {
    // Store context references before async operations
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final goRouter = GoRouter.of(context);

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(child: CircularProgressIndicator()),
      );

      Log.ui('User initiated logout', screen: 'ProfileView');

      // Call logout API
      final response = await ApiService.instance.auth.logout();

      // Close loading dialog
      if (mounted) {
        navigator.pop();
      }

      if (response.isSuccess) {
        Log.ui(
          'Logout successful, redirecting to login',
          screen: 'ProfileView',
        );

        // Navigate to login screen and clear all previous routes
        if (mounted) {
          goRouter.go('/login');
        }
      } else {
        Log.ui(
          'Logout failed',
          screen: 'ProfileView',
          context: {'error': response.error},
        );

        // Even if API fails, still redirect to login since local data is cleared
        if (mounted) {
          goRouter.go('/login');
        }
      }
    } catch (e) {
      Log.e('Error during logout', tag: 'PROFILE_VIEW', error: e);

      // Close loading dialog if still open
      if (mounted) {
        navigator.pop();

        // Show error message but still redirect to login
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Logout completed. Please login again.'),
            backgroundColor: Colours.lightThemePrimaryColour,
          ),
        );

        // Redirect to login
        goRouter.go('/login');
      }
    }
  }
}
