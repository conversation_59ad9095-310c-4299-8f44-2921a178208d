import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';

class HelpSupportView extends StatefulWidget {
  const HelpSupportView({super.key});

  static const path = '/profile/help-support';

  @override
  State<HelpSupportView> createState() => _HelpSupportViewState();
}

class _HelpSupportViewState extends State<HelpSupportView> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  
  // Contact Form Controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;

  List<FAQItem> _faqs = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    // Simulate loading FAQs and other data
    await Future.delayed(Duration(seconds: 1));
    
    if (mounted) {
      setState(() {
        _faqs = [
          FAQItem(
            question: 'How do I place an order?',
            answer: 'To place an order, browse our products, add items to your cart, and proceed to checkout. You can pay using various payment methods including cards, UPI, and net banking.',
          ),
          FAQItem(
            question: 'What are the delivery charges?',
            answer: 'Delivery charges vary based on your location and order value. Orders above ₹500 qualify for free delivery within the city. For other locations, charges range from ₹50-₹150.',
          ),
          FAQItem(
            question: 'How long does delivery take?',
            answer: 'Standard delivery takes 3-5 business days. Express delivery (available in select cities) takes 1-2 business days. You will receive tracking information once your order is shipped.',
          ),
          FAQItem(
            question: 'Can I return or exchange products?',
            answer: 'Yes, we accept returns within 7 days of delivery for unused items in original packaging. Exchanges are available for size/color variations subject to availability.',
          ),
          FAQItem(
            question: 'What payment methods do you accept?',
            answer: 'We accept all major credit/debit cards, UPI payments, net banking, and popular digital wallets. Cash on delivery is available for orders below ₹2000.',
          ),
          FAQItem(
            question: 'How do I track my order?',
            answer: 'You can track your order from the "My Orders" section in your profile. You will also receive SMS and email updates with tracking information.',
          ),
          FAQItem(
            question: 'Do you offer customization services?',
            answer: 'Yes, we offer customization for select products. Please contact our support team with your requirements, and we will provide a quote and timeline.',
          ),
          FAQItem(
            question: 'What if I receive a damaged product?',
            answer: 'If you receive a damaged product, please contact us immediately with photos. We will arrange for a replacement or full refund as per your preference.',
          ),
        ];
        _isLoading = false;
      });
    }
  }

  Future<void> _submitContactForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    // Simulate form submission
    await Future.delayed(Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isSubmitting = false;
      });

      // Clear form
      _nameController.clear();
      _emailController.clear();
      _subjectController.clear();
      _messageController.clear();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Your message has been sent successfully! We will get back to you soon.'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Help & Support", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading help content...',
              size: 100,
            )
          : Column(
              children: [
                // Tab Bar
                Container(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: Colours.lightThemePrimaryColour,
                    unselectedLabelColor: Colours.lightThemeSecondaryTextColour,
                    indicatorColor: Colours.lightThemePrimaryColour,
                    labelStyle: TextStyles.paragraphSubTextRegular1.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: TextStyles.paragraphSubTextRegular1,
                    tabs: [
                      Tab(text: 'FAQ'),
                      Tab(text: 'Contact Us'),
                      Tab(text: 'Quick Help'),
                    ],
                  ),
                ),
                
                // Tab Views
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildFAQTab(),
                      _buildContactTab(),
                      _buildQuickHelpTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildFAQTab() {
    return ListView.builder(
      padding: ResponsiveUtils.getResponsivePadding(context),
      itemCount: _faqs.length,
      itemBuilder: (context, index) {
        final faq = _faqs[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colours.lightThemeWhiteColour,
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colours.lightThemeStockColour,
              width: 1,
            ),
          ),
          child: ExpansionTile(
            title: Text(
              faq.question,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Text(
                  faq.answer,
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContactTab() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact Information
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Get in Touch',
                    style: TextStyles.headingSemiBold1.copyWith(
                      color: Colours.lightThemePrimaryColour,
                    ),
                  ),
                  SizedBox(height: 8),
                  _buildContactInfo(Icons.phone, '+91 98765 43210'),
                  SizedBox(height: 4),
                  _buildContactInfo(Icons.email, '<EMAIL>'),
                  SizedBox(height: 4),
                  _buildContactInfo(Icons.access_time, 'Mon-Sat: 9:00 AM - 6:00 PM'),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
            
            // Contact Form
            Text(
              'Send us a Message',
              style: TextStyles.headingSemiBold1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            CustomTextField(
              controller: _nameController,
              labelText: 'Your Name',
              hintText: 'Enter your full name',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Name is required';
                }
                return null;
              },
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            CustomTextField(
              controller: _emailController,
              labelText: 'Email Address',
              hintText: 'Enter your email address',
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Email is required';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            CustomTextField(
              controller: _subjectController,
              labelText: 'Subject',
              hintText: 'Enter the subject of your inquiry',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Subject is required';
                }
                return null;
              },
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            CustomTextField(
              controller: _messageController,
              labelText: 'Message',
              hintText: 'Describe your issue or question in detail',
              maxLines: 5,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Message is required';
                }
                if (value.trim().length < 10) {
                  return 'Message should be at least 10 characters long';
                }
                return null;
              },
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
            
            CustomButton(
              onPressed: _submitContactForm,
              text: 'Send Message',
              isLoading: _isSubmitting,
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickHelpTab() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        children: [
          _buildQuickHelpCard(
            icon: Icons.chat,
            title: 'Live Chat',
            subtitle: 'Chat with our support team',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Live chat feature coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
          
          SizedBox(height: 16),
          
          _buildQuickHelpCard(
            icon: Icons.video_call,
            title: 'Video Call Support',
            subtitle: 'Schedule a video call with our experts',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Video call support coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
          
          SizedBox(height: 16),
          
          _buildQuickHelpCard(
            icon: Icons.book,
            title: 'User Guide',
            subtitle: 'Learn how to use our app',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('User guide coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
          
          SizedBox(height: 16),
          
          _buildQuickHelpCard(
            icon: Icons.bug_report,
            title: 'Report a Bug',
            subtitle: 'Found an issue? Let us know',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Bug reporting feature coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
          
          SizedBox(height: 16),
          
          _buildQuickHelpCard(
            icon: Icons.feedback,
            title: 'Send Feedback',
            subtitle: 'Share your thoughts and suggestions',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Feedback feature coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colours.lightThemePrimaryColour,
        ),
        SizedBox(width: 8),
        Text(
          text,
          style: TextStyles.paragraphSubTextRegular2.copyWith(
            color: Colours.lightThemePrimaryColour,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickHelpCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colours.lightThemeStockColour,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colours.lightThemePrimaryColour,
                size: 24,
              ),
            ),
            
            SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.classAdaptiveTextColour(context),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colours.lightThemeSecondaryTextColour,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ],
        ),
      ),
    );
  }
}

// Data Models
class FAQItem {
  final String question;
  final String answer;

  FAQItem({
    required this.question,
    required this.answer,
  });
}
