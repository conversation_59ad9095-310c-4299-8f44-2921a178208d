import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';

class EditAddressView extends StatefulWidget {
  final AddressModel address;
  
  const EditAddressView({
    super.key,
    required this.address,
  });

  static const path = '/profile/addresses/edit';

  @override
  State<EditAddressView> createState() => _EditAddressViewState();
}

class _EditAddressViewState extends State<EditAddressView> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  // Controllers
  late final TextEditingController _nameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _addressLine1Controller;
  late final TextEditingController _addressLine2Controller;
  late final TextEditingController _cityController;
  late final TextEditingController _stateController;
  late final TextEditingController _pincodeController;

  // Address type
  final List<String> _addressTypes = ['Home', 'Office', 'Other'];
  late String _selectedAddressType;
  late bool _isDefault;

  @override
  void initState() {
    super.initState();
    
    // Initialize controllers with existing address data
    _nameController = TextEditingController(text: widget.address.name ?? '');
    _phoneController = TextEditingController(text: widget.address.phone ?? '');
    _addressLine1Controller = TextEditingController(text: widget.address.addressLine1);
    _addressLine2Controller = TextEditingController(text: widget.address.addressLine2 ?? '');
    _cityController = TextEditingController(text: widget.address.city);
    _stateController = TextEditingController(text: widget.address.state);
    _pincodeController = TextEditingController(text: widget.address.postalCode);
    
    // Set address type and default status
    _selectedAddressType = widget.address.type?.toLowerCase() == 'home' ? 'Home' :
                          widget.address.type?.toLowerCase() == 'office' ? 'Office' : 'Other';
    _isDefault = widget.address.isDefault;
  }

  Future<void> _updateAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Log.ui('Updating address', screen: 'EditAddressView', context: {
        'addressId': widget.address.id,
        'type': _selectedAddressType.toLowerCase(),
        'city': _cityController.text,
        'state': _stateController.text,
      });

      final response = await ApiService.instance.user.updateAddress(
        addressId: widget.address.id!,
        type: _selectedAddressType.toLowerCase(),
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        addressLine1: _addressLine1Controller.text.trim(),
        addressLine2: _addressLine2Controller.text.trim().isEmpty
            ? null
            : _addressLine2Controller.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        postalCode: _pincodeController.text.trim(),
        country: 'India',
        isDefault: _isDefault,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          Log.ui('Address updated successfully', screen: 'EditAddressView');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Address updated successfully'),
              backgroundColor: Colours.lightThemePrimaryColour,
            ),
          );

          // Return true to indicate success and trigger refresh
          Navigator.of(context).pop(true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to update address'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      Log.e('Error updating address', tag: 'EDIT_ADDRESS_VIEW', error: e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update address. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressLine1Controller.dispose();
    _addressLine2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Edit Address", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Address Type Selection
              Text(
                'Address Type',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: 12),
              
              Row(
                children: _addressTypes.map((type) {
                  final isSelected = _selectedAddressType == type;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedAddressType = type;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.only(right: type != _addressTypes.last ? 8 : 0),
                        padding: EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colours.lightThemePrimaryColour
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? Colours.lightThemePrimaryColour
                                : Colours.lightThemeStockColour,
                            width: 0.5,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            type,
                            style: TextStyles.paragraphSubTextRegular1.copyWith(
                              color: isSelected
                                  ? Colors.white
                                  : Colours.classAdaptiveTextColour(context),
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              SizedBox(height: 24),

              // Contact Information
              Text(
                'Contact Information',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: 16),

              CustomTextField(
                controller: _nameController,
                labelText: 'Full Name',
                hintText: 'Enter full name',
                prefixIcon: Icon(Icons.person_outline),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your full name';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              CustomTextField(
                controller: _phoneController,
                labelText: 'Phone Number',
                hintText: 'Enter phone number',
                prefixIcon: Icon(Icons.phone_outlined),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your phone number';
                  }
                  if (value.trim().length != 10) {
                    return 'Please enter a valid 10-digit phone number';
                  }
                  return null;
                },
              ),

              SizedBox(height: 24),

              // Address Information
              Text(
                'Address Information',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: 16),

              CustomTextField(
                controller: _addressLine1Controller,
                labelText: 'Address Line 1',
                hintText: 'Enter address line 1',
                prefixIcon: Icon(Icons.location_on_outlined),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your address';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              CustomTextField(
                controller: _addressLine2Controller,
                labelText: 'Address Line 2 (Optional)',
                hintText: 'Enter address line 2 (optional)',
                prefixIcon: Icon(Icons.business_outlined),
              ),
              SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _cityController,
                      labelText: 'City',
                      hintText: 'Enter city',
                      prefixIcon: Icon(Icons.location_city_outlined),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter city';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: CustomTextField(
                      controller: _stateController,
                      labelText: 'State',
                      hintText: 'Enter state',
                      prefixIcon: Icon(Icons.map_outlined),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter state';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),

              CustomTextField(
                controller: _pincodeController,
                labelText: 'Pincode',
                hintText: 'Enter pincode',
                prefixIcon: Icon(Icons.pin_drop_outlined),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter pincode';
                  }
                  if (value.trim().length != 6) {
                    return 'Please enter a valid 6-digit pincode';
                  }
                  return null;
                },
              ),

              SizedBox(height: 24),

              // Set as Default Address
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colours.lightThemeStockColour,
                    width: 0.5,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.star_outline,
                      color: Colours.lightThemePrimaryColour,
                      size: 24,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Set as Default Address',
                            style: TextStyles.headingSemiBold1.copyWith(
                              color: Colours.classAdaptiveTextColour(context),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Use this address as your default delivery address',
                            style: TextStyles.paragraphSubTextRegular2.copyWith(
                              color: Colours.lightThemeSecondaryTextColour,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _isDefault,
                      onChanged: (value) {
                        setState(() {
                          _isDefault = value;
                        });
                      },
                      activeColor: Colours.lightThemePrimaryColour,
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32),

              // Update Button
              CustomButton(
                onPressed: _isLoading ? null : _updateAddress,
                text: _isLoading ? 'Updating...' : 'Update Address',
                isLoading: _isLoading,
              ),

              SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
