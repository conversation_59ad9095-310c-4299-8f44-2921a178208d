import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';

class AddPaymentMethodView extends StatefulWidget {
  const AddPaymentMethodView({super.key});

  static const path = '/profile/payment-methods/add';

  @override
  State<AddPaymentMethodView> createState() => _AddPaymentMethodViewState();
}

class _AddPaymentMethodViewState extends State<AddPaymentMethodView> with TickerProviderStateMixin {
  late TabController _tabController;
  final _cardFormKey = GlobalKey<FormState>();
  final _upiFormKey = GlobalKey<FormState>();
  
  // Card Form Controllers
  final _cardNumberController = TextEditingController();
  final _cardHolderNameController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();
  
  // UPI Form Controllers
  final _upiIdController = TextEditingController();
  
  bool _isLoading = false;
  bool _isDefault = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  Future<void> _savePaymentMethod() async {
    bool isValid = false;
    
    if (_tabController.index == 0) {
      // Card validation
      isValid = _cardFormKey.currentState!.validate();
    } else {
      // UPI validation
      isValid = _upiFormKey.currentState!.validate();
    }
    
    if (!isValid) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Payment method saved successfully!'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );

      Navigator.pop(context, true); // Return true to indicate success
    }
  }

  String _formatCardNumber(String value) {
    value = value.replaceAll(' ', '');
    String formatted = '';
    for (int i = 0; i < value.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formatted += ' ';
      }
      formatted += value[i];
    }
    return formatted;
  }

  String _formatExpiryDate(String value) {
    value = value.replaceAll('/', '');
    if (value.length >= 2) {
      return '${value.substring(0, 2)}/${value.substring(2)}';
    }
    return value;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cardNumberController.dispose();
    _cardHolderNameController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    _upiIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Add Payment Method", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colours.lightThemeWhiteColour,
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colours.lightThemePrimaryColour,
              unselectedLabelColor: Colours.lightThemeSecondaryTextColour,
              indicatorColor: Colours.lightThemePrimaryColour,
              labelStyle: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyles.paragraphSubTextRegular1,
              tabs: [
                Tab(
                  icon: Icon(Icons.credit_card),
                  text: 'Card',
                ),
                Tab(
                  icon: Icon(Icons.qr_code),
                  text: 'UPI',
                ),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCardForm(),
                _buildUPIForm(),
              ],
            ),
          ),
          
          // Save Button
          Container(
            padding: ResponsiveUtils.getResponsivePadding(context),
            child: CustomButton(
              onPressed: _savePaymentMethod,
              text: 'Save Payment Method',
              isLoading: _isLoading,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardForm() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Form(
        key: _cardFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card Preview
            Container(
              width: double.infinity,
              height: 200,
              margin: EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colours.lightThemePrimaryColour,
                    Colours.lightThemeSecondaryColour,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'CARD',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Icon(
                          Icons.contactless,
                          color: Colors.white,
                        ),
                      ],
                    ),
                    Spacer(),
                    Text(
                      _cardNumberController.text.isEmpty 
                          ? '**** **** **** ****' 
                          : _formatCardNumber(_cardNumberController.text),
                      style: TextStyles.headingSemiBold.copyWith(
                        color: Colors.white,
                        fontSize: 18,
                        letterSpacing: 2,
                      ),
                    ),
                    SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'CARD HOLDER',
                              style: TextStyles.paragraphSubTextRegular2.copyWith(
                                color: Colors.white70,
                                fontSize: 10,
                              ),
                            ),
                            Text(
                              _cardHolderNameController.text.isEmpty 
                                  ? 'YOUR NAME' 
                                  : _cardHolderNameController.text.toUpperCase(),
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'EXPIRES',
                              style: TextStyles.paragraphSubTextRegular2.copyWith(
                                color: Colors.white70,
                                fontSize: 10,
                              ),
                            ),
                            Text(
                              _expiryDateController.text.isEmpty 
                                  ? 'MM/YY' 
                                  : _expiryDateController.text,
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Card Form Fields
            CustomTextField(
              controller: _cardNumberController,
              labelText: 'Card Number',
              hintText: '1234 5678 9012 3456',
              prefixIcon: Icon(Icons.credit_card),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setState(() {
                  if (value.length <= 19) {
                    _cardNumberController.value = TextEditingValue(
                      text: _formatCardNumber(value),
                      selection: TextSelection.collapsed(
                        offset: _formatCardNumber(value).length,
                      ),
                    );
                  }
                });
              },
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Card number is required';
                }
                String cleanValue = value.replaceAll(' ', '');
                if (cleanValue.length != 16) {
                  return 'Please enter a valid 16-digit card number';
                }
                return null;
              },
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            CustomTextField(
              controller: _cardHolderNameController,
              labelText: 'Card Holder Name',
              hintText: 'Enter name as on card',
              prefixIcon: Icon(Icons.person_outline),
              onChanged: (value) {
                setState(() {});
              },
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Card holder name is required';
                }
                return null;
              },
            ),
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _expiryDateController,
                    labelText: 'Expiry Date',
                    hintText: 'MM/YY',
                    prefixIcon: Icon(Icons.calendar_today),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        if (value.length <= 5) {
                          _expiryDateController.value = TextEditingValue(
                            text: _formatExpiryDate(value),
                            selection: TextSelection.collapsed(
                              offset: _formatExpiryDate(value).length,
                            ),
                          );
                        }
                      });
                    },
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Expiry date is required';
                      }
                      if (value.length != 5) {
                        return 'Please enter valid expiry date';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: CustomTextField(
                    controller: _cvvController,
                    labelText: 'CVV',
                    hintText: '123',
                    prefixIcon: Icon(Icons.lock_outline),
                    keyboardType: TextInputType.number,
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'CVV is required';
                      }
                      if (value.length < 3 || value.length > 4) {
                        return 'Please enter valid CVV';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
            
            _buildDefaultPaymentOption(),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
          ],
        ),
      ),
    );
  }

  Widget _buildUPIForm() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Form(
        key: _upiFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // UPI Info Card
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colours.lightThemePrimaryColour.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.qr_code,
                    color: Colours.lightThemePrimaryColour,
                    size: 40,
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'UPI Payment',
                          style: TextStyles.headingSemiBold1.copyWith(
                            color: Colours.lightThemePrimaryColour,
                          ),
                        ),
                        Text(
                          'Add your UPI ID for quick and secure payments',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            CustomTextField(
              controller: _upiIdController,
              labelText: 'UPI ID',
              hintText: 'yourname@paytm',
              prefixIcon: Icon(Icons.account_balance_wallet),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'UPI ID is required';
                }
                if (!value.contains('@')) {
                  return 'Please enter a valid UPI ID';
                }
                return null;
              },
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            
            // Popular UPI Apps
            Text(
              'Popular UPI Apps',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildUPIAppChip('PayTM', '@paytm'),
                _buildUPIAppChip('PhonePe', '@ybl'),
                _buildUPIAppChip('Google Pay', '@okaxis'),
                _buildUPIAppChip('BHIM', '@upi'),
              ],
            ),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
            
            _buildDefaultPaymentOption(),
            
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
          ],
        ),
      ),
    );
  }

  Widget _buildUPIAppChip(String appName, String suffix) {
    return GestureDetector(
      onTap: () {
        String currentText = _upiIdController.text;
        if (currentText.contains('@')) {
          currentText = currentText.split('@')[0];
        }
        _upiIdController.text = '$currentText$suffix';
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colours.lightThemeStockColour,
          ),
        ),
        child: Text(
          '$appName $suffix',
          style: TextStyles.paragraphSubTextRegular2.copyWith(
            color: Colours.classAdaptiveTextColour(context),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultPaymentOption() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.star_outline,
            color: Colours.lightThemePrimaryColour,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Set as Default Payment Method',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Use this as your preferred payment method',
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isDefault,
            onChanged: (value) {
              setState(() {
                _isDefault = value;
              });
            },
            activeColor: Colours.lightThemePrimaryColour,
          ),
        ],
      ),
    );
  }
}
