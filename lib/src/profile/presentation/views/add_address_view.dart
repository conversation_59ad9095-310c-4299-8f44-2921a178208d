import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';

class AddAddressView extends StatefulWidget {
  const AddAddressView({super.key});

  static const path = '/profile/addresses/add';

  @override
  State<AddAddressView> createState() => _AddAddressViewState();
}

class _AddAddressViewState extends State<AddAddressView> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressLine1Controller = TextEditingController();
  final _addressLine2Controller = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  
  bool _isLoading = false;
  bool _isDefault = false;
  String _selectedAddressType = 'Home';
  final List<String> _addressTypes = ['Home', 'Office', 'Other'];

  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Log.ui('Adding new address', screen: 'AddAddressView', context: {
        'type': _selectedAddressType.toLowerCase(),
        'city': _cityController.text,
        'state': _stateController.text,
      });

      final fullName = '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}'.trim();

      final response = await ApiService.instance.user.addAddress(
        type: _selectedAddressType.toLowerCase(),
        name: fullName,
        phone: _phoneController.text.trim(),
        addressLine1: _addressLine1Controller.text.trim(),
        addressLine2: _addressLine2Controller.text.trim().isEmpty
            ? null
            : _addressLine2Controller.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        postalCode: _pincodeController.text.trim(),
        country: 'India',
        isDefault: _isDefault,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Debug logging to see what's happening
        Log.ui('Add address response received', screen: 'AddAddressView', context: {
          'success': response.success,
          'isSuccess': response.isSuccess,
          'error': response.error,
          'statusCode': response.statusCode,
          'data': response.data,
        });

        if (response.isSuccess) {
          Log.ui('Address added successfully', screen: 'AddAddressView');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Address added successfully'),
              backgroundColor: Colours.lightThemePrimaryColour,
            ),
          );

          // Return true to indicate success and trigger refresh
          Navigator.of(context).pop(true);
        } else {
          Log.ui('Address add failed', screen: 'AddAddressView', context: {
            'error': response.error,
            'success': response.success,
            'isSuccess': response.isSuccess,
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to add address'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      Log.e('Error adding address', tag: 'ADD_ADDRESS_VIEW', error: e);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add address. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _addressLine1Controller.dispose();
    _addressLine2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Add New Address", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Address Type Selection
              Text(
                'Address Type',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: 12),
              
              Row(
                children: _addressTypes.map((type) {
                  final isSelected = _selectedAddressType == type;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedAddressType = type;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.only(right: type != _addressTypes.last ? 8 : 0),
                        padding: EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? Colours.lightThemePrimaryColour 
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected 
                                ? Colours.lightThemePrimaryColour 
                                : Colours.lightThemeStockColour,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _getAddressTypeIcon(type),
                              color: isSelected 
                                  ? Colors.white 
                                  : Colours.lightThemeSecondaryTextColour,
                              size: 16,
                            ),
                            SizedBox(width: 4),
                            Text(
                              type,
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: isSelected 
                                    ? Colors.white 
                                    : Colours.classAdaptiveTextColour(context),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
              
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
              
              // Contact Information
              Text(
                'Contact Information',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              // First Name
              CustomTextField(
                controller: _firstNameController,
                labelText: 'First Name',
                hintText: 'Enter first name',
                prefixIcon: Icon(Icons.person_outline),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'First name is required';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),

              // Last Name
              CustomTextField(
                controller: _lastNameController,
                labelText: 'Last Name',
                hintText: 'Enter last name',
                prefixIcon: Icon(Icons.person_outline),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Last name is required';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              CustomTextField(
                controller: _phoneController,
                labelText: 'Phone Number',
                hintText: 'Enter phone number',
                prefixIcon: Icon(Icons.phone_outlined),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Phone number is required';
                  }
                  if (value.length < 10) {
                    return 'Please enter a valid phone number';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
              
              // Address Information
              Text(
                'Address Information',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              CustomTextField(
                controller: _addressLine1Controller,
                labelText: 'Address Line 1',
                hintText: 'House/Flat/Building number, Street name',
                prefixIcon: Icon(Icons.location_on_outlined),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Address line 1 is required';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              CustomTextField(
                controller: _addressLine2Controller,
                labelText: 'Address Line 2 (Optional)',
                hintText: 'Area, Landmark',
                prefixIcon: Icon(Icons.location_city_outlined),
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _cityController,
                      labelText: 'City',
                      hintText: 'Enter city',
                      prefixIcon: Icon(Icons.location_city),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'City is required';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _stateController,
                      labelText: 'State',
                      hintText: 'Enter state',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'State is required';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
              
              CustomTextField(
                controller: _pincodeController,
                labelText: 'Pincode',
                hintText: 'Enter 6-digit pincode',
                prefixIcon: Icon(Icons.pin_drop_outlined),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Pincode is required';
                  }
                  if (value.length != 6) {
                    return 'Please enter a valid 6-digit pincode';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
              
              // Default Address Option
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colours.lightThemeStockColour,
                    width: 0.5,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.star_outline,
                      color: Colours.lightThemePrimaryColour,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Set as Default Address',
                            style: TextStyles.paragraphSubTextRegular1.copyWith(
                              color: Colours.classAdaptiveTextColour(context),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'Use this address as your default delivery address',
                            style: TextStyles.paragraphSubTextRegular2.copyWith(
                              color: Colours.lightThemeSecondaryTextColour,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _isDefault,
                      onChanged: (value) {
                        setState(() {
                          _isDefault = value;
                        });
                      },
                      activeColor: Colours.lightThemePrimaryColour,
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 32)),
              
              // Save Button
              CustomButton(
                onPressed: _saveAddress,
                text: 'Save Address',
                isLoading: _isLoading,
              ),
              
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getAddressTypeIcon(String type) {
    switch (type) {
      case 'Home':
        return Icons.home_outlined;
      case 'Office':
        return Icons.business_outlined;
      case 'Other':
        return Icons.location_on_outlined;
      default:
        return Icons.location_on_outlined;
    }
  }
}
