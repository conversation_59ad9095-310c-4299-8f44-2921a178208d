import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/singletons/cache.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/common/app/cache_helper.dart';
import 'package:ghanshyam_murti_bhandar/core/services/injection_container.dart';

class SettingsView extends StatefulWidget {
  const SettingsView({super.key});

  static const path = '/settings';

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ThemeMode>(
      valueListenable: Cache.instance.themeModeNotifier,
      builder: (context, themeMode, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text("Settings", style: TextStyles.headingSemiBold),
            bottom: AppBarBottom(),
            centerTitle: true,
            backgroundColor: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colours.lightThemeWhiteColour,
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
          ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Settings Section
            _buildSectionHeader('Appearance'),
            SizedBox(height: 12),
            _buildThemeSettingsCard(),
            SizedBox(height: 24),

            // App Settings Section
            _buildSectionHeader('App Settings'),
            SizedBox(height: 12),
            _buildAppSettingsCard(),
            SizedBox(height: 24),

            // About Section
            _buildSectionHeader('About'),
            SizedBox(height: 12),
            _buildAboutCard(),
          ],
        ),
      ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyles.headingMedium3.copyWith(
        color: Colours.classAdaptiveTextColour(context),
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildThemeSettingsCard() {
    return Container(
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          _buildThemeOption(
            title: 'Light Theme',
            subtitle: 'Use light theme',
            icon: Icons.light_mode,
            themeMode: ThemeMode.light,
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildThemeOption(
            title: 'Dark Theme',
            subtitle: 'Use dark theme',
            icon: Icons.dark_mode,
            themeMode: ThemeMode.dark,
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildThemeOption(
            title: 'System Default',
            subtitle: 'Follow system theme',
            icon: Icons.settings_system_daydream,
            themeMode: ThemeMode.system,
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required ThemeMode themeMode,
  }) {
    return ValueListenableBuilder<ThemeMode>(
      valueListenable: Cache.instance.themeModeNotifier,
      builder: (context, currentTheme, child) {
        final isSelected = currentTheme == themeMode;
        return ListTile(
          leading: Icon(
            icon,
            color: isSelected 
                ? Colours.lightThemePrimaryColour 
                : Colours.lightThemeSecondaryTextColour,
          ),
          title: Text(
            title,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),
          trailing: isSelected
              ? Icon(
                  Icons.check_circle,
                  color: Colours.lightThemePrimaryColour,
                )
              : Icon(
                  Icons.radio_button_unchecked,
                  color: Colours.lightThemeSecondaryTextColour,
                ),
          onTap: () async {
            // Save theme to both cache and persistent storage
            Cache.instance.setThemeMode(themeMode);
            final cacheHelper = sl<CacheHelper>();
            await cacheHelper.cacheThemeMode(themeMode);
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        );
      },
    );
  }

  Widget _buildAppSettingsCard() {
    return Container(
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.notifications_outlined,
            title: 'Push Notifications',
            subtitle: 'Receive notifications',
            trailing: Switch(
              value: true,
              onChanged: (value) {
                // Handle notification toggle
              },
              activeColor: Colours.lightThemePrimaryColour,
            ),
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildSettingItem(
            icon: Icons.language_outlined,
            title: 'Language',
            subtitle: 'English',
            trailing: Icon(
              Icons.chevron_right,
              color: Colours.lightThemeSecondaryTextColour,
            ),
            onTap: () {
              // Handle language selection
            },
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildSettingItem(
            icon: Icons.storage_outlined,
            title: 'Clear Cache',
            subtitle: 'Free up storage space',
            trailing: Icon(
              Icons.chevron_right,
              color: Colours.lightThemeSecondaryTextColour,
            ),
            onTap: () {
              _showClearCacheDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutCard() {
    return Container(
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.info_outline,
            title: 'App Version',
            subtitle: '1.0.0',
            trailing: SizedBox.shrink(),
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildSettingItem(
            icon: Icons.privacy_tip_outlined,
            title: 'Privacy Policy',
            subtitle: 'Read our privacy policy',
            trailing: Icon(
              Icons.chevron_right,
              color: Colours.lightThemeSecondaryTextColour,
            ),
            onTap: () {
              // Handle privacy policy
            },
          ),
          Divider(
            height: 1,
            color: Colours.lightThemeStockColour,
          ),
          _buildSettingItem(
            icon: Icons.description_outlined,
            title: 'Terms of Service',
            subtitle: 'Read our terms',
            trailing: Icon(
              Icons.chevron_right,
              color: Colours.lightThemeSecondaryTextColour,
            ),
            onTap: () {
              // Handle terms of service
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colours.lightThemeSecondaryTextColour,
      ),
      title: Text(
        title,
        style: TextStyles.paragraphSubTextRegular1.copyWith(
          color: Colours.classAdaptiveTextColour(context),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyles.paragraphSubTextRegular2.copyWith(
          color: Colours.lightThemeSecondaryTextColour,
        ),
      ),
      trailing: trailing,
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear Cache'),
        content: Text('Are you sure you want to clear the app cache? This will remove temporary files and may improve performance.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Cache cleared successfully'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
            child: Text(
              'Clear',
              style: TextStyle(color: Colours.lightThemePrimaryColour),
            ),
          ),
        ],
      ),
    );
  }
}
