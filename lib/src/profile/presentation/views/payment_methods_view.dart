import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/add_payment_method_view.dart';

class PaymentMethodsView extends StatefulWidget {
  const PaymentMethodsView({super.key});

  static const path = '/profile/payment-methods';

  @override
  State<PaymentMethodsView> createState() => _PaymentMethodsViewState();
}

class _PaymentMethodsViewState extends State<PaymentMethodsView> {
  bool _isLoading = true;
  List<PaymentMethod> _paymentMethods = [];

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    // Simulate loading payment methods
    await Future.delayed(Duration(seconds: 1));
    
    if (mounted) {
      setState(() {
        _paymentMethods = [
          PaymentMethod(
            id: '1',
            type: PaymentType.card,
            cardNumber: '**** **** **** 1234',
            cardHolderName: 'John Doe',
            expiryDate: '12/25',
            cardType: CardType.visa,
            isDefault: true,
          ),
          PaymentMethod(
            id: '2',
            type: PaymentType.card,
            cardNumber: '**** **** **** 5678',
            cardHolderName: 'John Doe',
            expiryDate: '08/26',
            cardType: CardType.mastercard,
            isDefault: false,
          ),
          PaymentMethod(
            id: '3',
            type: PaymentType.upi,
            upiId: 'john.doe@paytm',
            isDefault: false,
          ),
        ];
        _isLoading = false;
      });
    }
  }

  void _addNewPaymentMethod() async {
    final result = await context.push(AddPaymentMethodView.path);
    if (result == true) {
      // Refresh the payment methods list
      _loadPaymentMethods();
    }
  }

  void _editPaymentMethod(PaymentMethod paymentMethod) {
    _showPaymentMethodDialog(paymentMethod: paymentMethod);
  }

  void _deletePaymentMethod(PaymentMethod paymentMethod) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Payment Method'),
        content: Text('Are you sure you want to delete this payment method?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _paymentMethods.removeWhere((pm) => pm.id == paymentMethod.id);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Payment method deleted successfully'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _setDefaultPaymentMethod(PaymentMethod paymentMethod) {
    setState(() {
      for (var pm in _paymentMethods) {
        pm.isDefault = pm.id == paymentMethod.id;
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Default payment method updated'),
        backgroundColor: Colours.lightThemePrimaryColour,
      ),
    );
  }

  void _showPaymentMethodDialog({PaymentMethod? paymentMethod}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(paymentMethod == null ? 'Add Payment Method' : 'Edit Payment Method'),
        content: Text('Payment method form coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Payment method functionality coming soon!'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Payment Methods", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _addNewPaymentMethod,
            icon: Icon(Icons.add),
          ),
        ],
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading payment methods...',
              size: 100,
            )
          : _paymentMethods.isEmpty
              ? EmptyStateWidget(
                  lottieAsset: Media.search,
                  title: 'No payment methods found',
                  description: 'Add your first payment method to get started',
                  buttonText: 'Add Payment Method',
                  onButtonPressed: _addNewPaymentMethod,
                )
              : Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        padding: ResponsiveUtils.getResponsivePadding(context),
                        itemCount: _paymentMethods.length,
                        itemBuilder: (context, index) {
                          final paymentMethod = _paymentMethods[index];
                          return _buildPaymentMethodCard(paymentMethod);
                        },
                      ),
                    ),
                    
                    // Add New Payment Method Button
                    Container(
                      padding: ResponsiveUtils.getResponsivePadding(context),
                      child: CustomButton(
                        onPressed: _addNewPaymentMethod,
                        text: 'Add Payment Method',
                        icon: Icon(Icons.add, color: Colors.white),
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod paymentMethod) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: paymentMethod.isDefault 
              ? Colours.lightThemePrimaryColour 
              : Colours.lightThemeStockColour,
          width: paymentMethod.isDefault ? 1 : 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Method Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getPaymentTypeColor(paymentMethod.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getPaymentTypeIcon(paymentMethod.type),
                  color: _getPaymentTypeColor(paymentMethod.type),
                  size: 24,
                ),
              ),
              
              SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (paymentMethod.type == PaymentType.card) ...[
                      Row(
                        children: [
                          Text(
                            paymentMethod.cardNumber!,
                            style: TextStyles.headingSemiBold1.copyWith(
                              color: Colours.classAdaptiveTextColour(context),
                            ),
                          ),
                          SizedBox(width: 8),
                          _buildCardTypeIcon(paymentMethod.cardType!),
                        ],
                      ),
                      Text(
                        paymentMethod.cardHolderName!,
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                      Text(
                        'Expires ${paymentMethod.expiryDate}',
                        style: TextStyles.paragraphSubTextRegular2.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                    ] else if (paymentMethod.type == PaymentType.upi) ...[
                      Text(
                        'UPI',
                        style: TextStyles.headingSemiBold1.copyWith(
                          color: Colours.classAdaptiveTextColour(context),
                        ),
                      ),
                      Text(
                        paymentMethod.upiId!,
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              if (paymentMethod.isDefault) ...[
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Default',
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colours.lightThemePrimaryColour,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(width: 8),
              ],
              
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _editPaymentMethod(paymentMethod);
                      break;
                    case 'default':
                      _setDefaultPaymentMethod(paymentMethod);
                      break;
                    case 'delete':
                      _deletePaymentMethod(paymentMethod);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  if (!paymentMethod.isDefault)
                    PopupMenuItem(
                      value: 'default',
                      child: Row(
                        children: [
                          Icon(Icons.star, size: 16),
                          SizedBox(width: 8),
                          Text('Set as Default'),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getPaymentTypeColor(PaymentType type) {
    switch (type) {
      case PaymentType.card:
        return Colors.blue;
      case PaymentType.upi:
        return Colors.orange;
      case PaymentType.netbanking:
        return Colors.green;
      case PaymentType.wallet:
        return Colors.purple;
    }
  }

  IconData _getPaymentTypeIcon(PaymentType type) {
    switch (type) {
      case PaymentType.card:
        return Icons.credit_card;
      case PaymentType.upi:
        return Icons.qr_code;
      case PaymentType.netbanking:
        return Icons.account_balance;
      case PaymentType.wallet:
        return Icons.account_balance_wallet;
    }
  }

  Widget _buildCardTypeIcon(CardType cardType) {
    String text;
    Color color;
    
    switch (cardType) {
      case CardType.visa:
        text = 'VISA';
        color = Colors.blue;
        break;
      case CardType.mastercard:
        text = 'MC';
        color = Colors.red;
        break;
      case CardType.rupay:
        text = 'RUPAY';
        color = Colors.green;
        break;
      case CardType.amex:
        text = 'AMEX';
        color = Colors.blue;
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyles.paragraphSubTextRegular2.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }
}

// Data Models
class PaymentMethod {
  final String id;
  final PaymentType type;
  final String? cardNumber;
  final String? cardHolderName;
  final String? expiryDate;
  final CardType? cardType;
  final String? upiId;
  bool isDefault;

  PaymentMethod({
    required this.id,
    required this.type,
    this.cardNumber,
    this.cardHolderName,
    this.expiryDate,
    this.cardType,
    this.upiId,
    required this.isDefault,
  });
}

enum PaymentType {
  card,
  upi,
  netbanking,
  wallet,
}

enum CardType {
  visa,
  mastercard,
  rupay,
  amex,
}
