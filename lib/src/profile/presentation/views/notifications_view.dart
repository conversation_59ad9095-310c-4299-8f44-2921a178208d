import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';

class NotificationsView extends StatefulWidget {
  const NotificationsView({super.key});

  static const path = '/profile/notifications';

  @override
  State<NotificationsView> createState() => _NotificationsViewState();
}

class _NotificationsViewState extends State<NotificationsView> {
  bool _isLoading = true;
  
  // Notification Settings
  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  
  // Notification Categories
  bool _orderUpdates = true;
  bool _promotionalOffers = true;
  bool _newArrivals = false;
  bool _priceDrops = true;
  bool _stockAlerts = false;
  bool _festivalOffers = true;
  bool _accountSecurity = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  Future<void> _loadNotificationSettings() async {
    // Simulate loading notification settings
    await Future.delayed(Duration(seconds: 1));
    
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveNotificationSettings() async {
    // Show loading state
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 16),
            Text('Saving settings...'),
          ],
        ),
        backgroundColor: Colours.lightThemePrimaryColour,
        duration: Duration(seconds: 2),
      ),
    );

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    if (mounted) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Notification settings saved successfully!'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Notifications", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveNotificationSettings,
            child: Text(
              'Save',
              style: TextStyle(
                color: _isLoading ? Colors.grey : Colours.lightThemePrimaryColour,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading notification settings...',
              size: 100,
            )
          : SingleChildScrollView(
              padding: ResponsiveUtils.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Notification Methods Section
                  _buildSectionHeader('Notification Methods'),
                  _buildNotificationMethodCard(),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
                  
                  // Notification Categories Section
                  _buildSectionHeader('Notification Categories'),
                  _buildNotificationCategoriesCard(),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 24)),
                  
                  // Quick Actions Section
                  _buildSectionHeader('Quick Actions'),
                  _buildQuickActionsCard(),
                  
                  SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: TextStyles.headingSemiBold1.copyWith(
          color: Colours.classAdaptiveTextColour(context),
        ),
      ),
    );
  }

  Widget _buildNotificationMethodCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildSwitchTile(
            title: 'Push Notifications',
            subtitle: 'Receive notifications on your device',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
            icon: Icons.notifications,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Email Notifications',
            subtitle: 'Receive notifications via email',
            value: _emailNotifications,
            onChanged: (value) {
              setState(() {
                _emailNotifications = value;
              });
            },
            icon: Icons.email,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'SMS Notifications',
            subtitle: 'Receive notifications via SMS',
            value: _smsNotifications,
            onChanged: (value) {
              setState(() {
                _smsNotifications = value;
              });
            },
            icon: Icons.sms,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCategoriesCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildSwitchTile(
            title: 'Order Updates',
            subtitle: 'Order confirmations, shipping updates',
            value: _orderUpdates,
            onChanged: (value) {
              setState(() {
                _orderUpdates = value;
              });
            },
            icon: Icons.shopping_bag,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Promotional Offers',
            subtitle: 'Special discounts and deals',
            value: _promotionalOffers,
            onChanged: (value) {
              setState(() {
                _promotionalOffers = value;
              });
            },
            icon: Icons.local_offer,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'New Arrivals',
            subtitle: 'Latest products and collections',
            value: _newArrivals,
            onChanged: (value) {
              setState(() {
                _newArrivals = value;
              });
            },
            icon: Icons.new_releases,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Price Drops',
            subtitle: 'Price reductions on wishlist items',
            value: _priceDrops,
            onChanged: (value) {
              setState(() {
                _priceDrops = value;
              });
            },
            icon: Icons.trending_down,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Stock Alerts',
            subtitle: 'When out-of-stock items are available',
            value: _stockAlerts,
            onChanged: (value) {
              setState(() {
                _stockAlerts = value;
              });
            },
            icon: Icons.inventory,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Festival Offers',
            subtitle: 'Special festival and seasonal offers',
            value: _festivalOffers,
            onChanged: (value) {
              setState(() {
                _festivalOffers = value;
              });
            },
            icon: Icons.celebration,
          ),
          
          Divider(height: 24),
          
          _buildSwitchTile(
            title: 'Account Security',
            subtitle: 'Login alerts and security updates',
            value: _accountSecurity,
            onChanged: (value) {
              setState(() {
                _accountSecurity = value;
              });
            },
            icon: Icons.security,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildActionTile(
            title: 'Enable All Notifications',
            subtitle: 'Turn on all notification categories',
            icon: Icons.notifications_active,
            onTap: () {
              setState(() {
                _pushNotifications = true;
                _emailNotifications = true;
                _smsNotifications = true;
                _orderUpdates = true;
                _promotionalOffers = true;
                _newArrivals = true;
                _priceDrops = true;
                _stockAlerts = true;
                _festivalOffers = true;
                _accountSecurity = true;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('All notifications enabled'),
                  backgroundColor: Colours.lightThemePrimaryColour,
                ),
              );
            },
          ),
          
          Divider(height: 24),
          
          _buildActionTile(
            title: 'Disable All Notifications',
            subtitle: 'Turn off all notification categories',
            icon: Icons.notifications_off,
            onTap: () {
              setState(() {
                _pushNotifications = false;
                _emailNotifications = false;
                _smsNotifications = false;
                _orderUpdates = false;
                _promotionalOffers = false;
                _newArrivals = false;
                _priceDrops = false;
                _stockAlerts = false;
                _festivalOffers = false;
                // Keep account security enabled for safety
                _accountSecurity = true;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('All notifications disabled (except security)'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colours.lightThemePrimaryColour,
            size: 20,
          ),
        ),
        
        SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                subtitle,
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
            ],
          ),
        ),
        
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Colours.lightThemePrimaryColour,
        ),
      ],
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colours.lightThemeSecondaryColour.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colours.lightThemeSecondaryColour,
                size: 20,
              ),
            ),
            
            SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.classAdaptiveTextColour(context),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyles.paragraphSubTextRegular2.copyWith(
                      color: Colours.lightThemeSecondaryTextColour,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ],
        ),
      ),
    );
  }
}
