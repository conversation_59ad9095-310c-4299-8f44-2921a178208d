import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/categories/presentation/views/category_products_view.dart';
import 'package:ghanshyam_murti_bhandar/src/product_details/presentation/views/product_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/products/presentation/views/all_products_view.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/category_model.dart'
    as new_category;

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  static const path = '/home';

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> with WidgetsBindingObserver {
  final _searchController = TextEditingController();

  bool _isLoading = true;
  List<ProductModel> _recommendedProducts = [];
  List<ProductModel> _featuredProducts = [];
  List<new_category.CategoryModel> _categories = [];
  int _cartItemCount = 0;
  int _wishlistItemCount = 0;
  Map<String, bool> _wishlistStates =
      {}; // Track wishlist states for instant updates

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadData();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh counts when app comes back to foreground
      _refreshCartCount();
      _refreshWishlistCount();
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load data from API services
      await Future.wait([
        _loadRecommendedProducts(),
        _loadFeaturedProducts(),
        _loadCategories(),
        _loadCartItemCount(),
        _loadWishlistItemCount(),
      ]);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to load data: ${ApiErrorHandler.getUserFriendlyMessage(e)}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadRecommendedProducts() async {
    try {
      // Use the existing products service which should handle the response correctly
      final response = await ApiService.instance.products.getProducts(
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
      );

      if (response.isSuccess && response.data != null) {
        _recommendedProducts = response.data!.products;

        // Initialize wishlist states from product data
        for (final product in _recommendedProducts) {
          _wishlistStates[product.id] = product.isWishlisted;
          debugPrint(
            '🛒 Product ${product.name}: isWishlisted = ${product.isWishlisted}',
          );
        }

        debugPrint(
          'Loaded ${_recommendedProducts.length} recommended products',
        );
      }
    } catch (e) {
      debugPrint('Error loading recommended products: $e');
    }
  }

  Future<void> _loadFeaturedProducts() async {
    try {
      final response = await ApiService.instance.products.getFeaturedProducts(
        limit: 10,
      );

      if (response.isSuccess && response.data != null) {
        _featuredProducts = response.data!;

        // Initialize wishlist states from product data
        for (final product in _featuredProducts) {
          _wishlistStates[product.id] = product.isWishlisted;
          debugPrint(
            '⭐ Featured Product ${product.name}: isWishlisted = ${product.isWishlisted}',
          );
        }

        debugPrint('Loaded ${_featuredProducts.length} featured products');
      }
    } catch (e) {
      debugPrint('Error loading featured products: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      debugPrint('Loading root categories for home page...');
      final response = await ApiService.instance.categories.getRootCategories();
      debugPrint(
        'Root categories response: success=${response.isSuccess}, data length=${response.data?.length}',
      );

      if (response.isSuccess && response.data != null) {
        setState(() {
          // Store all categories but only show first 7 + More button
          _categories = response.data!;
        });
        debugPrint('Root categories loaded: ${_categories.length} categories');
        for (var category in _categories) {
          debugPrint(
            'Root Category: ${category.name} (${category.id}) - Parent: ${category.parent}',
          );
        }
      } else {
        debugPrint('Failed to load root categories: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error loading root categories: $e');
    }
  }

  Future<void> _loadCartItemCount() async {
    try {
      _cartItemCount = await ApiService.instance.cart.getCartItemCount();
    } catch (e) {
      debugPrint('Error loading cart count: $e');
    }
  }

  Future<void> _loadWishlistItemCount() async {
    try {
      _wishlistItemCount =
          await ApiService.instance.wishlist.getWishlistItemCount();
    } catch (e) {
      debugPrint('Error loading wishlist count: $e');
    }
  }

  /// Refresh cart count and update UI
  Future<void> _refreshCartCount() async {
    try {
      final newCartCount = await ApiService.instance.cart.getCartItemCount();
      if (mounted && newCartCount != _cartItemCount) {
        setState(() {
          _cartItemCount = newCartCount;
        });
        debugPrint('🛒 Cart count updated: $_cartItemCount');
      }
    } catch (e) {
      debugPrint('Error refreshing cart count: $e');
    }
  }

  /// Refresh wishlist count and update UI
  Future<void> _refreshWishlistCount() async {
    try {
      final newWishlistCount =
          await ApiService.instance.wishlist.getWishlistItemCount();
      if (mounted && newWishlistCount != _wishlistItemCount) {
        setState(() {
          _wishlistItemCount = newWishlistCount;
        });
        debugPrint('❤️ Wishlist count updated: $_wishlistItemCount');
      }

      // Also refresh the wishlist status of all products
      await _refreshProductWishlistStatus();
    } catch (e) {
      debugPrint('Error refreshing wishlist count: $e');
    }
  }

  /// Refresh the wishlist status of all products by fetching the latest wishlist
  Future<void> _refreshProductWishlistStatus() async {
    try {
      final wishlistResponse = await ApiService.instance.wishlist.getWishlist();
      if (wishlistResponse.isSuccess && wishlistResponse.data != null) {
        final wishlistProductIds =
            wishlistResponse.data!.items.map((item) => item.product.id).toSet();

        // Update wishlist states for all products
        setState(() {
          // Update recommended products
          for (int i = 0; i < _recommendedProducts.length; i++) {
            final productId = _recommendedProducts[i].id;
            final isWishlisted = wishlistProductIds.contains(productId);
            _wishlistStates[productId] = isWishlisted;
            _recommendedProducts[i] = _recommendedProducts[i].copyWith(
              isWishlisted: isWishlisted,
            );
          }

          // Update featured products
          for (int i = 0; i < _featuredProducts.length; i++) {
            final productId = _featuredProducts[i].id;
            final isWishlisted = wishlistProductIds.contains(productId);
            _wishlistStates[productId] = isWishlisted;
            _featuredProducts[i] = _featuredProducts[i].copyWith(
              isWishlisted: isWishlisted,
            );
          }
        });

        debugPrint(
          '🔄 Refreshed wishlist status for ${_wishlistStates.length} products',
        );
      }
    } catch (e) {
      debugPrint('Error refreshing product wishlist status: $e');
    }
  }

  Future<void> _toggleWishlist(ProductModel product) async {
    // Get current state (from our map or product default)
    final currentState = _wishlistStates[product.id] ?? product.isWishlisted;
    final previousState = currentState;

    // Optimistically update UI immediately
    setState(() {
      _wishlistStates[product.id] = !currentState;
    });

    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(
        product.id,
      );

      if (response.isSuccess && response.data != null) {
        // Update with actual server response
        setState(() {
          _wishlistStates[product.id] = response.data!.isWishlisted;

          // Update the product's isWishlisted property in all product lists
          _updateProductWishlistStatus(product.id, response.data!.isWishlisted);
        });

        // Update wishlist count
        _refreshWishlistCount();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data!.wasAdded
                    ? 'Added to favorites'
                    : 'Removed from favorites',
              ),
              backgroundColor:
                  response.data!.wasAdded ? Colors.green : Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Revert to previous state on failure
        setState(() {
          _wishlistStates[product.id] = previousState;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update favorites'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Revert to previous state on error
      setState(() {
        _wishlistStates[product.id] = previousState;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  // Helper method to get category color
  Color _getCategoryColor(String categoryName) {
    final colors = [
      Color(0xFFFF9500), // Orange
      Color(0xFF007AFF), // Blue
      Color(0xFF5856D6), // Purple
      Color(0xFFe53e3e), // Red
      Color(0xFFFF2D92), // Pink
      Color(0xFF34C759), // Green
      Color(0xFF5AC8FA), // Light Blue
      Color(0xFFFF3B30), // Red
    ];

    // Use hash of category name to get consistent color
    final hash = categoryName.hashCode.abs();
    return colors[hash % colors.length];
  }

  // Helper method to get category icon
  IconData _getCategoryIcon(String categoryName) {
    final name = categoryName.toLowerCase();

    if (name.contains('best') ||
        name.contains('selling') ||
        name.contains('popular')) {
      return Icons.star;
    } else if (name.contains('new') || name.contains('arrival')) {
      return Icons.new_releases;
    } else if (name.contains('home') || name.contains('kitchen')) {
      return Icons.home;
    } else if (name.contains('electronics') || name.contains('device')) {
      return Icons.devices;
    } else if (name.contains('fashion') || name.contains('clothing')) {
      return Icons.checkroom;
    } else if (name.contains('beauty') || name.contains('care')) {
      return Icons.face;
    } else if (name.contains('gift') || name.contains('rakhi')) {
      return Icons.card_giftcard;
    } else if (name.contains('food') || name.contains('grocery')) {
      return Icons.local_grocery_store;
    } else if (name.contains('book') || name.contains('education')) {
      return Icons.book;
    } else if (name.contains('sport') || name.contains('fitness')) {
      return Icons.sports;
    } else {
      return Icons.category;
    }
  }

  Widget _buildCategoryItem(
    new_category.CategoryModel category, {
    IconData? icon,
    Color? color,
  }) {
    // Use provided color or generate one based on category name
    final categoryColor = color ?? _getCategoryColor(category.name);
    final categoryIcon = icon ?? _getCategoryIcon(category.name);

    return GestureDetector(
      onTap: () {
        // Navigate to category products page with the selected category
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => CategoryProductsView(
                  categoryId: category.id,
                  categoryName: category.name,
                ),
          ),
        );
      },
      child: Column(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  categoryColor.withValues(alpha: 0.1),
                  categoryColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: CoreUtils.adaptiveColour(
                  context,
                  lightModeColour: categoryColor.withValues(alpha: 0.4),
                  darkModeColour: categoryColor.withValues(alpha: 1.0),
                ),
                width: 2.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: categoryColor.withValues(alpha: 0.15),
                  blurRadius: 12,
                  offset: Offset(0, 6),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child:
                  category.image != null && category.image!.isNotEmpty
                      ? ClipOval(
                        child: Image.network(
                          // Check if the image URL is already complete
                          category.image!.startsWith('http://') ||
                                  category.image!.startsWith('https://')
                              ? category.image!
                              : 'https://ghanshyambackend.onrender.com/${category.image}',
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            // If the image fails to load, show icon
                            return Icon(
                              categoryIcon,
                              size: 50,
                              color: categoryColor,
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return SizedBox(
                              width: 80,
                              height: 80,
                              child: Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                  color: categoryColor,
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                      : Icon(categoryIcon, size: 50, color: categoryColor),
            ),
          ),
          SizedBox(height: 4),
          SizedBox(
            width: 100,
            child: Text(
              category.name,
              style: TextStyle(
                color: Colours.classAdaptiveTextColour(context),
                fontSize: 14,
                fontWeight: FontWeight.w600,
                height: 1.2,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDealCard(String topText, String price, Color priceColor) {
    return Container(
      height: 90,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colors.white,
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
            CoreUtils.adaptiveColour(
              context,
              lightModeColour: Color(0xFFFFFAFA),
              darkModeColour: Colours.darkThemeDarkSharpColor.withValues(
                alpha: 0.8,
              ),
            ),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: priceColor.withValues(alpha: 0.3),
            darkModeColour: priceColor.withValues(alpha: 0.9),
          ),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: priceColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            topText,
            style: TextStyle(
              color: CoreUtils.adaptiveColour(
                context,
                lightModeColour: Colours.lightThemeSecondaryTextColour,
                darkModeColour: Colors.white,
              ),
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 6),
          Text(
            price,
            style: TextStyle(
              color: priceColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrustBadge(IconData icon, String text) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 32),
        SizedBox(height: 8),
        Text(
          text,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Helper method to build "More Categories" item
  Widget _buildMoreCategoriesItem() {
    return GestureDetector(
      onTap: () {
        // Navigate to all categories page using GoRouter
        context.push('/categories');
      },
      child: Column(
        children: [
          Container(
            width: 90,
            height: 90,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF8E8E93).withValues(alpha: 0.1),
                  Color(0xFF8E8E93).withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: Color(0xFF8E8E93).withValues(alpha: 0.4),
                width: 2.0,
              ),
            ),
            child: Center(
              child: Icon(Icons.grid_view, size: 30, color: Color(0xFF8E8E93)),
            ),
          ),
          SizedBox(height: 10),
          SizedBox(
            width: 75,
            child: Text(
              'More',
              style: TextStyle(
                color: Colours.classAdaptiveTextColour(context),
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build category loading item
  Widget _buildCategoryLoadingItem() {
    return Column(
      children: [
        Container(
          width: 75,
          height: 75,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colours.lightThemePrimaryColour,
              ),
            ),
          ),
        ),
        SizedBox(height: 10),
        Container(
          width: 50,
          height: 12,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      ],
    );
  }

  // Remove this method since we're loading from API
  // The _recommendedProducts list is now populated from the API

  Widget _buildRecommendedProductCard(ProductModel product) {
    return GestureDetector(
      onTap: () async {
        // Navigate to product details and refresh counts when returning
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailsView(product: product),
          ),
        );
        // Refresh both cart and wishlist counts when returning from product details
        _refreshCartCount();
        _refreshWishlistCount();
      },
      child: Container(
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colors.white,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colors.grey.withValues(alpha: 0.2),
              darkModeColour: Colors.grey.withValues(alpha: 0.4),
            ),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colours.lightThemeTintStockColour,
                  image:
                      CoreUtils.isValidImageUrl(product.imageUrl)
                          ? DecorationImage(
                            image: NetworkImage(product.imageUrl),
                            fit: BoxFit.cover,
                            onError: (exception, stackTrace) {},
                          )
                          : null,
                ),
                child: Stack(
                  children: [
                    // Show placeholder icon if no valid image
                    if (!CoreUtils.isValidImageUrl(product.imageUrl))
                      Center(
                        child: Icon(
                          Icons.image_outlined,
                          color: Colours.lightThemeSecondaryTextColour,
                          size: 48,
                        ),
                      ),
                    // Discount Badge
                    if (product.originalPrice != null &&
                        product.originalPrice! > product.price)
                      Positioned(
                        child: Container(
                          height: 25,
                          width: 60,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                          ),
                          child: Text(
                            '${product.discountPercentage.round()}% OFF',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    // Featured Badge
                    if (product.isFeatured == true)
                      Positioned(
                        left: 0,
                        bottom: 0,
                        child: Container(
                          height: 25,
                          width: 70,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                          ),
                          child: Text(
                            'FEATURED',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    // Favorite Icon
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () => _toggleWishlist(product),
                        child: Container(
                          width: 28,
                          height: 28,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            (_wishlistStates[product.id] ??
                                    product.isWishlisted)
                                ? Icons.favorite
                                : Icons.favorite_outline,
                            color:
                                (_wishlistStates[product.id] ??
                                        product.isWishlisted)
                                    ? Colors.red
                                    : Colors.grey,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Product Details
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Name
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Product Description
                    if (product.description.isNotEmpty)
                      Text(
                        product.description,
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    SizedBox(height: 4),
                    // Rating
                    if (product.rating > 0)
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 12),
                          SizedBox(width: 2),
                          Text(
                            '${product.rating}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          if (product.reviewCount > 0)
                            Text(
                              ' (${product.reviewCount})',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    // Stock Status
                    SizedBox(height: 2),
                    Row(
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color:
                                product.stock > 0 ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          product.stock > 0
                              ? 'In Stock (${product.stock})'
                              : 'Out of Stock',
                          style: TextStyle(
                            fontSize: 9,
                            color:
                                product.stock > 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Text(
                          '₹${product.price}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFFe53e3e),
                          ),
                        ),
                        if (product.originalPrice != null) ...[
                          SizedBox(width: 4),
                          Text(
                            '₹${product.originalPrice}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CoreUtils.adaptiveColour(
        context,
        lightModeColour: Colors.white,
        darkModeColour: Colours.darkThemeDarkSharpColor,
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // Custom App Bar
            SliverToBoxAdapter(
              child: Container(
                color: CoreUtils.adaptiveColour(
                  context,
                  lightModeColour: Colors.white,
                  darkModeColour: Colours.darkThemeDarkSharpColor,
                ),
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Top Header with Logo and Icons
                    Row(
                      children: [
                        // Menu Icon
                        Icon(
                          Icons.menu,
                          color: Colours.classAdaptiveTextColour(context),
                          size: 24,
                        ),
                        SizedBox(width: 16),
                        // Logo
                        Expanded(
                          child: Row(
                            children: [
                              Text(
                                'Ghanshyam',
                                style: TextStyles.headingSemiBold.copyWith(
                                  color: Colors.red,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Murti',
                                style: TextStyles.headingSemiBold.copyWith(
                                  color: Colours.classAdaptiveTextColour(
                                    context,
                                  ),
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Right Icons
                        GestureDetector(
                          onTap: () => context.go('/search'),
                          child: Icon(
                            Icons.search,
                            color: Colours.classAdaptiveTextColour(context),
                            size: 24,
                          ),
                        ),
                        SizedBox(width: 16),
                        GestureDetector(
                          onTap: () => context.go('/favorites'),
                          child: Stack(
                            children: [
                              Icon(
                                Icons.favorite_outline,
                                color: Colours.classAdaptiveTextColour(context),
                                size: 24,
                              ),
                              if (_wishlistItemCount > 0)
                                Positioned(
                                  right: 0,
                                  top: 0,
                                  child: Container(
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    constraints: BoxConstraints(
                                      minWidth: 16,
                                      minHeight: 16,
                                    ),
                                    child: Text(
                                      '$_wishlistItemCount',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        SizedBox(width: 16),
                        GestureDetector(
                          onTap: () => context.go('/cart'),
                          child: Stack(
                            children: [
                              Icon(
                                Icons.shopping_cart_outlined,
                                color: Colours.classAdaptiveTextColour(context),
                                size: 24,
                              ),
                              if (_cartItemCount > 0)
                                Positioned(
                                  right: 0,
                                  top: 0,
                                  child: Container(
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    constraints: BoxConstraints(
                                      minWidth: 16,
                                      minHeight: 16,
                                    ),
                                    child: Text(
                                      '$_cartItemCount',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    // Search Bar
                    GestureDetector(
                      onTap: () {
                        // Navigate to search page when search bar is tapped
                        context.go('/search');
                      },
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _searchController,
                                readOnly:
                                    true, // Make it read-only so it acts as a button
                                decoration: InputDecoration(
                                  hintText: 'What are you looking for?',
                                  hintStyle: TextStyle(color: Colors.grey[600]),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                  ),
                                ),
                                onTap: () {
                                  // Navigate to search page when text field is tapped
                                  context.go('/search');
                                },
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // Navigate to search page when search icon is tapped
                                final query = _searchController.text.trim();
                                if (query.isNotEmpty) {
                                  context.go('/search?query=$query');
                                } else {
                                  context.go('/search');
                                }
                              },
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(8),
                                    bottomRight: Radius.circular(8),
                                  ),
                                ),
                                child: Icon(Icons.search, color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // App Download Section - Commented out
            // SliverToBoxAdapter(
            //   child: Container(
            //     margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            //     child: Row(
            //       children: [
            //         Expanded(
            //           child: Container(
            //             height: 50,
            //             decoration: BoxDecoration(
            //               color: Colors.black,
            //               borderRadius: BorderRadius.circular(8),
            //               border: Border.all(color: Colors.white, width: 1),
            //             ),
            //             child: Row(
            //               mainAxisAlignment: MainAxisAlignment.center,
            //               children: [
            //                 Icon(Icons.android, color: Colors.white, size: 20),
            //                 SizedBox(width: 8),
            //                 Column(
            //                   mainAxisAlignment: MainAxisAlignment.center,
            //                   crossAxisAlignment: CrossAxisAlignment.start,
            //                   children: [
            //                     Text(
            //                       'ANDROID APP ON',
            //                       style: TextStyle(
            //                         color: Colors.white,
            //                         fontSize: 10,
            //                         fontWeight: FontWeight.w400,
            //                       ),
            //                     ),
            //                     Text(
            //                       'Google Play',
            //                       style: TextStyle(
            //                         color: Colors.white,
            //                         fontSize: 14,
            //                         fontWeight: FontWeight.w600,
            //                       ),
            //                     ),
            //                   ],
            //                 ),
            //               ],
            //             ),
            //           ),
            //         ),
            //         SizedBox(width: 12),
            //         Expanded(
            //           child: Container(
            //             height: 50,
            //             decoration: BoxDecoration(
            //               color: Colors.black,
            //               borderRadius: BorderRadius.circular(8),
            //               border: Border.all(color: Colors.white, width: 1),
            //             ),
            //             child: Row(
            //               mainAxisAlignment: MainAxisAlignment.center,
            //               children: [
            //                 Icon(Icons.apple, color: Colors.white, size: 20),
            //                 SizedBox(width: 8),
            //                 Column(
            //                   mainAxisAlignment: MainAxisAlignment.center,
            //                   crossAxisAlignment: CrossAxisAlignment.start,
            //                   children: [
            //                     Text(
            //                       'Download on the',
            //                       style: TextStyle(
            //                         color: Colors.white,
            //                         fontSize: 10,
            //                         fontWeight: FontWeight.w400,
            //                       ),
            //                     ),
            //                     Text(
            //                       'App Store',
            //                       style: TextStyle(
            //                         color: Colors.white,
            //                         fontSize: 14,
            //                         fontWeight: FontWeight.w600,
            //                       ),
            //                     ),
            //                   ],
            //                 ),
            //               ],
            //             ),
            //           ),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),

            // Top Categories Section
            SliverToBoxAdapter(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                constraints: BoxConstraints(minHeight: 350),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colors.white,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: CoreUtils.adaptiveColour(
                      context,
                      lightModeColour: Color(0xFFe53e3e).withValues(alpha: 0.3),
                      darkModeColour: Color(0xFFe53e3e).withValues(alpha: 0.8),
                    ),
                    width: 2.0,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 20,
                      offset: Offset(0, 8),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Top Categories Header with Beautiful Design
                    Container(
                      width: double.infinity,
                      height: 55,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFe53e3e),
                            Color(0xFFff6b6b),
                            Color(0xFFe53e3e),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xFFe53e3e).withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // Decorative circles
                          Positioned(
                            right: -20,
                            top: -20,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                          Positioned(
                            left: -30,
                            bottom: -30,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.08),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                          // Title
                          Center(
                            child: Text(
                              'Top Categories',
                              style: TextStyles.headingSemiBold1.copyWith(
                                color: Colors.white,
                                fontSize: 26,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10),
                    // Dynamic Categories Grid
                    if (_categories.isNotEmpty) ...[
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        child: GridView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: 0.75,
                              ),
                          itemCount:
                              _categories.length >= 7 ? 8 : _categories.length,
                          itemBuilder: (context, index) {
                            if (index < _categories.length && index < 7) {
                              return _buildCategoryItem(_categories[index]);
                            } else {
                              // Show "More" button as the 8th item when we have 7+ categories
                              return _buildMoreCategoriesItem();
                            }
                          },
                        ),
                      ),
                    ] else ...[
                      // Loading state for categories
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        child: GridView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 15,
                                childAspectRatio: 0.75,
                              ),
                          itemCount: 8,
                          itemBuilder: (context, index) {
                            return _buildCategoryLoadingItem();
                          },
                        ),
                      ),
                    ],
                    SizedBox(height: 35),
                  ],
                ),
              ),
            ),

            /// Dhamaka Deals Section
            SliverToBoxAdapter(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colors.white,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: CoreUtils.adaptiveColour(
                      context,
                      lightModeColour: Color(0xFFe53e3e).withValues(alpha: 0.3),
                      darkModeColour: Color(0xFFe53e3e).withValues(alpha: 0.8),
                    ),
                    width: 2.0,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 20,
                      offset: Offset(0, 8),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    /// Dhamaka Deals Header with Beautiful Design
                    Container(
                      width: double.infinity,
                      height: 55,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFe53e3e),
                            Color(0xFFff6b6b),
                            Color(0xFFe53e3e),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xFFe53e3e).withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          /// Decorative circles
                          Positioned(
                            right: -15,
                            top: -15,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                          Positioned(
                            left: -25,
                            bottom: -25,
                            child: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.08),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),

                          /// Title
                          Center(
                            child: Text(
                              'Dhamaka Deals',
                              style: TextStyles.headingSemiBold1.copyWith(
                                color: Colors.white,
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 25),

                    /// Deals Grid
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildDealCard(
                              'UNDER',
                              '₹9',
                              Color(0xFFe53e3e),
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: _buildDealCard(
                              'UNDER',
                              '₹19',
                              Color(0xFFe53e3e),
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: _buildDealCard(
                              'UNDER',
                              '₹29',
                              Color(0xFFe53e3e),
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: _buildDealCard(
                              'UNDER',
                              '₹49',
                              Color(0xFFe53e3e),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 25),
                  ],
                ),
              ),
            ),

            // Recommended Section Header
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Recommended',
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // Navigate to all products page
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AllProductsView(),
                          ),
                        );
                      },
                      child: Text(
                        'View More',
                        style: TextStyle(
                          color: Color(0xFFe53e3e),
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Recommended Products Grid
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.65, // Reduced to give more height
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                delegate: SliverChildBuilderDelegate((context, index) {
                  final product = _recommendedProducts[index];
                  return _buildRecommendedProductCard(product);
                }, childCount: _recommendedProducts.length),
              ),
            ),

            // Featured Products Section
            if (_featuredProducts.isNotEmpty) ...[
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Featured Products',
                        style: TextStyles.headingSemiBold1.copyWith(
                          color: Colours.classAdaptiveTextColour(context),
                          fontSize: 18,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          // Navigate to all featured products
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AllProductsView(),
                            ),
                          );
                        },
                        child: Text(
                          'View More',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colours.lightThemePrimaryColour,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Featured Products Grid
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.65,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final product = _featuredProducts[index];
                    return _buildRecommendedProductCard(product);
                  }, childCount: _featuredProducts.length),
                ),
              ),
            ],

            // Top Pick By Influencers Section
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Text(
                  'Top Pick By Influencers',
                  style: TextStyles.headingSemiBold1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontSize: 18,
                  ),
                ),
              ),
            ),

            // Influencer Picks or Loading
            if (_isLoading)
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 400,
                  child: LoadingWidget(
                    message: 'Loading products...',
                    size: 100,
                  ),
                ),
              )
            else
              SliverToBoxAdapter(
                child: Container(
                  height: 200,
                  margin: EdgeInsets.symmetric(horizontal: 16),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      return Container(
                        width: 150,
                        margin: EdgeInsets.only(right: 12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colours.lightThemeTintStockColour,
                        ),
                        child: Stack(
                          children: [
                            // Placeholder icon
                            Center(
                              child: Icon(
                                Icons.image_outlined,
                                color: Colours.lightThemeSecondaryTextColour,
                                size: 48,
                              ),
                            ),
                            // Gradient overlay
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                            ),
                            // Content
                            Positioned(
                              bottom: 16,
                              left: 16,
                              right: 16,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (index == 0)
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        'Under ₹20',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  SizedBox(height: 8),
                                  Text(
                                    index == 0
                                        ? 'Premium Collection'
                                        : index == 1
                                        ? 'Traditional Designs'
                                        : 'Modern Styles',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Influencer avatar (for first card)
                            if (index == 0)
                              Positioned(
                                top: 16,
                                left: 16,
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                    color: Colours.lightThemeTintStockColour,
                                  ),
                                  child: Stack(
                                    children: [
                                      // Placeholder icon for avatar
                                      Center(
                                        child: Icon(
                                          Icons.person_outline,
                                          color:
                                              Colours
                                                  .lightThemeSecondaryTextColour,
                                          size: 20,
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 0,
                                        right: 0,
                                        child: Container(
                                          width: 12,
                                          height: 12,
                                          decoration: BoxDecoration(
                                            color: Colors.green,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),

            // Trust Badges Section (Moved to Bottom)
            SliverToBoxAdapter(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFFe53e3e), Color(0xFFff6b6b)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xFFe53e3e).withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildTrustBadge(Icons.people, '1cr+ Happy\nCustomers'),
                    _buildTrustBadge(Icons.security, 'Secure\nPayment'),
                    _buildTrustBadge(Icons.local_offer, 'Lowest\nPrice'),
                  ],
                ),
              ),
            ),

            // Bottom Spacing
            SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  /// Update the isWishlisted property for a product in all product lists
  void _updateProductWishlistStatus(String productId, bool isWishlisted) {
    // Update in recommended products
    for (int i = 0; i < _recommendedProducts.length; i++) {
      if (_recommendedProducts[i].id == productId) {
        _recommendedProducts[i] = _recommendedProducts[i].copyWith(
          isWishlisted: isWishlisted,
        );
        break;
      }
    }

    // Update in featured products
    for (int i = 0; i < _featuredProducts.length; i++) {
      if (_featuredProducts[i].id == productId) {
        _featuredProducts[i] = _featuredProducts[i].copyWith(
          isWishlisted: isWishlisted,
        );
        break;
      }
    }
  }
}
