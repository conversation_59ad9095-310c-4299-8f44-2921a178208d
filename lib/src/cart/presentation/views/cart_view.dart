import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/cart/presentation/widgets/cart_item_widget.dart';
import 'package:ghanshyam_murti_bhandar/src/payment/presentation/views/payment_view.dart' as payment;
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/cart_model.dart';

class CartView extends StatefulWidget {
  const CartView({super.key});

  static const path = '/cart';

  @override
  State<CartView> createState() => _CartViewState();
}

class _CartViewState extends State<CartView> {
  bool _isLoading = true;
  CartModel? _cart;
  List<CartItem> _cartItems = [];

  @override
  void initState() {
    super.initState();
    _loadCartData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh cart data when returning to this page
    _loadCartData();
  }

  Future<void> _loadCartData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await ApiService.instance.cart.getCart();

      if (response.isSuccess && response.data != null) {
        _cart = response.data!;

        // Convert CartItemModel to CartItem for UI compatibility
        _cartItems = _cart!.items.map((item) {
          final imageUrl = item.product.imageUrl;
          debugPrint('🛒 Cart Item: ${item.product.name}');
          debugPrint('🛒 Image URL: "$imageUrl"');
          debugPrint('🛒 Images array: ${item.product.images}');
          debugPrint('🛒 Is valid URL: ${CoreUtils.isValidImageUrl(imageUrl)}');

          return CartItem(
            id: item.id ?? item.product.id,
            name: item.product.name,
            image: imageUrl,
            price: item.price ?? item.product.price,
            quantity: item.quantity,
            size: item.variant,
          );
        }).toList();
      } else {
        _cartItems = [];
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to load cart'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _cartItems = [];
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  double get _subtotal {
    return _cartItems.fold(0, (sum, item) => sum + (item.price * item.quantity));
  }

  double get _shipping {
    return _cartItems.isEmpty ? 0 : 100;
  }

  double get _total {
    return _subtotal + _shipping;
  }

  Future<void> _updateQuantity(String itemId, int newQuantity) async {
    try {
      // Show loading state
      setState(() {
        _isLoading = true;
      });

      // Find the cart item to get the productId
      final cartItem = _cart?.items.firstWhere(
        (item) => (item.id ?? item.product.id) == itemId,
        orElse: () => throw Exception('Cart item not found'),
      );

      final response = await ApiService.instance.cart.updateCartItem(
        productId: cartItem!.product.id,
        quantity: newQuantity,
      );

      if (response.isSuccess && response.data != null) {
        _cart = response.data!;

        // Update UI with new cart data
        _cartItems = _cart!.items.map((item) => CartItem(
          id: item.id ?? item.product.id,
          name: item.product.name,
          image: item.product.imageUrl,
          price: item.price ?? item.product.price,
          quantity: item.quantity,
          size: item.variant,
        )).toList();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Quantity updated'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to update quantity'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating quantity: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _removeItem(String itemId) async {
    try {
      // Show loading state
      setState(() {
        _isLoading = true;
      });

      final response = await ApiService.instance.cart.removeCartItem(itemId);

      if (response.isSuccess && response.data != null) {
        _cart = response.data!;

        // Update UI with new cart data
        _cartItems = _cart!.items.map((item) => CartItem(
          id: item.id ?? item.product.id,
          name: item.product.name,
          image: item.product.imageUrl,
          price: item.price ?? item.product.price,
          quantity: item.quantity,
          size: item.variant,
        )).toList();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Item removed from cart'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to remove item'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error removing item: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _clearCart() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await ApiService.instance.cart.clearCart();

      if (response.isSuccess) {
        _cart = null;
        _cartItems.clear();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Cart cleared'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Failed to clear cart'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _proceedToCheckout() {
    if (_cartItems.isEmpty) return;

    // Convert cart items to payment cart items
    final paymentCartItems = _cartItems.map((item) => payment.CartItem(
      id: item.id,
      name: item.name,
      image: item.image,
      price: item.price,
      quantity: item.quantity,
      category: item.size ?? 'Murti', // Use size as category or default
    )).toList();

    // Calculate totals using the same logic as cart display
    final subtotal = _cartItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
    final shippingCost = _shipping; // Use the same shipping calculation as cart display
    final tax = subtotal * 0.18; // 18% tax

    // Navigate to payment page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => payment.PaymentView(
          cartItems: paymentCartItems,
          subtotal: subtotal,
          shippingCost: shippingCost,
          tax: tax,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Shopping Cart", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        actions: [
          if (_cartItems.isNotEmpty)
            IconButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('Clear Cart'),
                    content: Text('Are you sure you want to remove all items?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _clearCart();
                        },
                        child: Text('Clear All'),
                      ),
                    ],
                  ),
                );
              },
              icon: Icon(Icons.delete_sweep_outlined),
            ),
        ],
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading cart...',
              size: 100,
            )
          : _cartItems.isEmpty
              ? EmptyStateWidget(
                  lottieAsset: Media.emptyCart,
                  title: 'Your cart is empty',
                  description: 'Add some items to your cart to see them here',
                  buttonText: 'Start Shopping',
                  onButtonPressed: () {
                    // Navigate to home or products
                  },
                )
              : Column(
              children: [
                // Cart Items List
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.all(16),
                    itemCount: _cartItems.length,
                    itemBuilder: (context, index) {
                      final item = _cartItems[index];
                      final cartItemModel = _cart!.items[index]; // Get the actual cart item model
                      final availableStock = cartItemModel.product.stock;

                      return CartItemWidget(
                        item: item,
                        availableStock: availableStock,
                        onQuantityChanged: (quantity) {
                          // Only validate stock for increases, allow decreases always
                          if (quantity > item.quantity && quantity > availableStock) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Only $availableStock items available in stock'),
                                backgroundColor: Colors.orange,
                                action: SnackBarAction(
                                  label: 'Set Max',
                                  textColor: Colors.white,
                                  onPressed: () {
                                    _updateQuantity(item.id, availableStock);
                                  },
                                ),
                              ),
                            );
                            return;
                          }
                          _updateQuantity(item.id, quantity);
                        },
                        onRemove: () => _removeItem(item.id),
                      );
                    },
                  ),
                ),
                // Cart Summary
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: CoreUtils.adaptiveColour(
                      context,
                      lightModeColour: Colours.lightThemeWhiteColour,
                      darkModeColour: Colours.darkThemeDarkSharpColor,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: Offset(0, -2),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    child: Column(
                      children: [
                        // Price Breakdown
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Subtotal',
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colours.lightThemeSecondaryTextColour,
                              ),
                            ),
                            Text(
                              '₹${_subtotal.toStringAsFixed(0)}',
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colours.classAdaptiveTextColour(context),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Shipping',
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colours.lightThemeSecondaryTextColour,
                              ),
                            ),
                            Text(
                              '₹${_shipping.toStringAsFixed(0)}',
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colours.classAdaptiveTextColour(context),
                              ),
                            ),
                          ],
                        ),
                        Divider(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total',
                              style: TextStyles.headingSemiBold1.copyWith(
                                color: Colours.classAdaptiveTextColour(context),
                              ),
                            ),
                            Text(
                              '₹${_total.toStringAsFixed(0)}',
                              style: TextStyles.headingSemiBold1.copyWith(
                                color: Colours.lightThemePrimaryColour,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        CustomButton(
                          onPressed: _proceedToCheckout,
                          text: 'Proceed to Checkout',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
