import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class CartItem {
  final String id;
  final String name;
  final String image;
  final double price;
  final int quantity;
  final String? size;
  final String? color;

  CartItem({
    required this.id,
    required this.name,
    required this.image,
    required this.price,
    required this.quantity,
    this.size,
    this.color,
  });
}

class CartItemWidget extends StatelessWidget {
  const CartItemWidget({
    super.key,
    required this.item,
    required this.onQuantityChanged,
    required this.onRemove,
    this.availableStock,
  });

  final CartItem item;
  final Function(int quantity) onQuantityChanged;
  final VoidCallback onRemove;
  final int? availableStock;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colours.lightThemeStockColour, width: 0.5),
      ),
      child: Row(
        children: [
          // Product Image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colours.lightThemeTintStockColour,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  CoreUtils.isValidImageUrl(item.image)
                      ? Image.network(
                        item.image,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colours.lightThemePrimaryColour,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          debugPrint(
                            '🛒 Image load error for ${item.name}: $error',
                          );
                          return Icon(
                            Icons.image_outlined,
                            color: Colours.lightThemeSecondaryTextColour,
                            size: 32,
                          );
                        },
                      )
                      : Icon(
                        Icons.image_outlined,
                        color: Colours.lightThemeSecondaryTextColour,
                        size: 32,
                      ),
            ),
          ),
          SizedBox(width: 12),
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      item.name,
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    IconButton(
                      onPressed: onRemove,
                      icon: Icon(
                        Icons.delete_outline,
                        color: Colors.red,
                        size: 20,
                      ),
                      constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
                SizedBox(height: 4),
                if (item.size != null || item.color != null) ...[
                  Row(
                    children: [
                      if (item.size != null) ...[
                        Text(
                          'Size: ${item.size}',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                        if (item.color != null) SizedBox(width: 12),
                      ],
                      if (item.color != null)
                        Text(
                          'Color: ${item.color}',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '₹${item.price.toStringAsFixed(0)}',
                          style: TextStyles.headingSemiBold1.copyWith(
                            color: Colours.lightThemePrimaryColour,
                          ),
                        ),

                        // Stock indicator - only show if stock is available
                        if (availableStock != null && availableStock! > 0) ...[
                          SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                size: 12,
                                color: Colors.green,
                              ),
                              SizedBox(width: 4),
                              Text(
                                '$availableStock available',
                                style: TextStyles.paragraphSubTextRegular2
                                    .copyWith(
                                      color: Colors.green.shade700,
                                      fontSize: 10,
                                    ),
                              ),
                              if (availableStock! <= 5) ...[
                                SizedBox(width: 6),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 4,
                                    vertical: 1,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                  child: Text(
                                    'Low',
                                    style: TextStyles.paragraphSubTextRegular2
                                        .copyWith(
                                          color: Colors.white,
                                          fontSize: 8,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                    Row(
                      children: [
                        // Quantity Controls
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colours.lightThemeStockColour,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              IconButton(
                                onPressed:
                                    item.quantity > 1
                                        ? () =>
                                            onQuantityChanged(item.quantity - 1)
                                        : null,
                                icon: Icon(
                                  Icons.remove,
                                  size: 16,
                                  color:
                                      item.quantity > 1
                                          ? Colours.classAdaptiveTextColour(
                                            context,
                                          )
                                          : Colours
                                              .lightThemeSecondaryTextColour,
                                ),
                                constraints: BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                                padding: EdgeInsets.zero,
                              ),
                              Container(
                                width: 40,
                                alignment: Alignment.center,
                                child: Text(
                                  '${item.quantity}',
                                  style: TextStyles.paragraphSubTextRegular1
                                      .copyWith(
                                        color: Colours.classAdaptiveTextColour(
                                          context,
                                        ),
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ),
                              IconButton(
                                onPressed:
                                    (availableStock == null ||
                                            item.quantity < availableStock!)
                                        ? () =>
                                            onQuantityChanged(item.quantity + 1)
                                        : null,
                                icon: Icon(
                                  Icons.add,
                                  size: 16,
                                  color:
                                      (availableStock == null ||
                                              item.quantity < availableStock!)
                                          ? Colours.classAdaptiveTextColour(
                                            context,
                                          )
                                          : Colours
                                              .lightThemeSecondaryTextColour,
                                ),
                                constraints: BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),

                        // Remove Button
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
