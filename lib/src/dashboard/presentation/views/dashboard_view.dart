import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/src/dashboard/presentation/utils/dashboard_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/dashboard/presentation/widgets/bottom_navigation_widget.dart';

class DashboardView extends StatefulWidget {
  const DashboardView({super.key, required this.state, required this.child});

  final GoRouterState state;
  final Widget child;

  @override
  State<DashboardView> createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _updateCurrentIndex();
  }

  @override
  void didUpdateWidget(DashboardView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.state.uri.path != widget.state.uri.path) {
      _updateCurrentIndex();
    }
  }

  void _updateCurrentIndex() {
    final path = widget.state.uri.path;
    switch (path) {
      case '/home':
        _currentIndex = 0;
        break;
      case '/search':
        _currentIndex = 1;
        break;
      case '/categories':
        _currentIndex = 2;
        break;
      case '/cart':
        _currentIndex = 3;
        break;
      case '/profile':
        _currentIndex = 4;
        break;
      default:
        _currentIndex = 0;
    }
  }

  void _onTabTapped(int index) {
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/search');
        break;
      case 2:
        context.go('/categories');
        break;
      case 3:
        context.go('/cart');
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: DashboardUtils.scaffoldKey,
      body: widget.child,
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
}
