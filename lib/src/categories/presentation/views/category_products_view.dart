import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/search/presentation/widgets/product_card_widget.dart'
    as search_widgets;
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/category_model.dart' as new_category;
import 'package:ghanshyam_murti_bhandar/src/product_details/presentation/views/product_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/search/presentation/widgets/filter_widgets.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';

class CategoryProductsView extends StatefulWidget {
  final String categoryId;
  final String categoryName;

  const CategoryProductsView({
    super.key,
    required this.categoryId,
    required this.categoryName,
  });

  @override
  State<CategoryProductsView> createState() => _CategoryProductsViewState();
}

class _CategoryProductsViewState extends State<CategoryProductsView> {
  bool _isLoading = true;
  List<ProductModel> _products = [];
  List<new_category.CategoryModel> _subcategories = [];
  String? _error;

  // Filter state
  String? _selectedCategory;

  // Helper function to convert ProductModel to search_widgets.Product
  search_widgets.Product _convertToSearchProduct(ProductModel product) {
    return search_widgets.Product(
      id: product.id,
      name: product.name,
      image: product.images.isNotEmpty ? product.images.first : '',
      price: product.price,
      originalPrice: product.originalPrice,
      category: product.category?.name ?? '',
      rating: product.rating,
      reviewCount: product.reviewCount,
      isInStock: product.stock > 0,
      isFavorite: product.isWishlisted,
    );
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadProducts(),
      _loadSubcategories(),
    ]);
  }

  Future<void> _loadProducts() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Load products for this category (including subcategories)
      final response = await ApiService.instance.products.getProductsByCategory(
        categoryId: widget.categoryId,
        page: 1,
        limit: 50,
      );

      if (response.isSuccess && response.data != null) {
        setState(() {
          _products = response.data!.products;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load products';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading products: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadSubcategories() async {
    try {
      // Load category with its related data (parent and children)
      final response = await ApiService.instance.categories.getCategoryWithRelated(widget.categoryId);

      if (response.isSuccess && response.data != null) {
        final categoryWithRelated = response.data!;

        setState(() {
          // Use children as subcategories
          _subcategories = categoryWithRelated.children;
        });

        // Log category information for debugging
        debugPrint('Category loaded: ${categoryWithRelated.category.name}');
        debugPrint('Has parent: ${categoryWithRelated.hasParent}');
        debugPrint('Children count: ${categoryWithRelated.children.length}');
        if (categoryWithRelated.hasParent) {
          debugPrint('Parent: ${categoryWithRelated.parent!.name}');
        }
      } else {
        // Fallback to the old method if the new one fails
        final fallbackResponse = await ApiService.instance.categories.getSubcategories(widget.categoryId);
        if (fallbackResponse.isSuccess && fallbackResponse.data != null) {
          setState(() {
            _subcategories = fallbackResponse.data!;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading subcategories: $e');
      // Fallback to the old method on exception
      try {
        final fallbackResponse = await ApiService.instance.categories.getSubcategories(widget.categoryId);
        if (fallbackResponse.isSuccess && fallbackResponse.data != null) {
          setState(() {
            _subcategories = fallbackResponse.data!;
          });
        }
      } catch (fallbackError) {
        debugPrint('Fallback also failed: $fallbackError');
      }
    }
  }

  Future<void> _toggleFavorite(String productId) async {
    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(productId);

      if (response.isSuccess && response.data != null) {
        // Reload products to get updated wishlist status
        await _loadProducts();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.data!.wasAdded
                  ? 'Added to favorites'
                  : 'Removed from favorites'),
              backgroundColor: response.data!.wasAdded
                  ? Colors.green
                  : Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addToCart(String productId) async {
    try {
      final response = await ApiService.instance.cart.addToCart(
        productId: productId,
        quantity: 1,
      );

      if (response.isSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added to cart'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View Cart',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pushNamed(context, '/cart');
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Failed to add to cart'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.categoryName.toUpperCase(),
          style: TextStyles.headingSemiBold.copyWith(
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
        bottom: AppBarBottom(),
        centerTitle: false,
        backgroundColor: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading products...',
              size: 100,
            )
          : Column(
              children: [
                // Subcategories header
                Container(
                  height: 70,
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _subcategories.length,
                    itemBuilder: (context, index) {
                      final subcategory = _subcategories[index];
                      return Container(
                        margin: EdgeInsets.only(right: 12),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 80,
                              height: 28,
                              decoration: BoxDecoration(
                                color: CoreUtils.adaptiveColour(
                                  context,
                                  lightModeColour: Color(0xFFF5F5F5),
                                  darkModeColour: Colours.darkThemeDarkSharpColor,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  subcategory.name.split(' ').length > 1
                                      ? subcategory.name.split(' ')[1]
                                      : subcategory.name.split(' ')[0], // Show second word or first if only one
                                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                                    fontSize: 9,
                                    fontWeight: FontWeight.w500,
                                    color: Colours.classAdaptiveTextColour(context),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            SizedBox(height: 3),
                            Flexible(
                              child: SizedBox(
                                width: 80,
                                child: Text(
                                  subcategory.name,
                                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                                    fontSize: 7,
                                    color: Colours.classAdaptiveTextColour(context).withValues(alpha: 0.7),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

                // Filters and Sort
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      // Filters button
                      GestureDetector(
                        onTap: () {
                          _showFilterBottomSheet();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.tune, size: 16),
                              SizedBox(width: 4),
                              Text(
                                'Filters',
                                style: TextStyles.paragraphSubTextRegular1.copyWith(
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),


                    ],
                  ),
                ),

                // Products grid
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: ResponsiveUtils.getGridCrossAxisCount(context),
                        childAspectRatio: ResponsiveUtils.getGridAspectRatio(context),
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: _products.length,
                      itemBuilder: (context, index) {
                        final product = _products[index];
                        final searchProduct = _convertToSearchProduct(product);
                        return search_widgets.ProductCardWidget(
                          product: searchProduct,
                          onTap: () {
                            // Navigate to product details with the ProductModel
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ProductDetailsView(product: product),
                              ),
                            );
                          },
                          onFavoriteToggle: () => _toggleFavorite(product.id),
                          onAddToCart: () => _addToCart(product.id),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => FilterBottomSheet(
        categories: _subcategories.map((cat) => cat.name).toList(),
        selectedCategory: _selectedCategory,
        onApplyFilters: (category) {
          setState(() {
            _selectedCategory = category;
          });
          // Here you would typically filter the products
          // For now, just show a snackbar
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Filters applied'),
              backgroundColor: Colours.lightThemePrimaryColour,
              duration: Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }


}
