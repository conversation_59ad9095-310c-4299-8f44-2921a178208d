import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/categories/presentation/views/category_products_view.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/category_model.dart'
    as new_category;
import 'package:ghanshyam_murti_bhandar/test_category_api.dart';

class CategoryGroup {
  final String id;
  final String name;
  final String icon;
  final List<new_category.CategoryModel> categories;
  final Color backgroundColor;

  CategoryGroup({
    required this.id,
    required this.name,
    required this.icon,
    required this.categories,
    required this.backgroundColor,
  });
}

class CategoriesView extends StatefulWidget {
  const CategoriesView({super.key});

  static const path = '/categories';

  @override
  State<CategoriesView> createState() => _CategoriesViewState();
}

class _CategoriesViewState extends State<CategoriesView> {
  List<new_category.CategoryTreeModel> _parentCategories = [];
  List<new_category.CategoryTreeModel> _subcategories = [];
  bool _isLoading = true;
  String? _error;
  int _selectedParentIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await ApiService.instance.categories.getRootCategories();

      if (response.isSuccess && response.data != null) {
        final categories = response.data!;

        // Convert CategoryModel to CategoryTreeModel for display
        final parentCategories =
            categories
                .map(
                  (cat) => new_category.CategoryTreeModel(
                    id: cat.id,
                    name: cat.name,
                    slug: cat.slug,
                    description: cat.description,
                    image: cat.image,
                    level: 0,
                    path: cat.slug,
                    isFeatured: false,
                    sortOrder: 0,
                    productCount: cat.productCount,
                    hasChildren: cat.subcategories.isNotEmpty,
                    children:
                        cat.subcategories
                            .map(
                              (sub) => new_category.CategoryTreeModel(
                                id: sub.id,
                                name: sub.name,
                                slug: sub.slug,
                                level: 1,
                                path: '${cat.slug}/${sub.slug}',
                                isFeatured: false,
                                sortOrder: 0,
                                productCount: 0,
                                hasChildren: false,
                                children: [],
                              ),
                            )
                            .toList(),
                  ),
                )
                .toList();

        setState(() {
          _parentCategories = parentCategories;
          // Set subcategories for the first parent category
          if (parentCategories.isNotEmpty) {
            _subcategories = parentCategories[0].children;
            _selectedParentIndex = 0;
          }
          _isLoading = false;
        });

        // Load detailed subcategory information including images
        if (parentCategories.isNotEmpty) {
          _loadSubcategoryDetails(parentCategories[0].children);
        }
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load categories';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading categories: $e';
        _isLoading = false;
      });
    }
  }

  /// Load detailed information for subcategories including images
  Future<void> _loadSubcategoryDetails(
    List<new_category.CategoryTreeModel> subcategories,
  ) async {
    try {
      final detailedSubcategories = <new_category.CategoryTreeModel>[];

      for (final subcategory in subcategories) {
        try {
          // Fetch detailed category information using getCategoryById
          final response = await ApiService.instance.categories.getCategoryById(
            subcategory.id,
          );

          if (response.isSuccess && response.data != null) {
            final detailedCategory = response.data!;

            debugPrint('Loading details for subcategory: ${subcategory.name}');
            debugPrint('Image URL: ${detailedCategory.image}');

            // Convert CategoryModel to CategoryTreeModel with image
            final detailedTreeModel = new_category.CategoryTreeModel(
              id: detailedCategory.id,
              name: detailedCategory.name,
              slug: detailedCategory.slug,
              description: detailedCategory.description,
              image:
                  detailedCategory.image, // This will have the actual image URL
              level: subcategory.level,
              path: subcategory.path,
              isFeatured: subcategory.isFeatured,
              sortOrder: subcategory.sortOrder,
              productCount: detailedCategory.productCount,
              hasChildren: detailedCategory.subcategories.isNotEmpty,
              children: subcategory.children,
            );

            detailedSubcategories.add(detailedTreeModel);
          } else {
            // If failed to get details, keep the original
            detailedSubcategories.add(subcategory);
          }
        } catch (e) {
          // If error, keep the original
          detailedSubcategories.add(subcategory);
          debugPrint(
            'Error loading details for subcategory ${subcategory.id}: $e',
          );
        }
      }

      // Update the subcategories with detailed information
      setState(() {
        _subcategories = detailedSubcategories;
      });
    } catch (e) {
      debugPrint('Error loading subcategory details: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Categories", style: TextStyles.headingSemiBold),
          bottom: AppBarBottom(),
          centerTitle: true,
          backgroundColor: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
        ),
        body: Center(
          child: CircularProgressIndicator(
            color: Colours.lightThemePrimaryColour,
          ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Categories", style: TextStyles.headingSemiBold),
          bottom: AppBarBottom(),
          centerTitle: true,
          backgroundColor: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                _error!,
                style: TextStyles.paragraphSubTextRegular1,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(onPressed: _loadCategories, child: Text('Retry')),
            ],
          ),
        ),
      );
    }

    if (_parentCategories.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Categories", style: TextStyles.headingSemiBold),
          bottom: AppBarBottom(),
          centerTitle: true,
          backgroundColor: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
        ),
        body: Center(
          child: Text(
            'No categories available',
            style: TextStyles.paragraphSubTextRegular1,
          ),
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Text("Categories", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        backgroundColor: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
      ),
      body: Row(
        children: [
          // Left sidebar with parent categories
          Container(
            width: 100,
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Color(0xFFF8F9FA),
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 8),
              itemCount: _parentCategories.length,
              itemBuilder: (context, index) {
                final category = _parentCategories[index];
                return _buildParentCategoryItem(
                  category,
                  index == _selectedParentIndex,
                  index,
                );
              },
            ),
          ),

          // Thin divider line
          Container(
            width: 0.5,
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colors.grey.withValues(alpha: 0.3),
              darkModeColour: Colors.grey.withValues(alpha: 0.5),
            ),
          ),

          // Right content area with subcategories
          Expanded(
            child: Container(
              color: CoreUtils.adaptiveColour(
                context,
                lightModeColour: Colours.lightThemeWhiteColour,
                darkModeColour: Colours.darkThemeDarkSharpColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with parent category name
                  Container(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      _parentCategories.isNotEmpty
                          ? _parentCategories[_selectedParentIndex].name
                          : 'Categories',
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontSize: 18,
                      ),
                    ),
                  ),

                  // Subcategories grid
                  Expanded(
                    child:
                        _subcategories.isEmpty
                            ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.category_outlined,
                                    size: 64,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'No subcategories available',
                                    style: TextStyles.paragraphSubTextRegular1,
                                  ),
                                  SizedBox(height: 8),
                                  TextButton(
                                    onPressed: () {
                                      // Navigate to parent category products
                                      if (_parentCategories.isNotEmpty) {
                                        final parentCategory =
                                            _parentCategories[_selectedParentIndex];
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    CategoryProductsView(
                                                      categoryId:
                                                          parentCategory.id,
                                                      categoryName:
                                                          parentCategory.name,
                                                    ),
                                          ),
                                        );
                                      }
                                    },
                                    child: Text(
                                      'View Products',
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                            : Padding(
                              padding: EdgeInsets.fromLTRB(12, 8, 12, 16),
                              child: GridView.builder(
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      childAspectRatio: 0.72,
                                      crossAxisSpacing: 8,
                                      mainAxisSpacing: 8,
                                    ),
                                itemCount: _subcategories.length,
                                itemBuilder: (context, index) {
                                  final subcategory = _subcategories[index];
                                  return _buildSubcategoryCard(subcategory);
                                },
                              ),
                            ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SearchCategoryPage()),
          );
        },
        backgroundColor: Colors.red,
        child: const Icon(Icons.search, color: Colors.white),
      ),
    );
  }

  Widget _buildParentCategoryItem(
    new_category.CategoryTreeModel category,
    bool isSelected,
    int index,
  ) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedParentIndex = index;
          _subcategories = category.children;
        });

        // Load detailed subcategory information including images
        _loadSubcategoryDetails(category.children);
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Colors.red.withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isSelected ? Border.all(color: Colors.red, width: 2) : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getCategoryColor(category.name),
                borderRadius: BorderRadius.circular(20),
              ),
              child:
                  category.image != null && category.image!.isNotEmpty
                      ? ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.network(
                          category.image!,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              _getCategoryIcon(category.name),
                              color: Colors.white,
                              size: 20,
                            );
                          },
                        ),
                      )
                      : Icon(
                        _getCategoryIcon(category.name),
                        color: Colors.white,
                        size: 20,
                      ),
            ),
            SizedBox(height: 8),
            Text(
              category.name,
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color:
                    isSelected
                        ? Colors.red
                        : Colours.classAdaptiveTextColour(context),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubcategoryCard(new_category.CategoryTreeModel subcategory) {
    debugPrint(
      'Building subcategory card for: ${subcategory.name}, image: ${subcategory.image}',
    );
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => CategoryProductsView(
                  categoryId: subcategory.id,
                  categoryName: subcategory.name,
                ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: CoreUtils.adaptiveColour(
              context,
              lightModeColour: Color(0xFFe53e3e).withValues(alpha: 0.3),
              darkModeColour: Color(0xFFe53e3e).withValues(alpha: 0.8),
            ),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Category image
              Container(
                height: 50,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
                  color: Colors.grey[200],
                ),
                child:
                    subcategory.image != null && subcategory.image!.isNotEmpty
                        ? ClipRRect(
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(8),
                          ),
                          child: Image.network(
                            subcategory.image!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.category,
                                  color: Colors.grey[400],
                                  size: 24,
                                ),
                              );
                            },
                          ),
                        )
                        : Container(
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.category,
                            color: Colors.grey[400],
                            size: 24,
                          ),
                        ),
              ),

              // Category info
              Container(
                height: 60,
                padding: EdgeInsets.all(6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        subcategory.name,
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.classAdaptiveTextColour(context),
                          fontWeight: FontWeight.w600,
                          fontSize: 13, // Increased from 11 to 13
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'View Products',
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.lightThemePrimaryColour,
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get category icon based on name
  IconData _getCategoryIcon(String categoryName) {
    final name = categoryName.toLowerCase();
    if (name.contains('human') || name.contains('people')) {
      return Icons.person;
    } else if (name.contains('metal') || name.contains('steel')) {
      return Icons.hardware;
    } else if (name.contains('electronics')) {
      return Icons.electrical_services;
    } else if (name.contains('clothing') || name.contains('fashion')) {
      return Icons.checkroom;
    } else if (name.contains('food') || name.contains('grocery')) {
      return Icons.restaurant;
    } else if (name.contains('books') || name.contains('education')) {
      return Icons.book;
    } else if (name.contains('sports') || name.contains('fitness')) {
      return Icons.sports;
    } else if (name.contains('home') || name.contains('furniture')) {
      return Icons.home;
    } else if (name.contains('beauty') || name.contains('cosmetics')) {
      return Icons.face;
    } else if (name.contains('toys') || name.contains('games')) {
      return Icons.toys;
    } else {
      return Icons.category;
    }
  }

  /// Get category color based on name
  Color _getCategoryColor(String categoryName) {
    final name = categoryName.toLowerCase();
    if (name.contains('human') || name.contains('people')) {
      return Colors.blue;
    } else if (name.contains('metal') || name.contains('steel')) {
      return Colors.grey;
    } else if (name.contains('electronics')) {
      return Colors.purple;
    } else if (name.contains('clothing') || name.contains('fashion')) {
      return Colors.pink;
    } else if (name.contains('food') || name.contains('grocery')) {
      return Colors.green;
    } else if (name.contains('books') || name.contains('education')) {
      return Colors.brown;
    } else if (name.contains('sports') || name.contains('fitness')) {
      return Colors.orange;
    } else if (name.contains('home') || name.contains('furniture')) {
      return Colors.teal;
    } else if (name.contains('beauty') || name.contains('cosmetics')) {
      return Colors.red;
    } else if (name.contains('toys') || name.contains('games')) {
      return Colors.yellow;
    } else {
      return Colors.indigo;
    }
  }
}
