import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/src/on_boarding/presentation/on_boarding_info_section.dart';
import 'package:ghanshyam_murti_bhandar/core/services/auth_guard.dart';

class OnBoardingView extends StatefulWidget {
  const OnBoardingView({super.key});

  static const path = '/onboarding';

  @override
  State<OnBoardingView> createState() => _OnBoardingViewState();
}

class _OnBoardingViewState extends State<OnBoardingView> {
  final pageController = PageController();
  int currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Container(
              padding: EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: <PERSON><PERSON>(
                alignment: Alignment.topRight,
                child: TextButton(
                  onPressed: () async {
                    final goRouter = GoRouter.of(context);
                    // Mark onboarding as completed
                    await AuthGuard.instance.markOnboardingCompleted();
                    if (mounted) {
                      goRouter.go('/login');
                    }
                  },
                  child: Text(
                    'Skip',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemeSecondaryTextColour,
                    ),
                  ),
                ),
              ),
            ),
            // Page content
            Expanded(
              child: PageView(
                controller: pageController,
                onPageChanged: (index) {
                  setState(() {
                    currentPage = index;
                  });
                },
                children: const [
                  OnBoardingInfoSection.first(),
                  OnBoardingInfoSection.second(),
                ],
              ),
            ),
            // Bottom section with indicators and buttons
            Container(
              padding: EdgeInsets.fromLTRB(20, 12, 20, 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      2,
                      (index) => Container(
                        margin: EdgeInsets.symmetric(horizontal: 4),
                        width: currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: currentPage == index
                              ? Colours.lightThemePrimaryColour
                              : Colours.lightThemeStockColour,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 20),
                  // Navigation buttons
                  Row(
                    children: [
                      if (currentPage > 0)
                        Expanded(
                          child: CustomButton(
                            onPressed: () {
                              pageController.previousPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            text: 'Previous',
                            isOutlined: true,
                          ),
                        ),
                      if (currentPage > 0) SizedBox(width: 16),
                      Expanded(
                        flex: currentPage == 0 ? 1 : 1,
                        child: CustomButton(
                          onPressed: () async {
                            if (currentPage < 1) {
                              pageController.nextPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            } else {
                              // Complete onboarding and go to login
                              final goRouter = GoRouter.of(context);
                              await AuthGuard.instance.markOnboardingCompleted();
                              if (mounted) {
                                goRouter.go('/login');
                              }
                            }
                          },
                          text: currentPage < 1 ? 'Next' : 'Get Started',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
