import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/src/orders/presentation/views/order_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/payment/presentation/views/payment_view.dart';

/// Demo class to show how to create sample orders and navigate to payment
class DemoOrderPayment {
  
  /// Create a sample order for demonstration
  static Order createSampleOrder() {
    final orderItems = [
      OrderItem(
        id: '1',
        name: 'Gane<PERSON> Murti - Premium Quality Handcrafted',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        price: 2500,
        quantity: 1,
        category: 'Ganesha Murtis',
      ),
      OrderItem(
        id: '2',
        name: '<PERSON> Mu<PERSON>i - Traditional Design',
        image: 'https://images.unsplash.com/photo-1596422846543-75c6fc197f07?w=400',
        price: 3200,
        quantity: 2,
        category: '<PERSON>',
      ),
    ];

    return Order(
      id: 'ORD${DateTime.now().millisecondsSinceEpoch}',
      status: 'Delivered',
      orderDate: DateTime.now().subtract(Duration(days: 10)),
      deliveryDate: DateTime.now().subtract(Duration(days: 3)),
      items: orderItems,
      subtotal: 8900,
      shippingCost: 50,
      tax: 1602, // 18% tax
      total: 10552,
      shippingAddress: 'John Doe\nHouse No. 123, Street Name\nCity, State - 123456\nIndia',
      paymentMethod: 'Razorpay',
      trackingNumber: 'TRK${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  /// Create sample cart items for payment demonstration
  static List<CartItem> createSampleCartItems() {
    return [
      CartItem(
        id: '1',
        name: 'Ganesha Murti - Premium Quality Handcrafted',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        price: 2500,
        quantity: 1,
        category: 'Ganesha Murtis',
      ),
      CartItem(
        id: '2',
        name: 'Krishna Murti - Traditional Design',
        image: 'https://images.unsplash.com/photo-1596422846543-75c6fc197f07?w=400',
        price: 3200,
        quantity: 2,
        category: 'Krishna Murtis',
      ),
      CartItem(
        id: '3',
        name: 'Lakshmi Murti - Golden Finish',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        price: 1800,
        quantity: 1,
        category: 'Lakshmi Murtis',
      ),
    ];
  }

  /// Navigate to order details page with sample data
  static void showSampleOrderDetails(BuildContext context) {
    final sampleOrder = createSampleOrder();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OrderDetailsView(order: sampleOrder),
      ),
    );
  }

  /// Navigate to payment page with sample cart items
  static void showSamplePayment(BuildContext context) {
    final cartItems = createSampleCartItems();
    final subtotal = cartItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
    final shippingCost = 50.0;
    final tax = subtotal * 0.18; // 18% tax

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentView(
          cartItems: cartItems,
          subtotal: subtotal,
          shippingCost: shippingCost,
          tax: tax,
        ),
      ),
    );
  }

  /// Create a demo widget to test the payment and order flow
  static Widget createDemoWidget() {
    return Builder(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: Text('Payment & Order Demo'),
          centerTitle: true,
        ),
        body: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Demo: Payment & Order System',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 40),
              
              // Payment Demo Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => showSamplePayment(context),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Demo: Payment Page',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
              
              SizedBox(height: 20),
              
              // Order Details Demo Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => showSampleOrderDetails(context),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Demo: Order Details Page',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
              
              SizedBox(height: 40),
              
              // Instructions
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Features Included:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('• Razorpay Payment Integration'),
                    Text('• Multiple Payment Methods (UPI, Cards, Net Banking, COD)'),
                    Text('• Order Details with Status Tracking'),
                    Text('• Order Summary & Price Breakdown'),
                    Text('• Shipping Address Management'),
                    Text('• Payment Success/Failure Handling'),
                    Text('• Order History & Invoice Download'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Extension to add demo functionality to any widget
extension DemoExtension on Widget {
  Widget withPaymentDemo() {
    return Builder(
      builder: (context) => Stack(
        children: [
          this,
          Positioned(
            top: 100,
            right: 20,
            child: FloatingActionButton(
              mini: true,
              onPressed: () => DemoOrderPayment.showSamplePayment(context),
              child: Icon(Icons.payment),
              tooltip: 'Demo Payment',
            ),
          ),
          Positioned(
            top: 160,
            right: 20,
            child: FloatingActionButton(
              mini: true,
              onPressed: () => DemoOrderPayment.showSampleOrderDetails(context),
              child: Icon(Icons.receipt_long),
              tooltip: 'Demo Order',
            ),
          ),
        ],
      ),
    );
  }
}
