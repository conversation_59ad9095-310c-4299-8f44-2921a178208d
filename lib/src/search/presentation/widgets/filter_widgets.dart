import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class CategoryFilter extends StatelessWidget {
  const CategoryFilter({
    super.key,
    required this.categories,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  final List<String> categories;
  final String? selectedCategory;
  final Function(String?) onCategorySelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: TextStyles.headingSemiBold1.copyWith(
            color: Colours.classAdaptiveTextColour(context),
          ),
        ),
        SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            FilterChip(
              label: Text('All'),
              selected: selectedCategory == null,
              onSelected: (selected) {
                onCategorySelected(selected ? null : selectedCategory);
              },
              selectedColor: Colours.lightThemePrimaryColour.withValues(alpha: 0.2),
              checkmarkColor: Colours.lightThemePrimaryColour,
            ),
            ...categories.map((category) => FilterChip(
              label: Text(category),
              selected: selectedCategory == category,
              onSelected: (selected) {
                onCategorySelected(selected ? category : null);
              },
              selectedColor: Colours.lightThemePrimaryColour.withValues(alpha: 0.2),
              checkmarkColor: Colours.lightThemePrimaryColour,
            )),
          ],
        ),
      ],
    );
  }
}

class PriceRangeFilter extends StatelessWidget {
  const PriceRangeFilter({
    super.key,
    required this.minPrice,
    required this.maxPrice,
    required this.currentRange,
    required this.onRangeChanged,
  });

  final double minPrice;
  final double maxPrice;
  final RangeValues currentRange;
  final Function(RangeValues) onRangeChanged;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Price Range',
          style: TextStyles.headingSemiBold1.copyWith(
            color: Colours.classAdaptiveTextColour(context),
          ),
        ),
        SizedBox(height: 12),
        RangeSlider(
          values: currentRange,
          min: minPrice,
          max: maxPrice,
          divisions: 20,
          activeColor: Colours.lightThemePrimaryColour,
          inactiveColor: Colours.lightThemeStockColour,
          labels: RangeLabels(
            '₹${currentRange.start.round()}',
            '₹${currentRange.end.round()}',
          ),
          onChanged: onRangeChanged,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '₹${currentRange.start.round()}',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
            ),
            Text(
              '₹${currentRange.end.round()}',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class SortFilter extends StatelessWidget {
  const SortFilter({
    super.key,
    required this.sortOptions,
    required this.selectedSort,
    required this.onSortSelected,
  });

  final List<String> sortOptions;
  final String selectedSort;
  final Function(String) onSortSelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort By',
          style: TextStyles.headingSemiBold1.copyWith(
            color: Colours.classAdaptiveTextColour(context),
          ),
        ),
        SizedBox(height: 12),
        ...sortOptions.map((option) => RadioListTile<String>(
          title: Text(
            option,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          value: option,
          groupValue: selectedSort,
          onChanged: (value) {
            if (value != null) onSortSelected(value);
          },
          activeColor: Colours.lightThemePrimaryColour,
          contentPadding: EdgeInsets.zero,
        )),
      ],
    );
  }
}

class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({
    super.key,
    required this.categories,
    required this.selectedCategory,
    required this.onApplyFilters,
  });

  final List<String> categories;
  final String? selectedCategory;
  final Function(String?) onApplyFilters;

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Drag handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filters',
                style: TextStyles.headingMedium3.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.close,
                    size: 20,
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 24),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CategoryFilter(
                    categories: widget.categories,
                    selectedCategory: _selectedCategory,
                    onCategorySelected: (category) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                  ),

                  SizedBox(height: 32),
                ],
              ),
            ),
          ),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategory = null;
                    });
                  },
                  child: Text('Clear All'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: () {
                    widget.onApplyFilters(_selectedCategory);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colours.lightThemePrimaryColour,
                  ),
                  child: Text(
                    'Apply Filters',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }
}
