import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class Product {
  final String id;
  final String name;
  final String image;
  final double price;
  final double? originalPrice;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isInStock;
  final bool isFavorite;

  Product({
    required this.id,
    required this.name,
    required this.image,
    required this.price,
    this.originalPrice,
    required this.category,
    required this.rating,
    required this.reviewCount,
    this.isInStock = true,
    this.isFavorite = false,
  });

  Product copyWith({
    String? id,
    String? name,
    String? image,
    double? price,
    double? originalPrice,
    String? category,
    double? rating,
    int? reviewCount,
    bool? isInStock,
    bool? isFavorite,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isInStock: isInStock ?? this.isInStock,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

class ProductCardWidget extends StatelessWidget {
  const ProductCardWidget({
    super.key,
    required this.product,
    required this.onTap,
    required this.onFavoriteToggle,
    required this.onAddToCart,
  });

  final Product product;
  final VoidCallback onTap;
  final VoidCallback onFavoriteToggle;
  final VoidCallback onAddToCart;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeWhiteColour,
            darkModeColour: Colours.darkThemeDarkSharpColor,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colours.lightThemeTintStockColour,
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      image:
                          CoreUtils.isValidImageUrl(product.image)
                              ? DecorationImage(
                                image: NetworkImage(product.image),
                                fit: BoxFit.cover,
                                onError: (exception, stackTrace) {},
                              )
                              : null,
                    ),
                    child:
                        !CoreUtils.isValidImageUrl(product.image)
                            ? Center(
                              child: Icon(
                                Icons.image_outlined,
                                color: Colours.lightThemeSecondaryTextColour,
                                size: 20,
                              ),
                            )
                            : null,
                  ),
                  // Favorite Button
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                      width: 32,
                      height: 32,
                      child: IconButton(
                        onPressed: onFavoriteToggle,
                        icon: Icon(
                          product.isFavorite
                              ? Icons.favorite
                              : Icons.favorite_outline,
                          color:
                              product.isFavorite
                                  ? Colors.red
                                  : Colours.lightThemeSecondaryTextColour,
                          size: 16,
                        ),
                        constraints: BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                  // Discount Badge
                  if (product.originalPrice != null)
                    Positioned(
                      top: 0,
                      left: 0,
                      child: Container(
                        height: 25,
                        width: 60,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        decoration: BoxDecoration(
                          color: Colours.lightThemeSecondaryColour,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12),
                            bottomRight: Radius.circular(12),
                          ),
                        ),
                        child: Text(
                          '${(((product.originalPrice! - product.price) / product.originalPrice!) * 100).round()}% OFF',
                          style: TextStyles.paragraphSubTextRegular.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),

                  /// Stock Status
                  if (!product.isInStock)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(12),
                          ),
                        ),
                        child: Center(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: Text(
                              'Out of Stock',
                              style: TextStyles.paragraphSubTextRegular2
                                  .copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 7,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Product Details
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.only(
                  left: 10,
                  right: 10,
                  top: 5,
                  bottom: 5,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 2,
                  children: [
                    Text(
                      product.category,
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    Text(
                      product.name,
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colours.lightThemeYellowTint,
                          size: 14,
                        ),
                        SizedBox(width: 1),
                        Text(
                          '${product.rating}',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                            fontSize: 12,
                          ),
                        ),
                        Spacer(),
                        Text(
                          '₹${product.price.toStringAsFixed(0)}',
                          style: TextStyles.headingSemiBold1.copyWith(
                            color: Colours.lightThemePrimaryColour,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    SizedBox(
                      width: double.infinity,
                      height: 32,
                      child: ElevatedButton(
                        onPressed: product.isInStock ? onAddToCart : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              product.isInStock
                                  ? Colours.lightThemePrimaryColour
                                  : Colours.lightThemeSecondaryTextColour,
                          padding: EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          minimumSize: Size.zero,
                          elevation: 0,
                        ),
                        child: Text(
                          product.isInStock ? 'Add to Cart' : 'Out of Stock',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    SizedBox(height: 2),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}