import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_text_field.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/src/product_details/presentation/views/product_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/search/presentation/widgets/filter_widgets.dart';
import 'package:ghanshyam_murti_bhandar/src/search/presentation/widgets/product_card_widget.dart'
    as search_widgets;
import 'package:ghanshyam_murti_bhandar/core/models/product_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/category_model.dart' as new_category;
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';

class SearchView extends StatefulWidget {
  const SearchView({super.key, this.initialCategory, this.initialQuery});

  static const path = '/search';
  final String? initialCategory;
  final String? initialQuery;

  @override
  State<SearchView> createState() => _SearchViewState();
}

class _SearchViewState extends State<SearchView> {
  final _searchController = TextEditingController();
  bool _isSearching = false;
  bool _hasSearched = false;
  String? _selectedCategory;


  List<ProductModel> _searchResults = [];
  List<ProductModel> _allProducts = [];
  List<new_category.CategoryModel> _categories = [];
  bool _isLoadingInitial = true;

  // Helper function to convert ProductModel to search_widgets.Product
  search_widgets.Product _convertToSearchProduct(ProductModel product) {
    return search_widgets.Product(
      id: product.id,
      name: product.name,
      image: product.images.isNotEmpty ? product.images.first : '',
      price: product.price,
      originalPrice: product.originalPrice,
      category: product.category?.name ?? '',
      rating: product.rating,
      reviewCount: product.reviewCount,
      isInStock: product.stock > 0,
      isFavorite: product.isWishlisted,
    );
  }

  @override
  void initState() {
    super.initState();
    _loadInitialData();

    // Handle initial query from URL parameters
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
    }
  }

  Future<void> _loadInitialData() async {
    try {
      setState(() {
        _isLoadingInitial = true;
      });

      // Load categories
      final categoriesResponse = await ApiService.instance.categories.getCategories();
      if (categoriesResponse.isSuccess) {
        _categories = categoriesResponse.data ?? [];
        print('🏷️ Loaded ${_categories.length} categories: ${_categories.map((c) => c.name).join(", ")}');
      }

      // Load all products initially
      final productsResponse = await ApiService.instance.products.getProducts(
        page: 1,
        limit: 50, // Load more products for better search experience
      );

      if (productsResponse.isSuccess) {
        _allProducts = productsResponse.data?.products ?? [];
        _searchResults = List.from(_allProducts);
      }

      // Handle initial category if provided
      if (widget.initialCategory != null) {
        final matchingCategory = _categories.firstWhere(
          (cat) => cat.name.toLowerCase().contains(widget.initialCategory!.toLowerCase()),
          orElse: () => _categories.isNotEmpty ? _categories.first : new_category.CategoryModel(
            id: '',
            name: widget.initialCategory!,
            description: '',
            slug: '',
            productCount: 0,
            subcategories: [],
            createdAt: DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          ),
        );
        _selectedCategory = matchingCategory.name;
      }

      // Perform initial search if query or category is provided
      if (widget.initialQuery != null || widget.initialCategory != null) {
        _performSearch();
      }

    } catch (e) {
      debugPrint('Error loading initial data: $e');
    } finally {
      setState(() {
        _isLoadingInitial = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();

    // If no query and no category, reset to initial state
    if (query.isEmpty && _selectedCategory == null) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // Find category ID if category is selected
      String? categoryId;
      if (_selectedCategory != null) {
        try {
          final selectedCat = _categories.firstWhere(
            (cat) => cat.name == _selectedCategory,
          );
          categoryId = selectedCat.id;
        } catch (e) {
          // Category not found, continue without category filter
          categoryId = null;
        }
      }

      // Use default sorting (no specific sort parameters)
      String? sortBy = null;
      String? sortOrder = null;

      // Call search API
      final response = await ApiService.instance.products.searchProducts(
        query: query,
        page: 1,
        limit: 50,
        category: categoryId,
        sortBy: sortBy,
        sortOrder: sortOrder,
      );

      if (mounted) {
        setState(() {
          _isSearching = false;
          _hasSearched = true;

          if (response.isSuccess) {
            _searchResults = response.data?.products ?? [];
          } else {
            _searchResults = [];
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(response.error ?? 'Search failed'),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false;
          _hasSearched = true;
          _searchResults = [];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Search error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => FilterBottomSheet(
        categories: _categories.map((cat) => cat.name).toList(),
        selectedCategory: _selectedCategory,
        onApplyFilters: (category) {
          setState(() {
            _selectedCategory = category;
          });
          _performSearch();
        },
      ),
    );
  }

  void _toggleFavorite(String productId) {
    setState(() {
      final index = _searchResults.indexWhere((p) => p.id == productId);
      if (index != -1) {
        final product = _searchResults[index];
        _searchResults[index] = product.copyWith(
          isWishlisted: !product.isWishlisted,
        );
      }
    });

    final product = _searchResults.firstWhere((p) => p.id == productId);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          product.isWishlisted ? 'Added to favorites' : 'Removed from favorites',
        ),
        backgroundColor: product.isWishlisted ? Colors.green : Colors.red,
      ),
    );
  }

  Future<void> _addToCart(String productId) async {
    try {
      final product = _searchResults.firstWhere((p) => p.id == productId);

      final response = await ApiService.instance.cart.addToCart(
        productId: productId,
        quantity: 1,
      );

      if (response.isSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${product.name} added to cart'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View Cart',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pushNamed(context, '/cart');
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.error ?? 'Failed to add to cart'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding to cart: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Search", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search Bar and Filters
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: CoreUtils.adaptiveColour(
                context,
                lightModeColour: Colours.lightThemeWhiteColour,
                darkModeColour: Colours.darkThemeDarkSharpColor,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: _searchController,
                        hintText: 'Search for murtis, categories...',
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                        onChanged: (value) {
                          // Perform search on text change with debounce
                          if (value.isNotEmpty) {
                            Future.delayed(Duration(milliseconds: 500), () {
                              if (_searchController.text == value) {
                                _performSearch();
                              }
                            });
                          }
                        },
                      ),
                    ),
                    SizedBox(width: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colours.lightThemePrimaryColour,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: _showFilterBottomSheet,
                        icon: Icon(Icons.tune, color: Colors.white),
                        tooltip: 'Filters',
                      ),
                    ),
                  ],
                ),
                if (_selectedCategory != null) ...[
                  SizedBox(height: 12),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        if (_selectedCategory != null)
                          _buildFilterChip('Category: $_selectedCategory', () {
                            setState(() {
                              _selectedCategory = null;
                            });
                            _performSearch();
                          }),


                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Categories
          if (!_hasSearched)
            Container(
              height: 120,
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Browse Categories',
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                    ),
                  ),
                  SizedBox(height: 12),
                  Expanded(
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _categories.length,
                      itemBuilder: (context, index) {
                        final category = _categories[index];
                        return Container(
                          margin: EdgeInsets.only(right: 12),
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedCategory = category.name;
                              });
                              _performSearch();
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colours.lightThemePrimaryColour
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colours.lightThemePrimaryColour
                                      .withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                category.name,
                                style: TextStyles.paragraphSubTextRegular1
                                    .copyWith(
                                      color: Colours.lightThemePrimaryColour,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          // Search Results
          Expanded(
            child: _isLoadingInitial
                ? LoadingWidget(
                    message: 'Loading categories...',
                    size: 100,
                  )
                : _isSearching
                    ? LoadingWidget(
                        message: 'Searching...',
                        size: 100,
                      )
                    : _hasSearched
                    ? _searchResults.isEmpty
                        ? EmptyStateWidget(
                          lottieAsset: Media.search,
                          title: 'No results found',
                          description: 'Try adjusting your search or filters',
                          buttonText: 'Clear Filters',
                          onButtonPressed: () {
                            setState(() {
                              _selectedCategory = null;
                              _searchController.clear();
                              _hasSearched = false;
                            });
                          },
                        )
                        : Column(
                          children: [
                            // Results Header
                            Container(
                              padding: EdgeInsets.all(16),
                              child: Text(
                                '${_searchResults.length} ${_searchResults.length == 1 ? 'result' : 'results'} found',
                                style: TextStyles.paragraphSubTextRegular1
                                    .copyWith(
                                      color:
                                          Colours
                                              .lightThemeSecondaryTextColour,
                                    ),
                              ),
                            ),
                            // Products Grid
                            Expanded(
                              child: GridView.builder(
                                padding: EdgeInsets.symmetric(horizontal: 12),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: ResponsiveUtils.getGridCrossAxisCount(context),
                                      childAspectRatio: ResponsiveUtils.getGridAspectRatio(context),
                                      crossAxisSpacing: 12,
                                      mainAxisSpacing: 12,
                                    ),
                                itemCount: _searchResults.length,
                                itemBuilder: (context, index) {
                                  final product = _searchResults[index];
                                  final searchProduct = _convertToSearchProduct(product);
                                  return search_widgets.ProductCardWidget(
                                    product: searchProduct,
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ProductDetailsView(product: product),
                                        ),
                                      );
                                    },
                                    onFavoriteToggle:
                                        () => _toggleFavorite(product.id),
                                    onAddToCart: () => _addToCart(product.id),
                                  );
                                },
                              ),
                            ),
                          ],
                        )
                    : EmptyStateWidget(
                      lottieAsset: Media.search,
                      title: 'Start Searching',
                      description:
                          'Search for your favorite murtis or browse categories',
                      height: 200,
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(
          label,
          style: TextStyles.paragraphSubTextRegular2.copyWith(
            color: Colours.lightThemePrimaryColour,
          ),
        ),
        deleteIcon: Icon(
          Icons.close,
          size: 16,
          color: Colours.lightThemePrimaryColour,
        ),
        onDeleted: onRemove,
        backgroundColor: Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
        side: BorderSide(
          color: Colours.lightThemePrimaryColour.withValues(alpha: 0.3),
        ),
      ),
    );
  }
}
