import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';

class PaymentResult {
  final bool isSuccess;
  final String? paymentId;
  final String? orderId;
  final String? signature;
  final String? errorMessage;

  PaymentResult({
    required this.isSuccess,
    this.paymentId,
    this.orderId,
    this.signature,
    this.errorMessage,
  });
}

class RazorpayService {
  static final RazorpayService _instance = RazorpayService._internal();
  factory RazorpayService() => _instance;
  RazorpayService._internal();

  late Razorpay _razorpay;
  Function(PaymentResult)? _onPaymentResult;

  void initialize() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void dispose() {
    _razorpay.clear();
  }

  Future<void> startPayment({
    required double amount,
    required String orderId,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String description,
    required Function(PaymentResult) onPaymentResult,
  }) async {
    _onPaymentResult = onPaymentResult;

    var options = {
      'key': 'rzp_test_1DP5mmOlF5G5ag', // Replace with your Razorpay key
      'amount': (amount * 100).toInt(), // Amount in paise
      'name': 'Ghanshyam Murti Bhandar',
      'description': description,
      'order_id': orderId,
      'prefill': {
        'contact': customerPhone,
        'email': customerEmail,
        'name': customerName,
      },
      'theme': {
        'color': '#6366F1', // Your app's primary color
      },
      'modal': {
        'ondismiss': () {
          log('Payment dismissed');
        }
      }
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      log('Error starting payment: $e');
      _onPaymentResult?.call(PaymentResult(
        isSuccess: false,
        errorMessage: 'Failed to start payment: $e',
      ));
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    log('Payment Success: ${response.paymentId}');
    _onPaymentResult?.call(PaymentResult(
      isSuccess: true,
      paymentId: response.paymentId,
      orderId: response.orderId,
      signature: response.signature,
    ));
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    log('Payment Error: ${response.code} - ${response.message}');
    _onPaymentResult?.call(PaymentResult(
      isSuccess: false,
      errorMessage: '${response.message}',
    ));
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    log('External Wallet: ${response.walletName}');
    _onPaymentResult?.call(PaymentResult(
      isSuccess: false,
      errorMessage: 'Payment cancelled - External wallet selected',
    ));
  }

  // Create order on your backend (this is a mock implementation)
  Future<String> createOrder({
    required double amount,
    required String currency,
    required String receipt,
  }) async {
    try {
      // Call the real backend API to create Razorpay order with actual total amount
      final response = await ApiService.instance.payments.createPaymentOrder(
        orderId: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        amount: amount, // This is the real total amount user needs to pay
        currency: currency,
      );

      if (response.isSuccess && response.data != null) {
        // Return the Razorpay order ID from the backend
        return response.data!.razorpayOrderId ?? 'order_${DateTime.now().millisecondsSinceEpoch}';
      } else {
        throw Exception(response.error ?? 'Failed to create payment order');
      }
    } catch (e) {
      log('Error creating Razorpay order: $e');
      throw Exception('Failed to create payment order: $e');
    }
  }

  // Verify payment signature (this should be done on your backend)
  bool verifyPaymentSignature({
    required String paymentId,
    required String orderId,
    required String signature,
    required String keySecret,
  }) {
    // In a real app, this verification should be done on your backend
    // This is just a placeholder
    return true;
  }
}

// Payment method models
class PaymentMethod {
  final String id;
  final String name;
  final String icon;
  final bool isEnabled;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.icon,
    required this.isEnabled,
  });
}

class PaymentMethods {
  static List<PaymentMethod> getAvailableMethods() {
    return [
      PaymentMethod(
        id: 'razorpay',
        name: 'Razorpay',
        icon: '💳',
        isEnabled: true,
      ),
      PaymentMethod(
        id: 'cod',
        name: 'Cash on Delivery',
        icon: '💵',
        isEnabled: true,
      ),
    ];
  }
}
