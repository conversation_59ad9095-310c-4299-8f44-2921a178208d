import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/cart_model.dart';
import 'package:ghanshyam_murti_bhandar/core/models/user_model.dart';

import 'package:ghanshyam_murti_bhandar/src/orders/presentation/views/order_details_view.dart';
import 'package:ghanshyam_murti_bhandar/src/orders/presentation/views/coupon_list_view.dart';
import 'package:ghanshyam_murti_bhandar/src/payment/services/razorpay_service.dart';
import 'package:ghanshyam_murti_bhandar/src/profile/presentation/views/addresses_view.dart';
import 'package:ghanshyam_murti_bhandar/core/models/coupon_model.dart';

class CartItem {
  final String id;
  final String name;
  final String image;
  final double price;
  final int quantity;
  final String category;

  CartItem({
    required this.id,
    required this.name,
    required this.image,
    required this.price,
    required this.quantity,
    required this.category,
  });
}

class PaymentView extends StatefulWidget {
  const PaymentView({
    super.key,
    this.cartItems,
    this.subtotal,
    this.shippingCost,
    this.tax,
  });

  // Optional parameters for backward compatibility
  final List<CartItem>? cartItems;
  final double? subtotal;
  final double? shippingCost;
  final double? tax;

  static const path = '/payment';

  @override
  State<PaymentView> createState() => _PaymentViewState();
}

class _PaymentViewState extends State<PaymentView> {
  final RazorpayService _razorpayService = RazorpayService();
  String _selectedPaymentMethod = 'razorpay';
  bool _isProcessingPayment = false;
  bool _isLoading = true;

  // Real data from APIs
  CartModel? _cart;
  List<CartItem> _cartItems = [];
  UserModel? _currentUser;
  AddressModel? _selectedAddress;

  // Calculated values
  double _subtotal = 0.0;
  double _shippingCost = 100.0; // Default shipping cost
  double _tax = 0.0;

  // Coupon state
  CouponValidationResult? _appliedCoupon;
  double _discountAmount = 0.0;

  double get _total => _subtotal + _shippingCost + _tax - _discountAmount;

  @override
  void initState() {
    super.initState();
    _razorpayService.initialize();
    _loadPaymentData();
  }

  Future<void> _loadPaymentData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load cart data if not provided
      if (widget.cartItems == null) {
        final cartResponse = await ApiService.instance.cart.getCart();
        if (cartResponse.isSuccess && cartResponse.data != null) {
          _cart = cartResponse.data!;
          _cartItems = _cart!.items.map((item) => CartItem(
            id: item.id ?? item.product.id,
            name: item.product.name,
            image: item.product.imageUrl,
            price: item.price ?? item.product.price,
            quantity: item.quantity,
            category: item.product.categoryName,
          )).toList();
          _subtotal = _cart!.calculatedSubtotal;
          _tax = _subtotal * 0.18; // 18% tax
        }
      } else {
        // Use provided cart items
        _cartItems = widget.cartItems!;
        _subtotal = widget.subtotal ?? 0.0;
        _shippingCost = widget.shippingCost ?? 100.0;
        _tax = widget.tax ?? 0.0;
      }

      // Load current user
      _currentUser = await ApiService.instance.user.getCurrentUser();

      // Load user addresses and select default
      if (_currentUser != null) {
        final addressResponse = await ApiService.instance.user.getAddresses();
        if (addressResponse.isSuccess && addressResponse.data != null) {
          final addresses = addressResponse.data!
              .map((addressData) => AddressModel.fromJson(addressData))
              .toList();

          // Find default address or use first one
          if (addresses.isNotEmpty) {
            _selectedAddress = addresses.firstWhere(
              (addr) => addr.isDefault,
              orElse: () => addresses.first,
            );
          }
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load payment data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _razorpayService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Payment", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoading
          ? LoadingWidget(
              message: 'Loading payment details...',
              size: 100,
            )
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Order Summary
                        _buildOrderSummary(),

                        // Shipping Address
                        _buildShippingAddress(),

                        // Apply Coupon
                        _buildApplyCoupon(),

                        // Payment Methods
                        _buildPaymentMethods(),

                        // Price Breakdown
                        _buildPriceBreakdown(),

                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),

                // Payment Button
                _buildPaymentButton(),
              ],
            ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary (${_cartItems.length} items)',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 16),
          ..._cartItems.take(3).map((item) => _buildOrderItem(item)),
          if (_cartItems.length > 3) ...[
            SizedBox(height: 8),
            Text(
              '+${_cartItems.length - 3} more items',
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderItem(CartItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colours.lightThemeTintStockColour,
              borderRadius: BorderRadius.circular(8),
              image: item.image.isNotEmpty
                  ? DecorationImage(
                      image: NetworkImage(item.image),
                      fit: BoxFit.cover,
                      onError: (exception, stackTrace) {},
                    )
                  : null,
            ),
            child: item.image.isEmpty
                ? Icon(
                    Icons.image_outlined,
                    color: Colours.lightThemeSecondaryTextColour,
                    size: 20,
                  )
                : null,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  'Qty: ${item.quantity}',
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '₹${(item.price * item.quantity).toStringAsFixed(0)}',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemePrimaryColour,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingAddress() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colours.lightThemePrimaryColour,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Delivery Address',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              Spacer(),
              TextButton(
                onPressed: _changeAddress,
                child: Text('Change'),
              ),
            ],
          ),
          SizedBox(height: 8),
          if (_selectedAddress != null) ...[
            Text(
              _selectedAddress!.name ?? _currentUser?.name ?? 'No Name',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 4),
            Text(
              _getFormattedAddress(_selectedAddress!),
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
                height: 1.4,
              ),
            ),
            if (_selectedAddress!.phone != null) ...[
              SizedBox(height: 4),
              Text(
                _selectedAddress!.phone!,
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
            ],
          ] else ...[
            Text(
              'No address selected',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _changeAddress,
              child: Text('Add Address'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildApplyCoupon() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: ApplyCouponWidget(
        orderAmount: _subtotal,
        onCouponApplied: _onCouponApplied,
        onCouponRemoved: _onCouponRemoved,
        appliedCoupon: _appliedCoupon,
      ),
    );
  }

  Widget _buildPaymentMethods() {
    final paymentMethods = PaymentMethods.getAvailableMethods();
    
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Method',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 16),
          ...paymentMethods.map((method) => _buildPaymentMethodTile(method)),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethod method) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: RadioListTile<String>(
        title: Row(
          children: [
            Text(
              method.icon,
              style: TextStyle(fontSize: 20),
            ),
            SizedBox(width: 12),
            Text(
              method.name,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
          ],
        ),
        value: method.id,
        groupValue: _selectedPaymentMethod,
        onChanged: method.isEnabled ? (value) {
          setState(() {
            _selectedPaymentMethod = value!;
          });
        } : null,
        activeColor: Colours.lightThemePrimaryColour,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildPriceBreakdown() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Price Details',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 16),
          _buildPriceRow('Subtotal', '₹${_subtotal.toStringAsFixed(0)}'),
          _buildPriceRow('Shipping', '₹${_shippingCost.toStringAsFixed(0)}'),
          _buildPriceRow('Tax', '₹${_tax.toStringAsFixed(0)}'),
          if (_discountAmount > 0) ...[
            _buildPriceRow(
              'Coupon Discount (${_appliedCoupon?.coupon?.code})',
              '-₹${_discountAmount.toStringAsFixed(0)}',
              isDiscount: true,
            ),
          ],
          Divider(height: 24),
          _buildPriceRow(
            'Total Amount',
            '₹${_total.toStringAsFixed(0)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, {bool isTotal = false, bool isDiscount = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w600,
                  )
                : isDiscount
                    ? TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colors.green.shade700,
                      )
                    : TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
          ),
          Text(
            value,
            style: isTotal
                ? TextStyles.headingSemiBold1.copyWith(
                    color: Colours.lightThemePrimaryColour,
                  )
                : isDiscount
                    ? TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w600,
                      )
                    : TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontWeight: FontWeight.w500,
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentButton() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isProcessingPayment ? null : _processPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colours.lightThemePrimaryColour,
              padding: EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isProcessingPayment
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Processing...',
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : Text(
                    'Pay ₹${_total.toStringAsFixed(0)}',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  void _processPayment() async {
    if (_selectedPaymentMethod == 'cod') {
      _processCODPayment();
      return;
    }

    setState(() {
      _isProcessingPayment = true;
    });

    try {
      // Create order
      final orderId = await _razorpayService.createOrder(
        amount: _total,
        currency: 'INR',
        receipt: 'receipt_${DateTime.now().millisecondsSinceEpoch}',
      );

      // Start Razorpay payment
      await _razorpayService.startPayment(
        amount: _total,
        orderId: orderId,
        customerName: _currentUser?.name ?? 'Customer',
        customerEmail: _currentUser?.email ?? '<EMAIL>',
        customerPhone: _selectedAddress?.phone ?? _currentUser?.phone ?? '+91XXXXXXXXXX',
        description: 'Payment for ${_cartItems.length} items',
        onPaymentResult: _handlePaymentResult,
      );
    } catch (e) {
      setState(() {
        _isProcessingPayment = false;
      });
      _showErrorDialog('Failed to initiate payment: $e');
    }
  }

  void _processCODPayment() {
    // For COD, directly create order
    _createOrder('COD', null);
  }

  void _handlePaymentResult(PaymentResult result) {
    setState(() {
      _isProcessingPayment = false;
    });

    if (result.isSuccess) {
      _createOrder('Razorpay', result.paymentId);
    } else {
      _showErrorDialog(result.errorMessage ?? 'Payment failed');
    }
  }

  void _createOrder(String paymentMethod, String? paymentId) async {
    if (_selectedAddress == null) {
      _showErrorDialog('Please select a delivery address');
      return;
    }

    try {
      setState(() {
        _isProcessingPayment = true;
      });

      // Prepare address data according to API specification
      // Split name into firstName and lastName if available
      final fullName = _selectedAddress!.name ?? _currentUser?.name ?? 'Customer';
      final nameParts = fullName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : 'Customer';
      final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      final addressData = {
        'firstName': firstName,
        'lastName': lastName,
        'phone': _selectedAddress!.phone ?? _currentUser?.phone ?? '',
        'addressLine1': _selectedAddress!.addressLine1,
        'city': _selectedAddress!.city,
        'state': _selectedAddress!.state,
        'postalCode': _selectedAddress!.postalCode,
        'country': _selectedAddress!.country,
      };

      // Prepare payment info according to API specification
      final paymentInfoData = {
        'method': paymentMethod.toLowerCase(),
      };

      // Create order using API (cart items are automatically used by the API)
      final orderResponse = await ApiService.instance.orders.createOrder(
        address: addressData,
        paymentInfo: paymentInfoData,
        notes: 'Order placed via mobile app',
      );

      if (orderResponse.isSuccess && orderResponse.data != null) {
        final order = orderResponse.data!;

        // Clear cart after successful order
        await ApiService.instance.cart.clearCart();

        // Navigate to order details (convert OrderModel to Order for UI compatibility)
        debugPrint('🛒 Converting OrderModel to UI Order:');
        debugPrint('🛒 OrderModel - subtotal: ${order.subtotal}, tax: ${order.tax}, shipping: ${order.shipping}, total: ${order.total}');
        debugPrint('🛒 OrderModel - items count: ${order.items.length}');

        final uiOrder = Order(
          id: order.id,
          status: order.status,
          orderDate: order.createdAt,
          deliveryDate: order.createdAt.add(Duration(days: 7)),
          items: order.items.map((item) => OrderItem(
            id: item.id ?? 'unknown',
            name: item.product.name,
            image: item.product.images.isNotEmpty ? item.product.images.first : '',
            price: item.price,
            quantity: item.quantity,
            category: item.product.categoryName,
          )).toList(),
          subtotal: order.subtotal,
          shippingCost: order.shipping,
          tax: order.tax,
          total: order.total,
          shippingAddress: _getFormattedAddress(_selectedAddress!),
          paymentMethod: paymentMethod,
          trackingNumber: order.orderNumber,
        );

        debugPrint('🛒 UI Order - subtotal: ${uiOrder.subtotal}, tax: ${uiOrder.tax}, shipping: ${uiOrder.shippingCost}, total: ${uiOrder.total}');
        debugPrint('🛒 UI Order - items count: ${uiOrder.items.length}');

        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => OrderDetailsView(order: uiOrder),
            ),
          );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Order placed successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showErrorDialog(orderResponse.error ?? 'Failed to create order');
      }
    } catch (e) {
      _showErrorDialog('Error creating order: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });
      }
    }
  }

  void _onCouponApplied(CouponValidationResult validationResult) {
    setState(() {
      _appliedCoupon = validationResult;
      _discountAmount = validationResult.discountAmount;
    });
  }

  void _onCouponRemoved() {
    setState(() {
      _appliedCoupon = null;
      _discountAmount = 0.0;
    });
  }

  void _showErrorDialog(String message) {
    // Check if it's a stock-related error
    final isStockError = message.toLowerCase().contains('insufficient stock') ||
                        message.toLowerCase().contains('stock');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isStockError ? 'Stock Issue' : 'Payment Failed'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (isStockError) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please reduce the quantity or remove the item from your cart.',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (isStockError)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                context.go('/cart');
              },
              child: Text('Go to Cart'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  // Helper method to format address
  String _getFormattedAddress(AddressModel address) {
    final parts = <String>[];
    parts.add(address.addressLine1);
    if (address.addressLine2 != null && address.addressLine2!.isNotEmpty) {
      parts.add(address.addressLine2!);
    }
    parts.add('${address.city}, ${address.state} - ${address.postalCode}');
    parts.add(address.country);
    return parts.join('\n');
  }

  // Navigate to addresses page to change address
  void _changeAddress() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddressesView(),
      ),
    );

    if (result == true) {
      // Reload addresses after returning from addresses page
      _loadPaymentData();
    }
  }
}
