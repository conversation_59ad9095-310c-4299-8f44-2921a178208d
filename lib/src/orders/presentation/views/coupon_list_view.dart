import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/empty_state_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/models/coupon_model.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/logging_service.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/responsive_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

/// Widget for applying coupons in order/checkout screen
class ApplyCouponWidget extends StatefulWidget {
  final double orderAmount;
  final Function(CouponValidationResult)? onCouponApplied;
  final Function()? onCouponRemoved;
  final CouponValidationResult? appliedCoupon;

  const ApplyCouponWidget({
    super.key,
    required this.orderAmount,
    this.onCouponApplied,
    this.onCouponRemoved,
    this.appliedCoupon,
  });

  @override
  State<ApplyCouponWidget> createState() => _ApplyCouponWidgetState();
}

class _ApplyCouponWidgetState extends State<ApplyCouponWidget> {
  final _couponController = TextEditingController();
  bool _isValidating = false;

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  Future<void> _validateAndApplyCoupon() async {
    final couponCode = _couponController.text.trim();
    if (couponCode.isEmpty) return;

    setState(() {
      _isValidating = true;
    });

    try {
      debugPrint('🎫 Widget: Calling validateCoupon API...');

      final response = await ApiService.instance.coupons.validateCoupon(
        couponCode: couponCode,
        orderAmount: widget.orderAmount,
      );

      debugPrint('🎫 Widget: Received response - success: ${response.success}, message: ${response.message}');
      debugPrint('🎫 Widget: Response data: ${response.data}');

      if (mounted) {
        setState(() {
          _isValidating = false;
        });

        if (response.data != null) {
          if (response.data!.isValid) {
            // Coupon is valid
            debugPrint('🎫 Widget: Coupon is valid, applying...');

            if (widget.onCouponApplied != null) {
              widget.onCouponApplied!(response.data!);
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(response.message ?? 'Coupon applied successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // Coupon is invalid - show exact API error message
            final errorMessage = response.message ??
                                response.data!.errorMessage ??
                                'Invalid coupon code';

            debugPrint('🎫 Widget: Coupon is invalid, showing error: $errorMessage');

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else {
          // Fallback for unexpected response format
          final errorMessage = response.message ?? 'Failed to validate coupon';
          debugPrint('🎫 Widget: No response data, showing fallback error: $errorMessage');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isValidating = false;
        });

        // The coupon service should handle all API errors and return proper responses
        // If we reach here, it's likely a network error or unexpected exception
        debugPrint('🎫 Unexpected error in coupon validation: $e');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Network error. Please check your connection.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeCoupon() {
    _couponController.clear();
    if (widget.onCouponRemoved != null) {
      widget.onCouponRemoved!();
    }
  }

  void _showCouponList() async {
    final selectedCoupon = await Navigator.of(context).push<CouponModel>(
      MaterialPageRoute(
        builder: (context) => CouponListView(
          orderAmount: widget.orderAmount,
        ),
      ),
    );

    if (selectedCoupon != null) {
      _couponController.text = selectedCoupon.code;
      _validateAndApplyCoupon();
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasAppliedCoupon = widget.appliedCoupon?.isValid == true;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colors.white,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_offer,
                color: Colours.lightThemePrimaryColour,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Apply Coupon',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),

          if (hasAppliedCoupon) ...[
            // Applied Coupon Display
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.appliedCoupon!.coupon!.code,
                          style: TextStyles.headingSemiBold1.copyWith(
                            color: Colors.green.shade700,
                          ),
                        ),
                        Text(
                          'Saved ₹${widget.appliedCoupon!.discountAmount.toInt()}',
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colors.green.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _removeCoupon,
                    icon: Icon(
                      Icons.close,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Coupon Input
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _couponController,
                    decoration: InputDecoration(
                      hintText: 'Enter coupon code',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    textCapitalization: TextCapitalization.characters,
                  ),
                ),
                SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isValidating ? null : _validateAndApplyCoupon,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colours.lightThemePrimaryColour,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  child: _isValidating
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text('Apply'),
                ),
              ],
            ),
            SizedBox(height: 12),

            // Browse Coupons Button
            TextButton.icon(
              onPressed: _showCouponList,
              icon: Icon(
                Icons.list_alt,
                color: Colours.lightThemePrimaryColour,
                size: 18,
              ),
              label: Text(
                'Browse available coupons',
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemePrimaryColour,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class CouponListView extends StatefulWidget {
  final double orderAmount;
  final Function(CouponModel)? onCouponSelected;

  const CouponListView({
    super.key,
    required this.orderAmount,
    this.onCouponSelected,
  });

  static const path = '/coupons';

  @override
  State<CouponListView> createState() => _CouponListViewState();
}

class _CouponListViewState extends State<CouponListView> {
  bool _isLoading = true;
  List<CouponModel> _coupons = [];
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCoupons();
  }

  Future<void> _loadCoupons() async {
    try {
      Log.ui('Loading available coupons', screen: 'CouponListView');

      final response = await ApiService.instance.coupons.getAvailableCoupons();

      if (response.isSuccess && response.data != null && mounted) {
        Log.ui('Coupons loaded successfully', screen: 'CouponListView', context: {
          'count': response.data!.length,
        });

        setState(() {
          _coupons = response.data!;
          _isLoading = false;
        });
      } else if (mounted) {
        Log.ui('Failed to load coupons', screen: 'CouponListView', context: {
          'error': response.error,
        });

        setState(() {
          _errorMessage = response.error ?? 'Failed to load coupons';
          _isLoading = false;
        });
      }
    } catch (e) {
      Log.e('Error loading coupons', tag: 'COUPON_LIST_VIEW', error: e);

      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load coupons. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  void _selectCoupon(CouponModel coupon) {
    Log.ui('Coupon selected', screen: 'CouponListView', context: {
      'couponCode': coupon.code,
      'discountType': coupon.discountType,
      'discountValue': coupon.discountValue,
    });

    if (widget.onCouponSelected != null) {
      widget.onCouponSelected!(coupon);
    }
    
    Navigator.of(context).pop(coupon);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Available Coupons", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: _isLoading
          ? LoadingWidget()
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyles.paragraphSubTextRegular1,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _isLoading = true;
                            _errorMessage = '';
                          });
                          _loadCoupons();
                        },
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _coupons.isEmpty
                  ? EmptyStateWidget(
                      lottieAsset: Media.search,
                      title: 'No coupons available',
                      description: 'There are no active coupons at the moment',
                    )
                  : ListView.builder(
                      padding: ResponsiveUtils.getResponsivePadding(context),
                      itemCount: _coupons.length,
                      itemBuilder: (context, index) {
                        final coupon = _coupons[index];
                        return _buildCouponCard(coupon);
                      },
                    ),
    );
  }

  Widget _buildCouponCard(CouponModel coupon) {
    final isExpired = !coupon.isValid;
    
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isExpired
            ? CoreUtils.adaptiveColour(
                context,
                lightModeColour: Colors.grey.shade100,
                darkModeColour: Colours.darkThemeDarkSharpColor.withValues(alpha: 0.5),
              )
            : CoreUtils.adaptiveColour(
                context,
                lightModeColour: Colors.white,
                darkModeColour: Colours.darkThemeDarkSharpColor,
              ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isExpired
              ? CoreUtils.adaptiveColour(
                  context,
                  lightModeColour: Colors.grey.shade300,
                  darkModeColour: Colors.grey.shade600,
                )
              : Colours.lightThemePrimaryColour.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isExpired ? null : () => _selectCoupon(coupon),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                // Coupon Icon
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: isExpired
                        ? CoreUtils.adaptiveColour(
                            context,
                            lightModeColour: Colors.grey.shade300,
                            darkModeColour: Colors.grey.shade600,
                          )
                        : Colours.lightThemePrimaryColour.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.local_offer,
                    color: isExpired 
                        ? Colors.grey.shade500 
                        : Colours.lightThemePrimaryColour,
                    size: 28,
                  ),
                ),
                SizedBox(width: 16),
                
                // Coupon Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Coupon Code
                      Text(
                        coupon.code,
                        style: TextStyles.headingSemiBold1.copyWith(
                          color: isExpired 
                              ? Colors.grey.shade600 
                              : Colours.classAdaptiveTextColour(context),
                        ),
                      ),
                      SizedBox(height: 4),
                      
                      // Description
                      Text(
                        coupon.description,
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: isExpired 
                              ? Colors.grey.shade500 
                              : Colours.lightThemeSecondaryTextColour,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8),
                      
                      // Expiry and Discount
                      Row(
                        children: [
                          // Discount Badge
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: isExpired 
                                  ? Colors.grey.shade300 
                                  : Colours.lightThemePrimaryColour,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              coupon.discountText,
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(width: 8),
                          
                          // Expiry Text
                          Expanded(
                            child: Text(
                              coupon.expiryText,
                              style: TextStyles.paragraphSubTextRegular1.copyWith(
                                color: isExpired 
                                    ? Colors.red 
                                    : Colors.orange.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Apply Button or Expired Label
                if (isExpired)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      'Expired',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colours.lightThemePrimaryColour,
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
