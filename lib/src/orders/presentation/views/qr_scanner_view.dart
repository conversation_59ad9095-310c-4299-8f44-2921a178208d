import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/services/qr_service.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/models/order_model.dart';
import 'package:ghanshyam_murti_bhandar/src/orders/presentation/views/order_details_view.dart' as detailed;

class QRScannerView extends StatefulWidget {
  const QRScannerView({super.key});

  static const path = '/qr-scanner';

  @override
  State<QRScannerView> createState() => _QRScannerViewState();
}

class _QRScannerViewState extends State<QRScannerView> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool _isProcessing = false;
  String? _lastScannedCode;

  @override
  void reassemble() {
    super.reassemble();
    if (controller != null) {
      controller!.pauseCamera();
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Scan Order QR", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        backgroundColor: Colours.lightThemeWhiteColour,
      ),
      body: Column(
        children: [
          // Instructions
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  size: 48,
                  color: Colours.lightThemePrimaryColour,
                ),
                SizedBox(height: 12),
                Text(
                  'Scan Order QR Code',
                  style: TextStyles.headingSemiBold1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Point your camera at the QR code on the order receipt to view order details',
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // QR Scanner
          Expanded(
            flex: 4,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colours.lightThemePrimaryColour,
                  width: 2,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(14),
                child: QRView(
                  key: qrKey,
                  onQRViewCreated: _onQRViewCreated,
                  overlay: QrScannerOverlayShape(
                    borderColor: Colours.lightThemePrimaryColour,
                    borderRadius: 16,
                    borderLength: 30,
                    borderWidth: 4,
                    cutOutSize: 250,
                  ),
                ),
              ),
            ),
          ),
          
          // Status and Controls
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (_isProcessing) ...[
                    CircularProgressIndicator(
                      color: Colours.lightThemePrimaryColour,
                    ),
                    SizedBox(height: 12),
                    Text(
                      'Processing QR code...',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                  ] else ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => controller?.toggleFlash(),
                          icon: Icon(Icons.flash_on),
                          label: Text('Flash'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colours.lightThemeStockColour,
                            foregroundColor: Colours.lightThemePrimaryTextColour,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: () => controller?.flipCamera(),
                          icon: Icon(Icons.flip_camera_ios),
                          label: Text('Flip'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colours.lightThemeStockColour,
                            foregroundColor: Colours.lightThemePrimaryTextColour,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      debugPrint('🔍 QR Scanner: Raw scan data received: ${scanData.code}');

      if (!_isProcessing && scanData.code != null && scanData.code != _lastScannedCode) {
        _lastScannedCode = scanData.code;
        debugPrint('🔍 QR Scanner: Processing new QR code: ${scanData.code}');

        // Pause camera to prevent multiple scans
        controller.pauseCamera();

        _handleQRCode(scanData.code!);
      }
    });
  }

  void _handleQRCode(String qrCode) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      debugPrint('🔍 Scanned QR Code: $qrCode');
      
      // Validate QR code format
      final qrInfo = QRService.instance.validateOrderQRData(qrCode);
      
      if (qrInfo == null) {
        debugPrint('🔍 QR Scanner: Invalid QR code format');
        _showErrorDialog('Invalid QR Code', 'This QR code is not a valid order QR code.\n\nScanned: $qrCode');
        _resumeScanning();
        return;
      }

      debugPrint('🔍 QR Scanner: Valid QR code - OrderID: ${qrInfo.orderId}');

      // Fetch order details from API
      final response = await ApiService.instance.orders.getOrder(qrInfo.orderId);
      
      if (response.isSuccess && response.data != null) {
        final order = response.data!;
        
        // Convert to UI order format
        final uiOrder = detailed.Order(
          id: order.id,
          status: order.status,
          orderDate: order.createdAt,
          deliveryDate: order.isDelivered ? order.updatedAt : null,
          items: order.items.map((item) => detailed.OrderItem(
            id: item.id ?? 'unknown',
            name: item.product.name,
            image: item.product.images.isNotEmpty ? item.product.images.first : '',
            price: item.price,
            quantity: item.quantity,
            category: item.product.categoryName,
          )).toList(),
          subtotal: order.subtotal,
          shippingCost: order.shipping,
          tax: order.tax,
          total: order.total,
          shippingAddress: _getFormattedAddress(order.shippingAddress),
          paymentMethod: order.paymentMethod ?? 'Unknown',
          trackingNumber: order.orderNumber,
        );

        // Navigate to order details
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => detailed.OrderDetailsView(order: uiOrder),
            ),
          );
        }
      } else {
        debugPrint('🔍 QR Scanner: Order not found in API');
        _showErrorDialog('Order Not Found', 'Could not find order details. The order may have been deleted or the QR code is invalid.');
        _resumeScanning();
      }
    } catch (e) {
      debugPrint('🔍 Error processing QR code: $e');
      _showErrorDialog('Error', 'An error occurred while processing the QR code: $e');
      _resumeScanning();
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _resumeScanning() {
    // Reset last scanned code and resume camera after a delay
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        _lastScannedCode = null;
        controller?.resumeCamera();
      }
    });
  }

  String _getFormattedAddress(dynamic address) {
    if (address == null) return 'No address provided';
    
    final addressLine1 = address.addressLine1 ?? '';
    final addressLine2 = address.addressLine2 ?? '';
    final city = address.city ?? '';
    final state = address.state ?? '';
    final zipCode = address.zipCode ?? '';
    
    return [addressLine1, addressLine2, city, state, zipCode]
        .where((part) => part.isNotEmpty)
        .join(', ');
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(color: Colours.lightThemePrimaryColour),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
