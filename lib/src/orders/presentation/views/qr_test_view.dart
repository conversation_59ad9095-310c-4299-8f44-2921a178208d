import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/services/qr_service.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class QRTestView extends StatefulWidget {
  const QRTestView({super.key});

  static const path = '/qr-test';

  @override
  State<QRTestView> createState() => _QRTestViewState();
}

class _QRTestViewState extends State<QRTestView> {
  String _testQRData = '';
  String _validationResult = '';

  @override
  void initState() {
    super.initState();
    _generateTestQR();
  }

  void _generateTestQR() {
    // Generate a test QR code
    final testOrderId = 'test_order_123';
    final testOrderNumber = 'ORD_TEST_001';
    
    final qrData = QRService.instance.generateOrderQRData(testOrderId, testOrderNumber);
    
    setState(() {
      _testQRData = qrData;
    });
    
    // Test validation
    _validateQR(qrData);
  }

  void _validateQR(String qrData) {
    final qrInfo = QRService.instance.validateOrderQRData(qrData);
    
    setState(() {
      if (qrInfo != null) {
        _validationResult = 'Valid QR Code!\n'
            'Order ID: ${qrInfo.orderId}\n'
            'Order Number: ${qrInfo.orderNumber}\n'
            'Timestamp: ${qrInfo.timestamp}';
      } else {
        _validationResult = 'Invalid QR Code';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("QR Test", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        backgroundColor: Colours.lightThemeWhiteColour,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'QR Code Test',
              style: TextStyles.headingSemiBold1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 20),
            
            // QR Code Display
            if (_testQRData.isNotEmpty) ...[
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colours.lightThemeStockColour,
                    width: 1,
                  ),
                ),
                child: QrImageView(
                  data: _testQRData,
                  version: QrVersions.auto,
                  size: 200.0,
                  backgroundColor: Colors.white,
                  dataModuleStyle: QrDataModuleStyle(
                    dataModuleShape: QrDataModuleShape.square,
                    color: Colors.black,
                  ),
                  eyeStyle: QrEyeStyle(
                    eyeShape: QrEyeShape.square,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(height: 16),
              
              // QR Data Display
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colours.lightThemeStockColour,
                    width: 0.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'QR Data:',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                    ),
                    SizedBox(height: 8),
                    SelectableText(
                      _testQRData,
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        fontFamily: 'monospace',
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: _testQRData));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('QR data copied to clipboard'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      },
                      icon: Icon(Icons.copy),
                      label: Text('Copy QR Data'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colours.lightThemePrimaryColour,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
              
              // Validation Result
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeWhiteColour,
                    darkModeColour: Colours.darkThemeDarkSharpColor,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _validationResult.startsWith('Valid') 
                        ? Colors.green 
                        : Colors.red,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Validation Result:',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      _validationResult,
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: _validationResult.startsWith('Valid') 
                            ? Colors.green 
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _generateTestQR,
                    child: Text('Generate New QR'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colours.lightThemePrimaryColour,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/qr-scanner');
                    },
                    child: Text('Test Scanner'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colours.lightThemeStockColour,
                      foregroundColor: Colours.lightThemePrimaryTextColour,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            Text(
              'Instructions:\n'
              '1. Generate a test QR code above\n'
              '2. Take a screenshot or scan with another device\n'
              '3. Use "Test Scanner" to scan the QR code\n'
              '4. Check if the scanner recognizes the QR format',
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
