import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class OrderItem {
  final String id;
  final String name;
  final String image;
  final double price;
  final int quantity;
  final String category;

  OrderItem({
    required this.id,
    required this.name,
    required this.image,
    required this.price,
    required this.quantity,
    required this.category,
  });
}

class Order {
  final String id;
  final String status;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final List<OrderItem> items;
  final double subtotal;
  final double shippingCost;
  final double tax;
  final double total;
  final String shippingAddress;
  final String paymentMethod;
  final String? trackingNumber;

  Order({
    required this.id,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    required this.items,
    required this.subtotal,
    required this.shippingCost,
    required this.tax,
    required this.total,
    required this.shippingAddress,
    required this.paymentMethod,
    this.trackingNumber,
  });
}

class OrderDetailsView extends StatefulWidget {
  const OrderDetailsView({
    super.key,
    required this.order,
  });

  final Order order;
  static const path = '/order-details';

  @override
  State<OrderDetailsView> createState() => _OrderDetailsViewState();
}

class _OrderDetailsViewState extends State<OrderDetailsView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Order Details", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Status Header
            _buildOrderStatusHeader(),
            
            // Order Items
            _buildOrderItems(),
            
            // Shipping Information
            _buildShippingInfo(),
            
            // Payment Information
            _buildPaymentInfo(),
            
            // Order Summary
            _buildOrderSummary(),
            
            // Action Buttons
            _buildActionButtons(),
            
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatusHeader() {
    Color statusColor = _getStatusColor(widget.order.status);
    IconData statusIcon = _getStatusIcon(widget.order.status);
    
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  statusIcon,
                  color: statusColor,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order #${widget.order.id}',
                      style: TextStyles.headingSemiBold1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                      ),
                    ),
                    SizedBox(height: 4),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.order.status.toUpperCase(),
                        style: TextStyles.paragraphSubTextRegular2.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Date',
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      _formatDate(widget.order.orderDate),
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.classAdaptiveTextColour(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.order.deliveryDate != null)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Delivery Date',
                        style: TextStyles.paragraphSubTextRegular2.copyWith(
                          color: Colours.lightThemeSecondaryTextColour,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        _formatDate(widget.order.deliveryDate!),
                        style: TextStyles.paragraphSubTextRegular1.copyWith(
                          color: Colours.classAdaptiveTextColour(context),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          if (widget.order.trackingNumber != null) ...[
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: CoreUtils.adaptiveColour(
                  context,
                  lightModeColour: Colours.lightThemeTintStockColour,
                  darkModeColour: Colours.darkThemeDarkSharpColor.withValues(alpha: 0.7),
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: CoreUtils.adaptiveColour(
                    context,
                    lightModeColour: Colours.lightThemeStockColour.withValues(alpha: 0.3),
                    darkModeColour: Colours.lightThemeStockColour.withValues(alpha: 0.5),
                  ),
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.local_shipping,
                    color: Colours.lightThemePrimaryColour,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tracking Number',
                          style: TextStyles.paragraphSubTextRegular2.copyWith(
                            color: Colours.lightThemeSecondaryTextColour,
                          ),
                        ),
                        Text(
                          widget.order.trackingNumber!,
                          style: TextStyles.paragraphSubTextRegular1.copyWith(
                            color: Colours.lightThemePrimaryColour,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Track order functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Opening tracking details...'),
                          backgroundColor: Colours.lightThemePrimaryColour,
                        ),
                      );
                    },
                    child: Text('Track'),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderItems() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Items (${widget.order.items.length})',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 16),
          ...widget.order.items.map((item) => _buildOrderItemCard(item)),
        ],
      ),
    );
  }

  Widget _buildOrderItemCard(OrderItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeTintStockColour,
          darkModeColour: Colours.darkThemeDarkSharpColor.withValues(alpha: 0.7),
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: CoreUtils.adaptiveColour(
            context,
            lightModeColour: Colours.lightThemeStockColour.withValues(alpha: 0.3),
            darkModeColour: Colours.lightThemeStockColour.withValues(alpha: 0.5),
          ),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          // Product Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colours.lightThemeStockColour,
              borderRadius: BorderRadius.circular(8),
              image: item.image.isNotEmpty
                  ? DecorationImage(
                      image: NetworkImage(item.image),
                      fit: BoxFit.cover,
                      onError: (exception, stackTrace) {},
                    )
                  : null,
            ),
            child: item.image.isEmpty
                ? Icon(
                    Icons.image_outlined,
                    color: Colours.lightThemeSecondaryTextColour,
                    size: 24,
                  )
                : null,
          ),
          SizedBox(width: 12),
          
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  item.category,
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'Qty: ${item.quantity}',
                      style: TextStyles.paragraphSubTextRegular2.copyWith(
                        color: Colours.lightThemeSecondaryTextColour,
                      ),
                    ),
                    Spacer(),
                    Text(
                      '₹${(item.price * item.quantity).toStringAsFixed(0)}',
                      style: TextStyles.paragraphSubTextRegular1.copyWith(
                        color: Colours.lightThemePrimaryColour,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingInfo() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colours.lightThemePrimaryColour,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Shipping Address',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Text(
            widget.order.shippingAddress,
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: Colours.lightThemePrimaryColour,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Payment Information',
                style: TextStyles.headingSemiBold1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Text(
                'Payment Method:',
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
              SizedBox(width: 8),
              Text(
                widget.order.paymentMethod,
                style: TextStyles.paragraphSubTextRegular1.copyWith(
                  color: Colours.classAdaptiveTextColour(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 16,
                ),
                SizedBox(width: 4),
                Text(
                  'Payment Successful',
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CoreUtils.adaptiveColour(
          context,
          lightModeColour: Colours.lightThemeWhiteColour,
          darkModeColour: Colours.darkThemeDarkSharpColor,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemeStockColour.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary',
            style: TextStyles.headingSemiBold1.copyWith(
              color: Colours.classAdaptiveTextColour(context),
            ),
          ),
          SizedBox(height: 16),
          _buildSummaryRow('Subtotal', '₹${widget.order.subtotal.toStringAsFixed(0)}'),
          _buildSummaryRow('Shipping', '₹${widget.order.shippingCost.toStringAsFixed(0)}'),
          _buildSummaryRow('Tax', '₹${widget.order.tax.toStringAsFixed(0)}'),
          Divider(height: 24),
          _buildSummaryRow(
            'Total',
            '₹${widget.order.total.toStringAsFixed(0)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w600,
                  )
                : TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
          ),
          Text(
            value,
            style: isTotal
                ? TextStyles.headingSemiBold1.copyWith(
                    color: Colours.lightThemePrimaryColour,
                  )
                : TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colours.classAdaptiveTextColour(context),
                    fontWeight: FontWeight.w500,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          if (widget.order.status == 'Delivered') ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Rate and review functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Rate and review functionality coming soon!'),
                      backgroundColor: Colours.lightThemePrimaryColour,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colours.lightThemePrimaryColour,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Rate & Review',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            SizedBox(height: 12),
          ],
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Download invoice functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Downloading invoice...'),
                        backgroundColor: Colours.lightThemePrimaryColour,
                      ),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(color: Colours.lightThemePrimaryColour),
                  ),
                  child: Text(
                    'Download Invoice',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemePrimaryColour,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Need help functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Opening help center...'),
                        backgroundColor: Colours.lightThemePrimaryColour,
                      ),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(color: Colours.lightThemeSecondaryTextColour),
                  ),
                  child: Text(
                    'Need Help?',
                    style: TextStyles.paragraphSubTextRegular1.copyWith(
                      color: Colours.lightThemeSecondaryTextColour,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.schedule;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'shipped':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
