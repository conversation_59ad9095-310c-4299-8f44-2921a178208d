import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/app_bar_bottom.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';
import 'package:ghanshyam_murti_bhandar/core/services/api/api_service.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';

class WebOrderView extends StatefulWidget {
  const WebOrderView({
    super.key,
    required this.orderId,
    this.verify,
    this.timestamp,
  });

  final String orderId;
  final String? verify;
  final String? timestamp;
  
  static const path = '/web-order';

  @override
  State<WebOrderView> createState() => _WebOrderViewState();
}

class _WebOrderViewState extends State<WebOrderView> {
  Map<String, dynamic>? orderData;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadOrderData();
  }

  Future<void> _loadOrderData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Fetch order data from API
      final response = await ApiService.instance.get('/orders/${widget.orderId}');
      
      if (response.data['success'] == true) {
        setState(() {
          orderData = response.data['data'];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = response.data['message'] ?? 'Order not found';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load order details: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Order Details", style: TextStyles.headingSemiBold),
        bottom: AppBarBottom(),
        centerTitle: true,
        backgroundColor: Colours.lightThemeWhiteColour,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () => context.go('/home'),
            icon: Icon(Icons.home),
            tooltip: 'Go to Home',
          ),
        ],
      ),
      body: isLoading
          ? Center(child: LoadingWidget())
          : errorMessage != null
              ? _buildErrorView()
              : _buildOrderDetails(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              'Order Not Found',
              style: TextStyles.headingSemiBold.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 8),
            Text(
              errorMessage ?? 'Unable to load order details',
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _loadOrderData,
                  icon: Icon(Icons.refresh),
                  label: Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colours.lightThemePrimaryColour,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => context.go('/home'),
                  icon: Icon(Icons.home),
                  label: Text('Go Home'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colours.lightThemeStockColour,
                    foregroundColor: Colours.lightThemePrimaryTextColour,
                  ),
                ),
              ],
            ),
            SizedBox(height: 32),
            _buildAppDownloadSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetails() {
    if (orderData == null) return SizedBox.shrink();

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with app download prompt
          _buildAppDownloadSection(),
          SizedBox(height: 24),
          
          // Order Info
          _buildOrderInfoCard(),
          SizedBox(height: 16),
          
          // Order Items
          _buildOrderItemsCard(),
          SizedBox(height: 16),
          
          // Order Summary
          _buildOrderSummaryCard(),
          SizedBox(height: 16),
          
          // Delivery Info
          _buildDeliveryInfoCard(),
          SizedBox(height: 24),
          
          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildAppDownloadSection() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colours.lightThemePrimaryColour.withOpacity(0.1),
            Colours.lightThemeStockColour.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colours.lightThemePrimaryColour.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.phone_android,
                color: Colours.lightThemePrimaryColour,
                size: 24,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Get the Ghanshyam Murti Bhandar App',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Download our mobile app for a better shopping experience, order tracking, and exclusive offers!',
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: Colours.lightThemeSecondaryTextColour,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Add Play Store link
                    CoreUtils.showSnackBar(context, 'Play Store link coming soon!');
                  },
                  icon: Icon(Icons.android, size: 20),
                  label: Text('Play Store'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Add App Store link
                    CoreUtils.showSnackBar(context, 'App Store link coming soon!');
                  },
                  icon: Icon(Icons.apple, size: 20),
                  label: Text('App Store'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Information',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.bold,
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 12),
            _buildInfoRow('Order ID', orderData!['_id'] ?? 'N/A'),
            _buildInfoRow('Status', orderData!['status'] ?? 'N/A'),
            _buildInfoRow('Order Date', _formatDate(orderData!['createdAt'])),
            if (orderData!['trackingNumber'] != null)
              _buildInfoRow('Tracking Number', orderData!['trackingNumber']),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemsCard() {
    final items = orderData!['items'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Items (${items.length})',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.bold,
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 12),
            ...items.map((item) => _buildOrderItem(item)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Product Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[200],
            ),
            child: item['product']?['image'] != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      item['product']['image'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(Icons.image_not_supported, color: Colors.grey);
                      },
                    ),
                  )
                : Icon(Icons.image_not_supported, color: Colors.grey),
          ),
          SizedBox(width: 12),
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['product']?['name'] ?? 'Product Name',
                  style: TextStyles.paragraphSubTextRegular1.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colours.classAdaptiveTextColour(context),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Qty: ${item['quantity']} × ₹${item['price']}',
                  style: TextStyles.paragraphSubTextRegular2.copyWith(
                    color: Colours.lightThemeSecondaryTextColour,
                  ),
                ),
              ],
            ),
          ),
          // Total Price
          Text(
            '₹${(item['quantity'] * item['price']).toStringAsFixed(2)}',
            style: TextStyles.paragraphSubTextRegular1.copyWith(
              fontWeight: FontWeight.bold,
              color: Colours.lightThemePrimaryColour,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Summary',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.bold,
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 12),
            _buildSummaryRow('Subtotal', '₹${orderData!['subtotal']?.toStringAsFixed(2) ?? '0.00'}'),
            _buildSummaryRow('Shipping', '₹${orderData!['shippingCost']?.toStringAsFixed(2) ?? '0.00'}'),
            _buildSummaryRow('Tax', '₹${orderData!['tax']?.toStringAsFixed(2) ?? '0.00'}'),
            Divider(),
            _buildSummaryRow(
              'Total',
              '₹${orderData!['totalAmount']?.toStringAsFixed(2) ?? '0.00'}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Information',
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                fontWeight: FontWeight.bold,
                color: Colours.classAdaptiveTextColour(context),
              ),
            ),
            SizedBox(height: 12),
            if (orderData!['shippingAddress'] != null) ...[
              Text(
                'Shipping Address:',
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colours.classAdaptiveTextColour(context),
                ),
              ),
              SizedBox(height: 4),
              Text(
                _formatAddress(orderData!['shippingAddress']),
                style: TextStyles.paragraphSubTextRegular2.copyWith(
                  color: Colours.lightThemeSecondaryTextColour,
                ),
              ),
            ],
            if (orderData!['paymentInfo'] != null) ...[
              SizedBox(height: 12),
              _buildInfoRow('Payment Method', orderData!['paymentInfo']['method'] ?? 'N/A'),
              _buildInfoRow('Payment Status', orderData!['paymentInfo']['status'] ?? 'N/A'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => context.go('/home'),
            icon: Icon(Icons.shopping_bag),
            label: Text('Continue Shopping'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colours.lightThemePrimaryColour,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              // TODO: Add contact support functionality
              CoreUtils.showSnackBar(context, 'Contact support feature coming soon!');
            },
            icon: Icon(Icons.support_agent),
            label: Text('Contact Support'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colours.lightThemePrimaryColour,
              side: BorderSide(color: Colours.lightThemePrimaryColour),
              padding: EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyles.paragraphSubTextRegular2.copyWith(
                color: Colours.classAdaptiveTextColour(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: isTotal 
                  ? Colours.classAdaptiveTextColour(context)
                  : Colours.lightThemeSecondaryTextColour,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyles.paragraphSubTextRegular2.copyWith(
              color: isTotal 
                  ? Colours.lightThemePrimaryColour
                  : Colours.classAdaptiveTextColour(context),
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  String _formatAddress(Map<String, dynamic> address) {
    final parts = <String>[];
    if (address['street'] != null) parts.add(address['street']);
    if (address['city'] != null) parts.add(address['city']);
    if (address['state'] != null) parts.add(address['state']);
    if (address['pincode'] != null) parts.add(address['pincode']);
    return parts.join(', ');
  }
}
