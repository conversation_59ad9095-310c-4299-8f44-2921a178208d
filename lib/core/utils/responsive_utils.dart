import 'package:flutter/material.dart';

/// Utility class for responsive design helpers
class ResponsiveUtils {
  ResponsiveUtils._();

  /// Screen size breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Check if the screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if the screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  /// Check if the screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  /// Check if the screen height is small (useful for landscape or small devices)
  static bool isSmallHeight(BuildContext context) {
    return MediaQuery.of(context).size.height < 700;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(32);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(8);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(12);
    } else {
      return const EdgeInsets.all(16);
    }
  }

  /// Get responsive font size multiplier
  static double getFontSizeMultiplier(BuildContext context) {
    if (isMobile(context)) {
      return isSmallHeight(context) ? 0.85 : 1.0;
    } else if (isTablet(context)) {
      return 1.1;
    } else {
      return 1.2;
    }
  }

  /// Get responsive grid cross axis count for product grids
  static int getGridCrossAxisCount(BuildContext context) {
    if (isMobile(context)) {
      return 2;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 4;
    }
  }

  /// Get responsive grid aspect ratio
  static double getGridAspectRatio(BuildContext context) {
    // Compact aspect ratios for shorter cards
    if (isMobile(context)) {
      return 0.65; // Shorter cards
    } else if (isTablet(context)) {
      return 0.7;
    } else {
      return 0.75;
    }
  }

  /// Get responsive spacing between grid items
  static double getGridSpacing(BuildContext context) {
    if (isMobile(context)) {
      return 6; // Reduced spacing for better fit
    } else if (isTablet(context)) {
      return 10;
    } else {
      return 14;
    }
  }

  /// Get responsive spacing for general use
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final multiplier = getFontSizeMultiplier(context);
    return baseSpacing * multiplier;
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {double baseSize = 24}) {
    final multiplier = getFontSizeMultiplier(context);
    return baseSize * multiplier;
  }

  /// Get responsive border radius
  static double getBorderRadius(BuildContext context, {double baseRadius = 12}) {
    if (isMobile(context)) {
      return baseRadius;
    } else if (isTablet(context)) {
      return baseRadius * 1.2;
    } else {
      return baseRadius * 1.5;
    }
  }

  /// Get responsive container height
  static double getContainerHeight(BuildContext context, {double baseHeight = 200}) {
    if (isSmallHeight(context)) {
      return baseHeight * 0.8;
    } else if (isMobile(context)) {
      return baseHeight;
    } else if (isTablet(context)) {
      return baseHeight * 1.2;
    } else {
      return baseHeight * 1.5;
    }
  }

  /// Get responsive text style with size adjustment
  static TextStyle getResponsiveTextStyle(
    BuildContext context,
    TextStyle baseStyle,
  ) {
    final multiplier = getFontSizeMultiplier(context);
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * multiplier,
    );
  }

  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context) && desktop != null) {
      return desktop;
    } else if (isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }
}
