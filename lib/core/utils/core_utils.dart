import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/extensions/context_extensions.dart';

abstract class CoreUtils {
  const CoreUtils();

  static Color adaptiveColour(
    BuildContext context, {
    required Color lightModeColour,
    required Color darkModeColour,
  }) {
    return context.isDarkMode ? darkModeColour : lightModeColour;
  }

  /// Validate if an image URL is valid and can be loaded
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    final uri = Uri.tryParse(url);
    return uri != null && uri.hasAbsolutePath && (uri.hasScheme);
  }
}
