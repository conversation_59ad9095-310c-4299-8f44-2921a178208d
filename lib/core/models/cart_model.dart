import 'product_model.dart';

/// Cart model for shopping cart functionality
class CartModel {
  final List<CartItemModel> items;
  final double total;
  final int itemCount;
  final double? subtotal;
  final double? tax;
  final double? shipping;
  final double? discount;

  const CartModel({
    this.items = const [],
    required this.total,
    required this.itemCount,
    this.subtotal,
    this.tax,
    this.shipping,
    this.discount,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) {
    final items = (json['items'] as List<dynamic>?)
        ?.map((item) => CartItemModel.fromJson(item))
        .toList() ?? [];

    // Handle new API structure with summary object
    final summary = json['summary'] as Map<String, dynamic>?;

    if (summary != null) {
      // Use summary data from API
      return CartModel(
        items: items,
        total: (summary['total'] ?? 0).toDouble(),
        itemCount: summary['totalItems'] ?? 0,
        subtotal: (summary['subtotal'] ?? 0).toDouble(),
        tax: (summary['tax'] ?? 0).toDouble(),
        shipping: (summary['shipping'] ?? 0).toDouble(),
        discount: summary['discount']?.toDouble(),
      );
    } else {
      // Fallback to old structure or calculate values
      final calculatedSubtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
      final itemCount = items.fold(0, (sum, item) => sum + item.quantity);

      return CartModel(
        items: items,
        total: (json['total'] ?? calculatedSubtotal).toDouble(),
        itemCount: json['itemCount'] ?? itemCount,
        subtotal: json['subtotal']?.toDouble() ?? calculatedSubtotal,
        tax: json['tax']?.toDouble(),
        shipping: json['shipping']?.toDouble(),
        discount: json['discount']?.toDouble(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'total': total,
      'itemCount': itemCount,
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
    };
  }

  CartModel copyWith({
    List<CartItemModel>? items,
    double? total,
    int? itemCount,
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
  }) {
    return CartModel(
      items: items ?? this.items,
      total: total ?? this.total,
      itemCount: itemCount ?? this.itemCount,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
    );
  }

  // Getters
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  double get calculatedSubtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get finalTotal => subtotal ?? calculatedSubtotal;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartModel && 
           other.items.length == items.length &&
           other.total == total;
  }

  @override
  int get hashCode => Object.hash(items.length, total);

  @override
  String toString() {
    return 'CartModel(items: ${items.length}, total: $total, itemCount: $itemCount)';
  }
}

/// Cart item model
class CartItemModel {
  final String? id;
  final ProductModel product;
  final int quantity;
  final String? variant;
  final double? price;

  const CartItemModel({
    this.id,
    required this.product,
    required this.quantity,
    this.variant,
    this.price,
  });

  /// Helper method to parse images from cart product data
  static List<String> _parseProductImages(Map<String, dynamic> productData) {
    // Handle images array (plural)
    if (productData['images'] != null && productData['images'] is List) {
      return (productData['images'] as List).cast<String>();
    }

    // Handle single image (singular) - common in cart API
    if (productData['image'] != null && productData['image'] is String) {
      final imageUrl = productData['image'] as String;
      return imageUrl.isNotEmpty ? [imageUrl] : [];
    }

    return [];
  }

  factory CartItemModel.fromJson(Map<String, dynamic> json) {
    try {
      final product = ProductModel.fromJson(json['product']);
      return CartItemModel(
        id: json['id'] ?? json['_id'],
        product: product,
        quantity: json['quantity'] ?? 1,
        variant: json['variant'],
        price: json['price']?.toDouble() ?? product.price,
      );
    } catch (e) {
      // If ProductModel.fromJson fails, create a minimal product
      final productData = json['product'] as Map<String, dynamic>;
      final product = ProductModel(
        id: productData['_id'] ?? '',
        name: productData['name'] ?? 'Unknown Product',
        description: productData['description'] ?? '',
        price: (productData['price'] ?? 0).toDouble(),
        originalPrice: (productData['originalPrice'] ?? productData['price'] ?? 0).toDouble(),
        images: _parseProductImages(productData),
        stock: productData['stock'] ?? 0,
        rating: (productData['rating'] ?? 0).toDouble(),
        reviewCount: productData['reviewCount'] ?? 0,
        isActive: productData['isActive'] ?? true,
        isFeatured: productData['isFeatured'] ?? false,
        tags: (productData['tags'] as List?)?.cast<String>() ?? [],
      );

      return CartItemModel(
        id: json['id'] ?? json['_id'],
        product: product,
        quantity: json['quantity'] ?? 1,
        variant: json['variant'],
        price: json['price']?.toDouble() ?? product.price,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      if (variant != null) 'variant': variant,
      if (price != null) 'price': price,
    };
  }

  CartItemModel copyWith({
    String? id,
    ProductModel? product,
    int? quantity,
    String? variant,
    double? price,
  }) {
    return CartItemModel(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      variant: variant ?? this.variant,
      price: price ?? this.price,
    );
  }

  // Getters
  double get unitPrice => price ?? product.price;
  double get totalPrice => unitPrice * quantity;
  String get productName => product.name;
  String get productImage => product.imageUrl;
  bool get hasVariant => variant != null && variant!.isNotEmpty;
  bool get isInStock => product.isInStock;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItemModel && 
           other.id == id &&
           other.product.id == product.id;
  }

  @override
  int get hashCode => Object.hash(id, product.id);

  @override
  String toString() {
    return 'CartItemModel(id: $id, product: ${product.name}, quantity: $quantity)';
  }
}

/// Add to cart request model
class AddToCartRequest {
  final String productId;
  final int quantity;
  final String? variant;

  const AddToCartRequest({
    required this.productId,
    this.quantity = 1,
    this.variant,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'quantity': quantity,
      if (variant != null) 'variant': variant,
    };
  }
}

/// Update cart item request model
class UpdateCartItemRequest {
  final int quantity;

  const UpdateCartItemRequest({
    required this.quantity,
  });

  Map<String, dynamic> toJson() {
    return {
      'quantity': quantity,
    };
  }
}

/// Cart summary model for displaying cart totals
class CartSummaryModel {
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final int itemCount;
  final String currency;

  const CartSummaryModel({
    required this.subtotal,
    this.tax = 0.0,
    this.shipping = 0.0,
    this.discount = 0.0,
    required this.total,
    required this.itemCount,
    this.currency = 'INR',
  });

  factory CartSummaryModel.fromCart(CartModel cart) {
    return CartSummaryModel(
      subtotal: cart.subtotal ?? cart.calculatedSubtotal,
      tax: cart.tax ?? 0.0,
      shipping: cart.shipping ?? 0.0,
      discount: cart.discount ?? 0.0,
      total: cart.total,
      itemCount: cart.itemCount,
    );
  }

  factory CartSummaryModel.fromJson(Map<String, dynamic> json) {
    return CartSummaryModel(
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      tax: (json['tax'] ?? 0).toDouble(),
      shipping: (json['shipping'] ?? 0).toDouble(),
      discount: (json['discount'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      itemCount: json['itemCount'] ?? 0,
      currency: json['currency'] ?? 'INR',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'itemCount': itemCount,
      'currency': currency,
    };
  }

  CartSummaryModel copyWith({
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    int? itemCount,
    String? currency,
  }) {
    return CartSummaryModel(
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      itemCount: itemCount ?? this.itemCount,
      currency: currency ?? this.currency,
    );
  }

  // Getters
  bool get hasDiscount => discount > 0;
  bool get hasShipping => shipping > 0;
  bool get hasTax => tax > 0;
  double get savings => discount;
  String get formattedTotal => '₹${total.toStringAsFixed(2)}';
  String get formattedSubtotal => '₹${subtotal.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'CartSummaryModel(subtotal: $subtotal, total: $total, itemCount: $itemCount)';
  }
}
