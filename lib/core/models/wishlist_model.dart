import 'product_model.dart';

/// Wishlist model for user's favorite products
class WishlistModel {
  final List<WishlistItemModel> items;
  final int itemCount;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const WishlistModel({
    this.items = const [],
    required this.itemCount,
    this.createdAt,
    this.updatedAt,
  });

  factory WishlistModel.fromJson(Map<String, dynamic> json) {
    return WishlistModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => WishlistItemModel.fromJson(item))
          .toList() ?? [],
      itemCount: json['itemCount'] ?? 0,
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'itemCount': itemCount,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  WishlistModel copyWith({
    List<WishlistItemModel>? items,
    int? itemCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WishlistModel(
      items: items ?? this.items,
      itemCount: itemCount ?? this.itemCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Getters
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  List<String> get productIds => items.map((item) => item.product.id).toList();
  List<ProductModel> get products => items.map((item) => item.product).toList();

  /// Check if product is in wishlist
  bool containsProduct(String productId) {
    return items.any((item) => item.product.id == productId);
  }

  /// Get wishlist item for specific product
  WishlistItemModel? getItem(String productId) {
    try {
      return items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Add product to wishlist (local operation)
  WishlistModel addProduct(ProductModel product) {
    if (containsProduct(product.id)) return this;
    
    final newItem = WishlistItemModel(
      product: product,
      addedAt: DateTime.now(),
    );
    
    return copyWith(
      items: [...items, newItem],
      itemCount: itemCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Remove product from wishlist (local operation)
  WishlistModel removeProduct(String productId) {
    if (!containsProduct(productId)) return this;
    
    final newItems = items.where((item) => item.product.id != productId).toList();
    
    return copyWith(
      items: newItems,
      itemCount: newItems.length,
      updatedAt: DateTime.now(),
    );
  }

  /// Clear all items (local operation)
  WishlistModel clear() {
    return copyWith(
      items: [],
      itemCount: 0,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistModel && 
           other.items.length == items.length &&
           other.itemCount == itemCount;
  }

  @override
  int get hashCode => Object.hash(items.length, itemCount);

  @override
  String toString() {
    return 'WishlistModel(items: ${items.length}, itemCount: $itemCount)';
  }
}

/// Wishlist item model
class WishlistItemModel {
  final String? id;
  final ProductModel product;
  final DateTime addedAt;
  final String? notes;

  const WishlistItemModel({
    this.id,
    required this.product,
    required this.addedAt,
    this.notes,
  });

  factory WishlistItemModel.fromJson(Map<String, dynamic> json) {
    try {
      return WishlistItemModel(
        id: json['id'] ?? json['_id'],
        product: ProductModel.fromJson(json['product']),
        addedAt: json['addedAt'] != null
            ? DateTime.parse(json['addedAt'])
            : DateTime.now(),
        notes: json['notes'],
      );
    } catch (e) {
      // If ProductModel.fromJson fails, create a minimal product from wishlist data
      final productData = json['product'] as Map<String, dynamic>;
      final product = ProductModel(
        id: productData['_id'] ?? '',
        name: productData['name'] ?? 'Unknown Product',
        description: productData['description'] ?? '',
        price: (productData['price'] ?? 0).toDouble(),
        originalPrice: (productData['originalPrice'] ?? productData['price'] ?? 0).toDouble(),
        images: (productData['images'] as List?)?.cast<String>() ?? [],
        stock: productData['stock'] ?? 10,
        rating: (productData['rating'] ?? 0).toDouble(),
        reviewCount: productData['reviewCount'] ?? 0,
        isActive: productData['isActive'] ?? true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return WishlistItemModel(
        id: json['id'] ?? json['_id'],
        product: product,
        addedAt: json['addedAt'] != null
            ? DateTime.parse(json['addedAt'])
            : DateTime.now(),
        notes: json['notes'],
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'product': product.toJson(),
      'addedAt': addedAt.toIso8601String(),
      if (notes != null) 'notes': notes,
    };
  }

  WishlistItemModel copyWith({
    String? id,
    ProductModel? product,
    DateTime? addedAt,
    String? notes,
  }) {
    return WishlistItemModel(
      id: id ?? this.id,
      product: product ?? this.product,
      addedAt: addedAt ?? this.addedAt,
      notes: notes ?? this.notes,
    );
  }

  // Getters
  String get productName => product.name;
  String get productImage => product.imageUrl;
  double get productPrice => product.price;
  bool get isInStock => product.isInStock;
  bool get hasDiscount => product.hasDiscount;
  String get productId => product.id;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistItemModel && 
           other.id == id &&
           other.product.id == product.id;
  }

  @override
  int get hashCode => Object.hash(id, product.id);

  @override
  String toString() {
    return 'WishlistItemModel(id: $id, product: ${product.name})';
  }
}

/// Add to wishlist request model
class AddToWishlistRequest {
  final String productId;
  final String? notes;

  const AddToWishlistRequest({
    required this.productId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      if (notes != null) 'notes': notes,
    };
  }
}

/// Wishlist summary model
class WishlistSummaryModel {
  final int totalItems;
  final int inStockItems;
  final int outOfStockItems;
  final int onSaleItems;
  final double totalValue;
  final double potentialSavings;

  const WishlistSummaryModel({
    required this.totalItems,
    required this.inStockItems,
    required this.outOfStockItems,
    required this.onSaleItems,
    required this.totalValue,
    required this.potentialSavings,
  });

  factory WishlistSummaryModel.fromWishlist(WishlistModel wishlist) {
    int inStock = 0;
    int outOfStock = 0;
    int onSale = 0;
    double totalValue = 0.0;
    double potentialSavings = 0.0;

    for (final item in wishlist.items) {
      final product = item.product;
      
      if (product.isInStock) {
        inStock++;
      } else {
        outOfStock++;
      }
      
      if (product.hasDiscount) {
        onSale++;
        potentialSavings += (product.originalPrice! - product.price);
      }
      
      totalValue += product.price;
    }

    return WishlistSummaryModel(
      totalItems: wishlist.itemCount,
      inStockItems: inStock,
      outOfStockItems: outOfStock,
      onSaleItems: onSale,
      totalValue: totalValue,
      potentialSavings: potentialSavings,
    );
  }

  factory WishlistSummaryModel.fromJson(Map<String, dynamic> json) {
    return WishlistSummaryModel(
      totalItems: json['totalItems'] ?? 0,
      inStockItems: json['inStockItems'] ?? 0,
      outOfStockItems: json['outOfStockItems'] ?? 0,
      onSaleItems: json['onSaleItems'] ?? 0,
      totalValue: (json['totalValue'] ?? 0).toDouble(),
      potentialSavings: (json['potentialSavings'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalItems': totalItems,
      'inStockItems': inStockItems,
      'outOfStockItems': outOfStockItems,
      'onSaleItems': onSaleItems,
      'totalValue': totalValue,
      'potentialSavings': potentialSavings,
    };
  }

  // Getters
  bool get hasItems => totalItems > 0;
  bool get hasOutOfStockItems => outOfStockItems > 0;
  bool get hasOnSaleItems => onSaleItems > 0;
  bool get hasPotentialSavings => potentialSavings > 0;
  double get averageItemValue => totalItems > 0 ? totalValue / totalItems : 0.0;
  String get formattedTotalValue => '₹${totalValue.toStringAsFixed(2)}';
  String get formattedPotentialSavings => '₹${potentialSavings.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'WishlistSummaryModel(totalItems: $totalItems, totalValue: $totalValue)';
  }
}
