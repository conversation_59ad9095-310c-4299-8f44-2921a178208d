/// User model for authentication and profile management
class UserModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? dateOfBirth;
  final String? gender;
  final String role;
  final List<AddressModel> addresses;
  final List<String> wishlist;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.dateOfBirth,
    this.gender,
    required this.role,
    this.addresses = const [],
    this.wishlist = const [],
    this.createdAt,
    this.updatedAt,
  });
  
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      dateOfBirth: json['dateOfBirth'],
      gender: json['gender'],
      role: json['role'] ?? 'user',
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((address) => AddressModel.fromJson(address))
          .toList() ?? [],
      wishlist: (json['wishlist'] as List<dynamic>?)
          ?.map((item) => item.toString())
          .toList() ?? [],
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt']) 
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'role': role,
      'addresses': addresses.map((address) => address.toJson()).toList(),
      'wishlist': wishlist,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? role,
    List<AddressModel>? addresses,
    List<String>? wishlist,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      role: role ?? this.role,
      addresses: addresses ?? this.addresses,
      wishlist: wishlist ?? this.wishlist,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  bool get isAdmin => role == 'admin';
  bool get isUser => role == 'user';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
  
  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role)';
  }
}

/// Address model for user addresses
class AddressModel {
  final String? id;
  final String? name;
  final String? phone;
  final String addressLine1;
  final String? addressLine2;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final String? type; // 'home', 'office', 'other'
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const AddressModel({
    this.id,
    this.name,
    this.phone,
    required this.addressLine1,
    this.addressLine2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.type,
    this.isDefault = false,
    this.createdAt,
    this.updatedAt,
  });
  
  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      id: json['_id'] ?? json['id'],
      name: json['fullName'] ?? json['name'],
      phone: json['phone'],
      addressLine1: json['street'] ?? json['address_line_1'] ?? json['addressLine1'] ?? '',
      addressLine2: json['landmark'] ?? json['address_line_2'] ?? json['addressLine2'],
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      postalCode: json['postalCode'] ?? json['postal_code'] ?? '',
      country: json['country'] ?? 'India',
      type: json['type'],
      isDefault: json['isDefault'] ?? json['is_default'] ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'])
          : json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (phone != null) 'phone': phone,
      'street': addressLine1,
      if (addressLine2 != null) 'landmark': addressLine2,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'country': country,
      if (type != null) 'type': type,
      'isDefault': isDefault,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }
  
  AddressModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? type,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      type: type ?? this.type,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  String get fullAddress {
    final parts = <String>[
      if (name != null && name!.isNotEmpty) name!,
      addressLine1,
      if (addressLine2 != null && addressLine2!.isNotEmpty) addressLine2!,
      city,
      state,
      postalCode,
      country,
    ];
    return parts.join(', ');
  }

  String get shortAddress {
    return '$city, $state $postalCode';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AddressModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AddressModel(id: $id, addressLine1: $addressLine1, city: $city, state: $state)';
  }
}

/// User profile update request model
class UpdateProfileRequest {
  final String? name;
  final String? email;
  final String? phone;
  
  const UpdateProfileRequest({
    this.name,
    this.email,
    this.phone,
  });
  
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;
    return data;
  }
}

/// Change password request model
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;
  
  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
    };
  }
}

/// Login request model
class LoginRequest {
  final String email;
  final String password;
  
  const LoginRequest({
    required this.email,
    required this.password,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

/// Signup request model
class SignupRequest {
  final String name;
  final String email;
  final String password;
  final String role;
  
  const SignupRequest({
    required this.name,
    required this.email,
    required this.password,
    this.role = 'user',
  });
  
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'password': password,
      'role': role,
    };
  }
}
