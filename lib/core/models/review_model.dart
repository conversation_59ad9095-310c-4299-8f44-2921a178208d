/// Review model for product reviews
class ReviewModel {
  final String id;
  final String productId;
  final String userId;
  final String userName;
  final String? userAvatar;
  final int rating;
  final String comment;
  final String? title;
  final List<String> images;
  final bool isVerifiedPurchase;
  final int helpfulCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ReviewModel({
    required this.id,
    required this.productId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.rating,
    required this.comment,
    this.title,
    this.images = const [],
    this.isVerifiedPurchase = false,
    this.helpfulCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['_id'] ?? json['id'] ?? '',
      productId: json['productId'] ?? json['product_id'] ?? '',
      userId: json['userId'] ?? json['user_id'] ?? '',
      userName: json['userName'] ?? json['user_name'] ?? json['user']?['name'] ?? 'Anonymous',
      userAvatar: json['userAvatar'] ?? json['user_avatar'] ?? json['user']?['avatar'],
      rating: json['rating'] ?? 0,
      comment: json['comment'] ?? json['review'] ?? '',
      title: json['title'],
      images: (json['images'] as List<dynamic>?)?.cast<String>() ?? [],
      isVerifiedPurchase: json['isVerifiedPurchase'] ?? json['is_verified_purchase'] ?? false,
      helpfulCount: json['helpfulCount'] ?? json['helpful_count'] ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt']) ?? DateTime.now()
          : json['created_at'] != null
              ? DateTime.tryParse(json['created_at']) ?? DateTime.now()
              : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt']) ?? DateTime.now()
          : json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at']) ?? DateTime.now()
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'userId': userId,
      'userName': userName,
      if (userAvatar != null) 'userAvatar': userAvatar,
      'rating': rating,
      'comment': comment,
      if (title != null) 'title': title,
      'images': images,
      'isVerifiedPurchase': isVerifiedPurchase,
      'helpfulCount': helpfulCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  ReviewModel copyWith({
    String? id,
    String? productId,
    String? userId,
    String? userName,
    String? userAvatar,
    int? rating,
    String? comment,
    String? title,
    List<String>? images,
    bool? isVerifiedPurchase,
    int? helpfulCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      title: title ?? this.title,
      images: images ?? this.images,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Rating distribution model
class RatingDistributionModel {
  final int rating;
  final int count;
  final double percentage;

  const RatingDistributionModel({
    required this.rating,
    required this.count,
    required this.percentage,
  });

  factory RatingDistributionModel.fromJson(Map<String, dynamic> json) {
    return RatingDistributionModel(
      rating: json['rating'] ?? 0,
      count: json['count'] ?? 0,
      percentage: (json['percentage'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rating': rating,
      'count': count,
      'percentage': percentage,
    };
  }
}

/// Product reviews response model
class ProductReviewsModel {
  final List<ReviewModel> reviews;
  final int totalReviews;
  final int totalPages;
  final int currentPage;
  final List<RatingDistributionModel> ratingDistribution;

  const ProductReviewsModel({
    this.reviews = const [],
    required this.totalReviews,
    required this.totalPages,
    required this.currentPage,
    this.ratingDistribution = const [],
  });

  factory ProductReviewsModel.fromJson(Map<String, dynamic> json) {
    return ProductReviewsModel(
      reviews: (json['data'] as List<dynamic>?)
          ?.map((review) => ReviewModel.fromJson(review))
          .toList() ?? [],
      totalReviews: json['totalReviews'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      ratingDistribution: (json['ratingDistribution'] as List<dynamic>?)
          ?.map((rating) => RatingDistributionModel.fromJson(rating))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': reviews.map((review) => review.toJson()).toList(),
      'totalReviews': totalReviews,
      'totalPages': totalPages,
      'currentPage': currentPage,
      'ratingDistribution': ratingDistribution.map((rating) => rating.toJson()).toList(),
    };
  }

  /// Get average rating from distribution
  double get averageRating {
    if (ratingDistribution.isEmpty) return 0.0;
    
    double totalRating = 0.0;
    int totalCount = 0;
    
    for (final distribution in ratingDistribution) {
      totalRating += distribution.rating * distribution.count;
      totalCount += distribution.count;
    }
    
    return totalCount > 0 ? totalRating / totalCount : 0.0;
  }

  /// Check if there are more pages
  bool get hasMorePages => currentPage < totalPages;

  /// Check if reviews list is empty
  bool get isEmpty => reviews.isEmpty;

  /// Check if reviews list is not empty
  bool get isNotEmpty => reviews.isNotEmpty;
}
