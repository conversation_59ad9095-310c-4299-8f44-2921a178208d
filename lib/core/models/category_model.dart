/// Category model based on the exact API response
class CategoryModel {
  final String id;
  final String name;
  final String? description;
  final String slug;
  final String? image;
  final CategoryParent? parent;
  final int productCount;
  final List<CategorySubcategory> subcategories;
  final String createdAt;
  final String updatedAt;

  const CategoryModel({
    required this.id,
    required this.name,
    this.description,
    required this.slug,
    this.image,
    this.parent,
    required this.productCount,
    this.subcategories = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      slug: json['slug'] ?? '',
      image: json['image'],
      parent: json['parent'] != null ? CategoryParent.fromJson(json['parent']) : null,
      productCount: json['product_count'] ?? 0,
      subcategories: (json['subcategories'] as List<dynamic>?)
          ?.map((sub) => CategorySubcategory.fromJson(sub))
          .toList() ?? [],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'id': id,
      'name': name,
      'description': description,
      'slug': slug,
      'image': image,
      'parent': parent?.toJson(),
      'product_count': productCount,
      'subcategories': subcategories.map((sub) => sub.toJson()).toList(),
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Category with related data (parent and children)
class CategoryWithRelated {
  final CategoryModel category;
  final CategoryModel? parent;
  final List<CategoryModel> children;

  const CategoryWithRelated({
    required this.category,
    this.parent,
    this.children = const [],
  });

  /// Get all related categories (parent + children)
  List<CategoryModel> get allRelated {
    final related = <CategoryModel>[];
    if (parent != null) related.add(parent!);
    related.addAll(children);
    return related;
  }

  /// Check if category has parent
  bool get hasParent => parent != null;

  /// Check if category has children
  bool get hasChildren => children.isNotEmpty;

  /// Get breadcrumb path
  List<CategoryModel> get breadcrumb {
    final breadcrumb = <CategoryModel>[];
    if (parent != null) breadcrumb.add(parent!);
    breadcrumb.add(category);
    return breadcrumb;
  }
}

/// Category tree model for hierarchical structure - matches your exact API
class CategoryTreeModel {
  final String id;
  final String name;
  final String slug;
  final String? description;
  final String? image;
  final String? color;
  final int level;
  final String path;
  final bool isFeatured;
  final int sortOrder;
  final int productCount;
  final bool hasChildren;
  final List<CategoryTreeModel> children;

  const CategoryTreeModel({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.image,
    this.color,
    required this.level,
    required this.path,
    required this.isFeatured,
    required this.sortOrder,
    required this.productCount,
    required this.hasChildren,
    this.children = const [],
  });

  factory CategoryTreeModel.fromJson(Map<String, dynamic> json) {
    return CategoryTreeModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
      description: json['description'],
      image: json['image'],
      color: json['color'] ?? '#000000',
      level: json['level'] ?? 0,
      path: json['path'] ?? '',
      isFeatured: json['is_featured'] ?? false,
      sortOrder: json['sort_order'] ?? 0,
      productCount: json['product_count'] ?? 0,
      hasChildren: json['has_children'] ?? false,
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => CategoryTreeModel.fromJson(child))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'image': image,
      'color': color,
      'level': level,
      'path': path,
      'is_featured': isFeatured,
      'sort_order': sortOrder,
      'product_count': productCount,
      'has_children': hasChildren,
      'children': children.map((child) => child.toJson()).toList(),
    };
  }

  /// Get all subcategories (children) recursively
  List<CategoryTreeModel> getAllSubcategories() {
    final subcategories = <CategoryTreeModel>[];
    for (final child in children) {
      subcategories.add(child);
      subcategories.addAll(child.getAllSubcategories());
    }
    return subcategories;
  }

  /// Check if this category has any subcategories
  bool get hasSubcategories => children.isNotEmpty;

  /// Get direct children only
  List<CategoryTreeModel> get directChildren => children;
}

/// Category tree response model
class CategoryTreeResponse {
  final List<CategoryTreeModel> categories;
  final CategoryTreeMeta meta;

  const CategoryTreeResponse({
    required this.categories,
    required this.meta,
  });

  factory CategoryTreeResponse.fromJson(Map<String, dynamic> json) {
    return CategoryTreeResponse(
      categories: (json['categories'] as List<dynamic>?)
          ?.map((cat) => CategoryTreeModel.fromJson(cat))
          .toList() ?? [],
      meta: CategoryTreeMeta.fromJson(json['meta'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categories': categories.map((cat) => cat.toJson()).toList(),
      'meta': meta.toJson(),
    };
  }
}

/// Category tree meta information
class CategoryTreeMeta {
  final int totalCategories;
  final int totalProducts;
  final int treeDepth;
  final bool featuredOnly;
  final bool includesProducts;

  const CategoryTreeMeta({
    required this.totalCategories,
    required this.totalProducts,
    required this.treeDepth,
    required this.featuredOnly,
    required this.includesProducts,
  });

  factory CategoryTreeMeta.fromJson(Map<String, dynamic> json) {
    return CategoryTreeMeta(
      totalCategories: json['total_categories'] ?? 0,
      totalProducts: json['total_products'] ?? 0,
      treeDepth: json['tree_depth'] ?? 0,
      featuredOnly: json['featured_only'] ?? false,
      includesProducts: json['includes_products'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_categories': totalCategories,
      'total_products': totalProducts,
      'tree_depth': treeDepth,
      'featured_only': featuredOnly,
      'includes_products': includesProducts,
    };
  }
}

/// Category parent model
class CategoryParent {
  final String id;
  final String name;
  final String slug;

  const CategoryParent({
    required this.id,
    required this.name,
    required this.slug,
  });

  factory CategoryParent.fromJson(Map<String, dynamic> json) {
    return CategoryParent(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'id': id,
      'name': name,
      'slug': slug,
    };
  }
}

/// Category subcategory model
class CategorySubcategory {
  final String id;
  final String name;
  final String slug;

  const CategorySubcategory({
    required this.id,
    required this.name,
    required this.slug,
  });

  factory CategorySubcategory.fromJson(Map<String, dynamic> json) {
    return CategorySubcategory(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      slug: json['slug'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'id': id,
      'name': name,
      'slug': slug,
    };
  }
}
