import 'package:flutter/foundation.dart';
import 'product_model.dart';
import 'user_model.dart';

/// Order model for order management
class OrderModel {
  final String id;
  final String orderNumber;
  final String userId;
  final List<OrderItemModel> items;
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final String status;
  final String paymentStatus;
  final AddressModel shippingAddress;
  final AddressModel? billingAddress;
  final String? paymentMethod;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const OrderModel({
    required this.id,
    required this.orderNumber,
    required this.userId,
    required this.items,
    required this.subtotal,
    this.tax = 0.0,
    this.shipping = 0.0,
    this.discount = 0.0,
    required this.total,
    required this.status,
    required this.paymentStatus,
    required this.shippingAddress,
    this.billingAddress,
    this.paymentMethod,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  /// Helper method to extract pricing data from nested pricing object or direct fields
  static double _extractPricing(Map<String, dynamic> json, String field, double defaultValue) {
    double result = defaultValue;

    // First check if there's a pricing object
    if (json['pricing'] != null && json['pricing'] is Map<String, dynamic>) {
      final pricing = json['pricing'] as Map<String, dynamic>;
      if (pricing[field] != null) {
        result = (pricing[field]).toDouble();
        debugPrint('🛒 _extractPricing - Found $field in pricing object: $result');
        return result;
      }
    }

    // Fallback to direct field or alternative field names
    switch (field) {
      case 'subtotal':
        result = (json['subtotal'] ?? json['totalAmount'] ?? defaultValue).toDouble();
        break;
      case 'tax':
        result = (json['tax'] ?? json['taxAmount'] ?? defaultValue).toDouble();
        break;
      case 'shipping':
        result = (json['shipping'] ?? json['shippingCost'] ?? json['shippingAmount'] ?? defaultValue).toDouble();
        break;
      case 'discount':
        result = (json['discount'] ?? json['discountAmount'] ?? defaultValue).toDouble();
        break;
      case 'total':
        result = (json['total'] ?? json['totalAmount'] ?? json['finalAmount'] ?? defaultValue).toDouble();
        break;
      default:
        result = (json[field] ?? defaultValue).toDouble();
        break;
    }

    debugPrint('🛒 _extractPricing - Extracted $field: $result (default: $defaultValue)');
    return result;
  }

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    debugPrint('🛒 OrderModel.fromJson - Raw JSON: $json');
    debugPrint('🛒 OrderModel.fromJson - Available fields: ${json.keys.toList()}');
    debugPrint('🛒 OrderModel.fromJson - Items: ${json['items']}');
    debugPrint('🛒 OrderModel.fromJson - Pricing object: ${json['pricing']}');

    // Calculate totals from items if not provided by API
    final items = (json['items'] as List<dynamic>?)
        ?.map((item) => OrderItemModel.fromJson(item))
        .toList() ?? [];

    double calculatedSubtotal = 0;
    for (final item in items) {
      calculatedSubtotal += (item.price * item.quantity);
    }

    return OrderModel(
      id: json['_id'] ?? json['id'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      userId: json['userId'] ?? json['user'] ?? '',
      items: items,
      subtotal: _extractPricing(json, 'subtotal', calculatedSubtotal),
      tax: _extractPricing(json, 'tax', 0),
      shipping: _extractPricing(json, 'shipping', 0),
      discount: _extractPricing(json, 'discount', 0),
      total: _extractPricing(json, 'total', calculatedSubtotal),
      status: json['status'] ?? 'pending',
      paymentStatus: json['paymentInfo'] != null
          ? (json['paymentInfo']['status'] ?? 'pending')
          : (json['paymentStatus'] ?? 'pending'),
      shippingAddress: json['shippingAddress'] != null
          ? AddressModel.fromJson(json['shippingAddress'])
          : AddressModel(
              addressLine1: 'No address provided',
              city: 'Unknown',
              state: 'Unknown',
              postalCode: '000000',
              country: 'India',
            ),
      billingAddress: json['billingAddress'] != null
          ? AddressModel.fromJson(json['billingAddress'])
          : null,
      paymentMethod: json['paymentInfo'] != null
          ? (json['paymentInfo']['method'] ?? json['paymentMethod'])
          : json['paymentMethod'],
      notes: json['notes'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'userId': userId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'status': status,
      'paymentStatus': paymentStatus,
      'shippingAddress': shippingAddress.toJson(),
      'billingAddress': billingAddress?.toJson(),
      'paymentMethod': paymentMethod,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  OrderModel copyWith({
    String? id,
    String? orderNumber,
    String? userId,
    List<OrderItemModel>? items,
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    String? status,
    String? paymentStatus,
    AddressModel? shippingAddress,
    AddressModel? billingAddress,
    String? paymentMethod,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      userId: userId ?? this.userId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      billingAddress: billingAddress ?? this.billingAddress,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Getters
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  bool get isPending => status == 'pending';
  bool get isConfirmed => status == 'confirmed';
  bool get isProcessing => status == 'processing';
  bool get isShipped => status == 'shipped';
  bool get isDelivered => status == 'delivered';
  bool get isCancelled => status == 'cancelled';
  bool get isPaymentPending => paymentStatus == 'pending';
  bool get isPaymentCompleted => paymentStatus == 'completed';
  bool get isPaymentFailed => paymentStatus == 'failed';
  bool get canBeCancelled => isPending || isConfirmed;
  String get formattedTotal => '₹${total.toStringAsFixed(2)}';
  String get formattedCreatedAt => '${createdAt.day}/${createdAt.month}/${createdAt.year}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderModel(id: $id, orderNumber: $orderNumber, status: $status, total: $total)';
  }
}

/// Order item model
class OrderItemModel {
  final String? id;
  final ProductModel product;
  final int quantity;
  final double price;
  final String? variant;

  const OrderItemModel({
    this.id,
    required this.product,
    required this.quantity,
    required this.price,
    this.variant,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    debugPrint('🛒 OrderItemModel.fromJson - Raw JSON: $json');

    // Handle different API response structures
    ProductModel product;

    if (json['productSnapshot'] != null) {
      // Legacy API returns productSnapshot with product details
      final productSnapshot = json['productSnapshot'] as Map<String, dynamic>;
      product = ProductModel(
        id: json['product'] ?? productSnapshot['_id'] ?? '',
        name: productSnapshot['name'] ?? 'Unknown Product',
        description: productSnapshot['description'] ?? '',
        price: (productSnapshot['price'] ?? 0).toDouble(),
        images: (productSnapshot['images'] as List?)?.cast<String>() ?? [],
        stock: productSnapshot['stock'] ?? 0,
      );
    } else if (json['product'] != null && json['product'] is Map<String, dynamic>) {
      // New API returns full product object
      final productData = json['product'] as Map<String, dynamic>;
      product = ProductModel(
        id: productData['_id'] ?? productData['id'] ?? '',
        name: productData['name'] ?? 'Unknown Product',
        description: productData['description'] ?? '',
        price: (productData['price'] ?? 0).toDouble(),
        images: (productData['images'] as List?)?.cast<String>() ?? [],
        stock: productData['stock'] ?? 0,
        slug: productData['slug'] ?? '',
      );
    } else {
      // Fallback for minimal product data
      product = ProductModel(
        id: json['product']?.toString() ?? '',
        name: 'Unknown Product',
        description: '',
        price: (json['unitPrice'] ?? json['price'] ?? 0).toDouble(),
        stock: 0,
      );
    }

    final price = (json['unitPrice'] ?? json['price'] ?? product.price).toDouble();
    debugPrint('🛒 OrderItemModel - Product: ${product.name}, Price: $price, Quantity: ${json['quantity']}');

    return OrderItemModel(
      id: json['id'] ?? json['_id'],
      product: product,
      quantity: json['quantity'] ?? 1,
      price: price,
      variant: json['variant'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'price': price,
      if (variant != null) 'variant': variant,
    };
  }

  OrderItemModel copyWith({
    String? id,
    ProductModel? product,
    int? quantity,
    double? price,
    String? variant,
  }) {
    return OrderItemModel(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      variant: variant ?? this.variant,
    );
  }

  // Getters
  double get totalPrice => price * quantity;
  String get productName => product.name;
  String get productImage => product.imageUrl;
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedTotalPrice => '₹${totalPrice.toStringAsFixed(2)}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderItemModel(id: $id, product: ${product.name}, quantity: $quantity)';
  }
}

/// Order tracking model
class OrderTrackingModel {
  final String orderId;
  final String orderNumber;
  final String currentStatus;
  final List<OrderStatusUpdate> statusHistory;
  final String? trackingNumber;
  final String? estimatedDelivery;

  const OrderTrackingModel({
    required this.orderId,
    required this.orderNumber,
    required this.currentStatus,
    required this.statusHistory,
    this.trackingNumber,
    this.estimatedDelivery,
  });

  factory OrderTrackingModel.fromJson(Map<String, dynamic> json) {
    return OrderTrackingModel(
      orderId: json['orderId'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      currentStatus: json['currentStatus'] ?? '',
      statusHistory: (json['statusHistory'] as List<dynamic>?)
          ?.map((status) => OrderStatusUpdate.fromJson(status))
          .toList() ?? [],
      trackingNumber: json['trackingNumber'],
      estimatedDelivery: json['estimatedDelivery'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'orderNumber': orderNumber,
      'currentStatus': currentStatus,
      'statusHistory': statusHistory.map((status) => status.toJson()).toList(),
      'trackingNumber': trackingNumber,
      'estimatedDelivery': estimatedDelivery,
    };
  }

  // Getters
  bool get hasTrackingNumber => trackingNumber != null && trackingNumber!.isNotEmpty;
  bool get hasEstimatedDelivery => estimatedDelivery != null && estimatedDelivery!.isNotEmpty;
  OrderStatusUpdate? get latestUpdate => statusHistory.isNotEmpty ? statusHistory.last : null;

  @override
  String toString() {
    return 'OrderTrackingModel(orderId: $orderId, currentStatus: $currentStatus)';
  }
}

/// Order status update model
class OrderStatusUpdate {
  final String status;
  final String message;
  final DateTime timestamp;
  final String? location;

  const OrderStatusUpdate({
    required this.status,
    required this.message,
    required this.timestamp,
    this.location,
  });

  factory OrderStatusUpdate.fromJson(Map<String, dynamic> json) {
    return OrderStatusUpdate(
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'location': location,
    };
  }

  // Getters
  String get formattedTimestamp => '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute}';

  @override
  String toString() {
    return 'OrderStatusUpdate(status: $status, message: $message)';
  }
}
