/// Product model for ecommerce application
class ProductModel {
  final String id;
  final String name;
  final String description;
  final String slug;
  final String sku;
  final double price;
  final double? originalPrice;
  final double discountPercentage;
  final double discountAmount;

  final List<String> images;
  final CategoryModel? category;
  final List<ProductVariant> variants;
  final int stock;
  final int minOrderQuantity;
  final int maxOrderQuantity;
  final String availability;
  final String stockStatus;
  final bool isInStock;
  final double rating;
  final int reviewCount;
  final bool isActive;
  final bool isWishlisted;
  final bool isFeatured;
  final bool isBestseller;
  final bool isNewArrival;
  final int viewCount;
  final int salesCount;
  final List<String> tags;
  final Map<String, dynamic> specifications;
  final ShippingInfo? shippingInfo;
  final String returnPolicy;
  final String warranty;
  final List<ProductModel> relatedProducts;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ProductModel({
    required this.id,
    required this.name,
    required this.description,
    this.slug = '',
    this.sku = '',
    required this.price,
    this.originalPrice,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    this.images = const [],
    this.category,
    this.variants = const [],
    required this.stock,
    this.minOrderQuantity = 1,
    this.maxOrderQuantity = 10,
    this.availability = 'in_stock',
    this.stockStatus = 'in_stock',
    this.isInStock = true,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isActive = true,
    this.isWishlisted = false,
    this.isFeatured = false,
    this.isBestseller = false,
    this.isNewArrival = false,
    this.viewCount = 0,
    this.salesCount = 0,
    this.tags = const [],
    this.specifications = const {},
    this.shippingInfo,
    this.returnPolicy = '',
    this.warranty = '',
    this.relatedProducts = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      slug: json['slug'] ?? '',
      sku: json['sku'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: (json['original_price'] ?? json['originalPrice'])?.toDouble(),
      discountPercentage: (json['discount_percentage'] ?? 0).toDouble(),
      discountAmount: (json['discount_amount'] ?? 0).toDouble(),
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => image.toString())
          .toList() ?? [],
      category: json['category'] != null
          ? (json['category'] is String
              ? CategoryModel(
                  id: json['category'],
                  name: 'Unknown Category',
                  description: '',
                )
              : CategoryModel.fromJson(json['category']))
          : null,
      variants: (json['variants'] as List<dynamic>?)
          ?.map((variant) => ProductVariant.fromJson(variant))
          .toList() ?? [],
      stock: json['stock'] ?? 0,
      minOrderQuantity: json['min_order_quantity'] ?? 1,
      maxOrderQuantity: json['max_order_quantity'] ?? 10,
      availability: json['availability'] ?? 'in_stock',
      stockStatus: json['stock_status'] ?? 'in_stock',
      isInStock: json['is_in_stock'] ?? true,
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['review_count'] ?? json['reviewCount'] ?? 0,
      isActive: json['is_active'] ?? json['isActive'] ?? true,
      isWishlisted: json['is_favorite'] ?? json['isWishlisted'] ?? false,
      isFeatured: json['is_featured'] ?? json['isFeatured'] ?? false,
      isBestseller: json['is_bestseller'] ?? json['isBestseller'] ?? false,
      isNewArrival: json['is_new_arrival'] ?? json['isNewArrival'] ?? false,
      viewCount: json['view_count'] ?? json['viewCount'] ?? 0,
      salesCount: json['sales_count'] ?? json['salesCount'] ?? 0,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((tag) => tag.toString())
          .toList() ?? [],
      specifications: Map<String, dynamic>.from(json['specifications'] ?? {}),
      shippingInfo: json['shipping_info'] != null
          ? ShippingInfo.fromJson(json['shipping_info'] as Map<String, dynamic>)
          : null,
      returnPolicy: json['return_policy'] ?? '',
      warranty: json['warranty'] ?? '',
      relatedProducts: (json['related_products'] as List<dynamic>?)
          ?.map((product) => ProductModel.fromJson(product as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : json['createdAt'] != null
              ? DateTime.tryParse(json['createdAt'])
              : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : json['updatedAt'] != null
              ? DateTime.tryParse(json['updatedAt'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'slug': slug,
      'sku': sku,
      'price': price,
      'original_price': originalPrice,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'images': images,
      'category': category?.toJson(),
      'variants': variants.map((variant) => variant.toJson()).toList(),
      'stock': stock,
      'min_order_quantity': minOrderQuantity,
      'max_order_quantity': maxOrderQuantity,
      'availability': availability,
      'stock_status': stockStatus,
      'is_in_stock': isInStock,
      'rating': rating,
      'review_count': reviewCount,
      'is_active': isActive,
      'is_favorite': isWishlisted,
      'is_featured': isFeatured,
      'is_bestseller': isBestseller,
      'is_new_arrival': isNewArrival,
      'view_count': viewCount,
      'sales_count': salesCount,
      'tags': tags,
      'specifications': specifications,
      if (shippingInfo != null) 'shipping_info': shippingInfo!.toJson(),
      'return_policy': returnPolicy,
      'warranty': warranty,
      'related_products': relatedProducts.map((product) => product.toJson()).toList(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? slug,
    String? sku,
    double? price,
    double? originalPrice,
    double? discountPercentage,
    double? discountAmount,
    List<String>? images,
    CategoryModel? category,
    List<ProductVariant>? variants,
    int? stock,
    int? minOrderQuantity,
    int? maxOrderQuantity,
    String? availability,
    String? stockStatus,
    bool? isInStock,
    double? rating,
    int? reviewCount,
    bool? isActive,
    bool? isWishlisted,
    bool? isFeatured,
    bool? isBestseller,
    bool? isNewArrival,
    int? viewCount,
    int? salesCount,
    List<String>? tags,
    Map<String, dynamic>? specifications,
    ShippingInfo? shippingInfo,
    String? returnPolicy,
    String? warranty,
    List<ProductModel>? relatedProducts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      slug: slug ?? this.slug,
      sku: sku ?? this.sku,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      images: images ?? this.images,
      category: category ?? this.category,
      variants: variants ?? this.variants,
      stock: stock ?? this.stock,
      minOrderQuantity: minOrderQuantity ?? this.minOrderQuantity,
      maxOrderQuantity: maxOrderQuantity ?? this.maxOrderQuantity,
      availability: availability ?? this.availability,
      stockStatus: stockStatus ?? this.stockStatus,
      isInStock: isInStock ?? this.isInStock,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isActive: isActive ?? this.isActive,
      isWishlisted: isWishlisted ?? this.isWishlisted,
      isFeatured: isFeatured ?? this.isFeatured,
      isBestseller: isBestseller ?? this.isBestseller,
      isNewArrival: isNewArrival ?? this.isNewArrival,
      viewCount: viewCount ?? this.viewCount,
      salesCount: salesCount ?? this.salesCount,
      tags: tags ?? this.tags,
      specifications: specifications ?? this.specifications,
      shippingInfo: shippingInfo ?? this.shippingInfo,
      returnPolicy: returnPolicy ?? this.returnPolicy,
      warranty: warranty ?? this.warranty,
      relatedProducts: relatedProducts ?? this.relatedProducts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Getters
  String get imageUrl => images.isNotEmpty ? images.first : '';
  bool get hasDiscount => originalPrice != null && originalPrice! > price;
  bool get isOutOfStock => stock <= 0;
  String get categoryName => category?.name ?? '';
  String get categoryId => category?.id ?? '';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, price: $price, stock: $stock)';
  }
}

/// Product variant model
class ProductVariant {
  final String? id;
  final String name;
  final double? price;
  final int? stock;
  final Map<String, String> attributes;

  const ProductVariant({
    this.id,
    required this.name,
    this.price,
    this.stock,
    this.attributes = const {},
  });

  factory ProductVariant.fromJson(Map<String, dynamic> json) {
    return ProductVariant(
      id: json['id'] ?? json['_id'],
      name: json['name'] ?? '',
      price: json['price']?.toDouble(),
      stock: json['stock'],
      attributes: Map<String, String>.from(json['attributes'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      if (price != null) 'price': price,
      if (stock != null) 'stock': stock,
      'attributes': attributes,
    };
  }

  ProductVariant copyWith({
    String? id,
    String? name,
    double? price,
    int? stock,
    Map<String, String>? attributes,
  }) {
    return ProductVariant(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      stock: stock ?? this.stock,
      attributes: attributes ?? this.attributes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductVariant && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Category model
class CategoryModel {
  final String id;
  final String name;
  final String? description;
  final String? parent;
  final String? image;

  const CategoryModel({
    required this.id,
    required this.name,
    this.description,
    this.parent,
    this.image,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      parent: json['parent'] is Map<String, dynamic>
          ? json['parent']['id']
          : json['parent'],
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'parent': parent,
      'image': image,
    };
  }

  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? parent,
    String? image,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      parent: parent ?? this.parent,
      image: image ?? this.image,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Keep the old Product class for backward compatibility
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isWishlisted;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    required this.rating,
    required this.reviewCount,
    this.isWishlisted = false,
  });

  // Convert from ProductModel to Product for backward compatibility
  factory Product.fromProductModel(ProductModel productModel) {
    return Product(
      id: productModel.id,
      name: productModel.name,
      description: productModel.description,
      price: productModel.price,
      imageUrl: productModel.imageUrl,
      category: productModel.categoryName,
      rating: productModel.rating,
      reviewCount: productModel.reviewCount,
      isWishlisted: productModel.isWishlisted,
    );
  }

  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    String? category,
    double? rating,
    int? reviewCount,
    bool? isWishlisted,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isWishlisted: isWishlisted ?? this.isWishlisted,
    );
  }
}

/// Shipping information model
class ShippingInfo {
  final Map<String, dynamic> dimensions;
  final double shippingCost;

  const ShippingInfo({
    this.dimensions = const {},
    this.shippingCost = 0.0,
  });

  factory ShippingInfo.fromJson(Map<String, dynamic> json) {
    return ShippingInfo(
      dimensions: Map<String, dynamic>.from(json['dimensions'] ?? {}),
      shippingCost: (json['shippingCost'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dimensions': dimensions,
      'shippingCost': shippingCost,
    };
  }

  ShippingInfo copyWith({
    Map<String, dynamic>? dimensions,
    double? shippingCost,
  }) {
    return ShippingInfo(
      dimensions: dimensions ?? this.dimensions,
      shippingCost: shippingCost ?? this.shippingCost,
    );
  }
}
