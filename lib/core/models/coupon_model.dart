/// Coupon model for discount coupons
class CouponModel {
  final String id;
  final String code;
  final String description;
  final String discountType; // 'percentage' or 'fixed'
  final double discountValue;
  final DateTime validUntil;

  const CouponModel({
    required this.id,
    required this.code,
    required this.description,
    required this.discountType,
    required this.discountValue,
    required this.validUntil,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) {
    return CouponModel(
      id: json['_id'] ?? json['id'] ?? '',
      code: json['code'] ?? '',
      description: json['description'] ?? '',
      discountType: json['discountType'] ?? 'percentage',
      discountValue: (json['discountValue'] ?? 0).toDouble(),
      validUntil: json['validUntil'] != null
          ? DateTime.tryParse(json['validUntil']) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'description': description,
      'discountType': discountType,
      'discountValue': discountValue,
      'validUntil': validUntil.toIso8601String(),
    };
  }

  CouponModel copyWith({
    String? id,
    String? code,
    String? description,
    String? discountType,
    double? discountValue,
    DateTime? validUntil,
  }) {
    return CouponModel(
      id: id ?? this.id,
      code: code ?? this.code,
      description: description ?? this.description,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      validUntil: validUntil ?? this.validUntil,
    );
  }

  /// Check if coupon is still valid
  bool get isValid => DateTime.now().isBefore(validUntil);

  /// Get formatted discount text
  String get discountText {
    if (discountType == 'percentage') {
      return '${discountValue.toInt()}% OFF';
    } else {
      return '₹${discountValue.toInt()} OFF';
    }
  }

  /// Get formatted expiry text
  String get expiryText {
    final now = DateTime.now();
    final difference = validUntil.difference(now);
    
    if (difference.isNegative) {
      return 'Expired';
    } else if (difference.inDays > 0) {
      return 'Expires in ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'Expires in ${difference.inHours} hours';
    } else {
      return 'Expires soon';
    }
  }

  @override
  String toString() {
    return 'CouponModel(id: $id, code: $code, discountType: $discountType, discountValue: $discountValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CouponModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Coupon validation result model
class CouponValidationResult {
  final bool isValid;
  final CouponModel? coupon;
  final double discountAmount;
  final double finalAmount;
  final String? errorMessage;

  const CouponValidationResult({
    required this.isValid,
    this.coupon,
    required this.discountAmount,
    required this.finalAmount,
    this.errorMessage,
  });

  factory CouponValidationResult.fromJson(Map<String, dynamic> json) {
    // The response structure from the coupon service is already parsed
    // and contains the validation data directly
    final isValid = json['isValid'] ?? false;

    if (isValid) {
      return CouponValidationResult(
        isValid: true,
        coupon: json['coupon'] != null
            ? CouponModel.fromJson(json['coupon'])
            : null,
        discountAmount: (json['discountAmount'] ?? 0).toDouble(),
        finalAmount: (json['finalAmount'] ?? 0).toDouble(),
      );
    }

    // Handle error cases - use the exact API error message
    return CouponValidationResult(
      isValid: false,
      discountAmount: 0,
      finalAmount: 0,
      errorMessage: json['message'] ?? 'Invalid coupon code',
    );
  }

  CouponValidationResult copyWith({
    bool? isValid,
    CouponModel? coupon,
    double? discountAmount,
    double? finalAmount,
    String? errorMessage,
  }) {
    return CouponValidationResult(
      isValid: isValid ?? this.isValid,
      coupon: coupon ?? this.coupon,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'CouponValidationResult(isValid: $isValid, discountAmount: $discountAmount, finalAmount: $finalAmount)';
  }
}
