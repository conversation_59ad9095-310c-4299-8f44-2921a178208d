/// Payment order model for Razorpay integration
class PaymentOrderModel {
  final String id;
  final String orderId;
  final double amount;
  final String currency;
  final String status;
  final String? razorpayOrderId;
  final String? razorpayKey;
  final DateTime createdAt;

  const PaymentOrderModel({
    required this.id,
    required this.orderId,
    required this.amount,
    required this.currency,
    required this.status,
    this.razorpayOrderId,
    this.razorpayKey,
    required this.createdAt,
  });

  factory PaymentOrderModel.fromJson(Map<String, dynamic> json) {
    return PaymentOrderModel(
      id: json['id'] ?? json['_id'] ?? '',
      orderId: json['orderId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      status: json['status'] ?? 'created',
      razorpayOrderId: json['razorpayOrderId'],
      razorpayKey: json['razorpayKey'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderId': orderId,
      'amount': amount,
      'currency': currency,
      'status': status,
      'razorpayOrderId': razorpayOrderId,
      'razorpayKey': razorpayKey,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Getters
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
  bool get isCreated => status == 'created';
  bool get isPaid => status == 'paid';
  bool get isFailed => status == 'failed';

  @override
  String toString() {
    return 'PaymentOrderModel(id: $id, orderId: $orderId, amount: $amount, status: $status)';
  }
}

/// Payment verification model
class PaymentVerificationModel {
  final bool isVerified;
  final String paymentId;
  final String orderId;
  final String status;
  final String? transactionId;
  final DateTime? verifiedAt;

  const PaymentVerificationModel({
    required this.isVerified,
    required this.paymentId,
    required this.orderId,
    required this.status,
    this.transactionId,
    this.verifiedAt,
  });

  factory PaymentVerificationModel.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationModel(
      isVerified: json['isVerified'] ?? false,
      paymentId: json['paymentId'] ?? '',
      orderId: json['orderId'] ?? '',
      status: json['status'] ?? 'pending',
      transactionId: json['transactionId'],
      verifiedAt: json['verifiedAt'] != null 
          ? DateTime.parse(json['verifiedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isVerified': isVerified,
      'paymentId': paymentId,
      'orderId': orderId,
      'status': status,
      'transactionId': transactionId,
      'verifiedAt': verifiedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PaymentVerificationModel(isVerified: $isVerified, paymentId: $paymentId, status: $status)';
  }
}

/// Payment model
class PaymentModel {
  final String id;
  final String orderId;
  final String paymentId;
  final double amount;
  final String currency;
  final String status;
  final String method;
  final String? transactionId;
  final String? gatewayResponse;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const PaymentModel({
    required this.id,
    required this.orderId,
    required this.paymentId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.method,
    this.transactionId,
    this.gatewayResponse,
    required this.createdAt,
    this.updatedAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] ?? json['_id'] ?? '',
      orderId: json['orderId'] ?? '',
      paymentId: json['paymentId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      status: json['status'] ?? 'pending',
      method: json['method'] ?? 'razorpay',
      transactionId: json['transactionId'],
      gatewayResponse: json['gatewayResponse'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderId': orderId,
      'paymentId': paymentId,
      'amount': amount,
      'currency': currency,
      'status': status,
      'method': method,
      'transactionId': transactionId,
      'gatewayResponse': gatewayResponse,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Getters
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isRefunded => status == 'refunded';
  String get formattedCreatedAt => '${createdAt.day}/${createdAt.month}/${createdAt.year}';

  @override
  String toString() {
    return 'PaymentModel(id: $id, orderId: $orderId, amount: $amount, status: $status)';
  }
}

/// Refund model
class RefundModel {
  final String id;
  final String paymentId;
  final double amount;
  final String status;
  final String? reason;
  final String? refundId;
  final DateTime createdAt;
  final DateTime? processedAt;

  const RefundModel({
    required this.id,
    required this.paymentId,
    required this.amount,
    required this.status,
    this.reason,
    this.refundId,
    required this.createdAt,
    this.processedAt,
  });

  factory RefundModel.fromJson(Map<String, dynamic> json) {
    return RefundModel(
      id: json['id'] ?? json['_id'] ?? '',
      paymentId: json['paymentId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? 'pending',
      reason: json['reason'],
      refundId: json['refundId'],
      createdAt: DateTime.parse(json['createdAt']),
      processedAt: json['processedAt'] != null 
          ? DateTime.parse(json['processedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'paymentId': paymentId,
      'amount': amount,
      'status': status,
      'reason': reason,
      'refundId': refundId,
      'createdAt': createdAt.toIso8601String(),
      'processedAt': processedAt?.toIso8601String(),
    };
  }

  // Getters
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
  bool get isPending => status == 'pending';
  bool get isProcessed => status == 'processed';
  bool get isFailed => status == 'failed';

  @override
  String toString() {
    return 'RefundModel(id: $id, paymentId: $paymentId, amount: $amount, status: $status)';
  }
}

/// Payment method model
class PaymentMethodModel {
  final String id;
  final String name;
  final String type;
  final String? icon;
  final bool isEnabled;
  final double? processingFee;
  final String? description;

  const PaymentMethodModel({
    required this.id,
    required this.name,
    required this.type,
    this.icon,
    required this.isEnabled,
    this.processingFee,
    this.description,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      icon: json['icon'],
      isEnabled: json['isEnabled'] ?? true,
      processingFee: json['processingFee']?.toDouble(),
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'icon': icon,
      'isEnabled': isEnabled,
      'processingFee': processingFee,
      'description': description,
    };
  }

  // Getters
  bool get hasProcessingFee => processingFee != null && processingFee! > 0;
  String get formattedProcessingFee => hasProcessingFee 
      ? '₹${processingFee!.toStringAsFixed(2)}' 
      : 'Free';

  @override
  String toString() {
    return 'PaymentMethodModel(id: $id, name: $name, type: $type, isEnabled: $isEnabled)';
  }
}

/// Payment fees model
class PaymentFeesModel {
  final double amount;
  final double processingFee;
  final double gatewayFee;
  final double totalFee;
  final double finalAmount;

  const PaymentFeesModel({
    required this.amount,
    required this.processingFee,
    required this.gatewayFee,
    required this.totalFee,
    required this.finalAmount,
  });

  factory PaymentFeesModel.fromJson(Map<String, dynamic> json) {
    return PaymentFeesModel(
      amount: (json['amount'] ?? 0).toDouble(),
      processingFee: (json['processingFee'] ?? 0).toDouble(),
      gatewayFee: (json['gatewayFee'] ?? 0).toDouble(),
      totalFee: (json['totalFee'] ?? 0).toDouble(),
      finalAmount: (json['finalAmount'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'processingFee': processingFee,
      'gatewayFee': gatewayFee,
      'totalFee': totalFee,
      'finalAmount': finalAmount,
    };
  }

  // Getters
  bool get hasFees => totalFee > 0;
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
  String get formattedTotalFee => '₹${totalFee.toStringAsFixed(2)}';
  String get formattedFinalAmount => '₹${finalAmount.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'PaymentFeesModel(amount: $amount, totalFee: $totalFee, finalAmount: $finalAmount)';
  }
}
