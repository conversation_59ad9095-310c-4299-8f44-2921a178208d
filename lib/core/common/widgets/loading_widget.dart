import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/media.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:lottie/lottie.dart';

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({
    super.key,
    this.message,
    this.size = 120,
    this.showMessage = true,
  });

  final String? message;
  final double size;
  final bool showMessage;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.asset(
            Media.loading,
            width: size,
            height: size,
            fit: BoxFit.contain,
          ),
          if (showMessage && message != null) ...[
            SizedBox(height: 16),
            Text(
              message!,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact loading widget for smaller spaces
class CompactLoadingWidget extends StatelessWidget {
  const CompactLoadingWidget({super.key, this.size = 50});

  final double size;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        Media.loading,
        width: size,
        height: size,
        fit: BoxFit.contain,
      ),
    );
  }
}

class LoadingOverlay extends StatelessWidget {
  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
  });

  final bool isLoading;
  final Widget child;
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black54,
            child: LoadingWidget(message: message),
          ),
      ],
    );
  }
}
