import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/loading_widget.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.isLoading = false,
    this.isOutlined = false,
    this.height = 56,
    this.width,
    this.borderRadius = 12,
    this.icon,
  });

  final VoidCallback? onPressed;
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final bool isLoading;
  final bool isOutlined;
  final double height;
  final double? width;
  final double borderRadius;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    final defaultBackgroundColor =
        isOutlined ? Colors.transparent : Colours.lightThemePrimaryColour;

    final defaultTextColor =
        isOutlined
            ? Colours.lightThemePrimaryColour
            : Colours.lightThemeWhiteColour;

    return SizedBox(
      height: height,
      width: width ?? double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? defaultBackgroundColor,
          foregroundColor: textColor ?? defaultTextColor,
          elevation: isOutlined ? 0 : 2,
          shadowColor: isOutlined ? Colors.transparent : Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side:
                isOutlined
                    ? BorderSide(
                      color: borderColor ?? Colours.lightThemePrimaryColour,
                      width: 1.5,
                    )
                    : BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
        child:
            isLoading
                ? CompactLoadingWidget()
                : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (icon != null) ...[icon!, SizedBox(width: 8)],
                    Flexible(
                      child: Text(
                        text,
                        style: TextStyles.headingSemiBold1.copyWith(
                          color: textColor ?? defaultTextColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}

class CustomIconButton extends StatelessWidget {
  const CustomIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.borderRadius = 12,
  });

  final VoidCallback? onPressed;
  final IconData icon;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color:
            backgroundColor ??
            CoreUtils.adaptiveColour(
              context,
              lightModeColour: Colours.lightThemeWhiteColour,
              darkModeColour: Colours.darkThemeDarkSharpColor,
            ),
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: Colours.lightThemeStockColour, width: 1),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: iconColor ?? Colours.classAdaptiveTextColour(context),
          size: size * 0.5,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}
