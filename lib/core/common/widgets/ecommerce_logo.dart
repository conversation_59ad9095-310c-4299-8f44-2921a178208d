import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';

class EcommerceLogo extends StatelessWidget {
  const EcommerceLogo({super.key, this.style});

  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      textAlign: TextAlign.center,
      TextSpan(
        text: "Ghan<PERSON><PERSON>",
        style: style ?? TextStyles.appLogo.copyWith(color: Colors.white),
        children: const [
          TextSpan(
            text: ' Murti ',
            style: TextStyle(color: Colours.lightThemeSecondaryColour),
          ),
          TextSpan(
            text: "Bhandar",
            style: TextStyle(color: Colours.lightThemePrimaryColour),
          ),
        ],
      ),
    );
  }
}
