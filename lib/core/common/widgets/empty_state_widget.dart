import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/colours.dart';
import 'package:ghanshyam_murti_bhandar/core/res/styles/text.dart';
import 'package:ghanshyam_murti_bhandar/core/common/widgets/custom_button.dart';

class EmptyStateWidget extends StatelessWidget {
  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.description,
    this.lottieAsset,
    this.icon,
    this.buttonText,
    this.onButtonPressed,
    this.height = 300,
  });

  final String title;
  final String description;
  final String? lottieAsset;
  final IconData? icon;
  final String? buttonText;
  final VoidCallback? onButtonPressed;
  final double height;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: height,
              child: lottieAsset != null
                  ? Lottie.asset(
                      lottieAsset!,
                      height: height,
                      fit: BoxFit.contain,
                    )
                  : Icon(
                      icon ?? Icons.inbox_outlined,
                      size: height * 0.4,
                      color: Colours.lightThemeSecondaryTextColour,
                    ),
            ),
            SizedBox(height: 24),
            Text(
              title,
              style: TextStyles.headingMedium3.copyWith(
                color: Colours.classAdaptiveTextColour(context),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              description,
              style: TextStyles.paragraphSubTextRegular1.copyWith(
                color: Colours.lightThemeSecondaryTextColour,
              ),
              textAlign: TextAlign.center,
            ),
            if (buttonText != null && onButtonPressed != null) ...[
              SizedBox(height: 24),
              CustomButton(
                onPressed: onButtonPressed,
                text: buttonText!,
                width: 200,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
