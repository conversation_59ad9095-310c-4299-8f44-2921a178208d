import 'package:flutter/material.dart';
import 'package:ghanshyam_murti_bhandar/core/utils/core_utils.dart';

abstract class Colours {
  // lightThemePrimaryTint Color Swatch
  static const Color lightThemePrimaryTint = Color(0xffff6b6b);

  // lightThemePrimaryColour Color Swatch
  static const Color lightThemePrimaryColour = Color(0xffe53e3e);

  // lightThemeSecondaryColour Color Swatch
  static const Color lightThemeSecondaryColour = Color(0xfff76631);

  // lightThemePrimaryTextColour Color Swatch
  static const Color lightThemePrimaryTextColour = Color(0xff282344);

  // lightThemeSecondaryTextColour Color Swatch
  static const Color lightThemeSecondaryTextColour = Color(0xff9491a1);

  // lightThemePinkTint Color Swatch
  static const Color lightThemePinkTint = Color(0xfff08e98);

  // lightThemeWhiteColour Color Swatch
  static const Color lightThemeWhiteColour = Color(0xffffffff);

  // lightThemeTintStockColour Color Swatch
  static const Color lightThemeTintStockColour = Color(0xfff6f6f9);

  // lightThemeYellowTint Color Swatch
  static const Color lightThemeYellowTint = Color(0xfffec613);

  // lightThemeStockColour Color Swatch
  static const Color lightThemeStockColour = Color(0xffe4e4e9);

  // darkThemeDarkSharpColor Color Swatch
  static const Color darkThemeDarkSharpColor = Color(0xff191821);

  // darkThemeBDark Color Swatch
  static const Color darkThemeBGDark = Color(0xff0e0d11);

  // darkThemeDarkNavBarColour Color Swatch
  static const Color darkThemeDarkNavBarColour = Color(0xff201f27);

  // adaptive text color
  static Color classAdaptiveTextColour(BuildContext context) =>
      CoreUtils.adaptiveColour(
        context,
        lightModeColour: lightThemePrimaryTextColour,
        darkModeColour: lightThemeWhiteColour,
      );
}
