import 'package:flutter/material.dart';
import 'api/api_service.dart';

/// Example usage of the Wishlist Service
class WishlistUsageExample {
  
  /// Example: Add product to wishlist
  static Future<void> addToWishlistExample(String productId) async {
    try {
      final response = await ApiService.instance.wishlist.addToWishlist(productId);
      
      if (response.isSuccess) {
        debugPrint('✅ Product added to wishlist');
        debugPrint('Wishlist now has ${response.data?.itemCount} items');
      } else {
        debugPrint('❌ Failed to add to wishlist: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error adding to wishlist: $e');
    }
  }
  
  /// Example: Remove product from wishlist
  static Future<void> removeFromWishlistExample(String productId) async {
    try {
      final response = await ApiService.instance.wishlist.removeFromWishlist(productId);
      
      if (response.isSuccess) {
        debugPrint('✅ Product removed from wishlist');
        debugPrint('Wishlist now has ${response.data?.itemCount} items');
      } else {
        debugPrint('❌ Failed to remove from wishlist: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error removing from wishlist: $e');
    }
  }
  
  /// Example: Check if product is in wishlist
  static Future<bool> checkWishlistExample(String productId) async {
    try {
      final response = await ApiService.instance.wishlist.checkWishlist(productId);
      
      if (response.isSuccess && response.data != null) {
        final isWishlisted = response.data!;
        debugPrint('🔍 Product is ${isWishlisted ? 'in' : 'not in'} wishlist');
        return isWishlisted;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error checking wishlist: $e');
      return false;
    }
  }
  
  /// Example: Toggle product in wishlist (smart add/remove)
  static Future<void> toggleWishlistExample(String productId) async {
    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(productId);
      
      if (response.isSuccess && response.data != null) {
        final result = response.data!;
        debugPrint('✅ Product ${result.action} ${result.isWishlisted ? 'to' : 'from'} wishlist');
        debugPrint('Wishlist now has ${result.wishlist?.itemCount} items');
      } else {
        debugPrint('❌ Failed to toggle wishlist: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error toggling wishlist: $e');
    }
  }
  
  /// Example: Get full wishlist
  static Future<void> getWishlistExample() async {
    try {
      final response = await ApiService.instance.wishlist.getWishlist();
      
      if (response.isSuccess && response.data != null) {
        final wishlist = response.data!;
        debugPrint('📋 Wishlist loaded: ${wishlist.itemCount} items');
        
        for (final item in wishlist.items) {
          debugPrint('  - ${item.product.name} (₹${item.product.price})');
        }
      } else {
        debugPrint('❌ Failed to load wishlist: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error loading wishlist: $e');
    }
  }
  
  /// Example: Clear entire wishlist
  static Future<void> clearWishlistExample() async {
    try {
      final response = await ApiService.instance.wishlist.clearWishlist();
      
      if (response.isSuccess) {
        debugPrint('✅ Wishlist cleared successfully');
      } else {
        debugPrint('❌ Failed to clear wishlist: ${response.error}');
      }
    } catch (e) {
      debugPrint('Error clearing wishlist: $e');
    }
  }
  
  /// Example: Complete wishlist workflow
  static Future<void> completeWishlistWorkflow() async {
    const productId = 'sample_product_id';
    
    try {
      // 1. Check if product is in wishlist
      final isWishlisted = await checkWishlistExample(productId);
      
      // 2. Add to wishlist if not already there
      if (!isWishlisted) {
        await addToWishlistExample(productId);
      }
      
      // 3. Get full wishlist
      await getWishlistExample();
      
      // 4. Toggle wishlist (will remove since we just added)
      await toggleWishlistExample(productId);
      
      // 5. Get updated wishlist
      await getWishlistExample();
      
    } catch (e) {
      debugPrint('Error in wishlist workflow: $e');
    }
  }
}

/// Widget example showing how to use wishlist in UI
class WishlistButtonExample extends StatefulWidget {
  final String productId;
  
  const WishlistButtonExample({
    Key? key,
    required this.productId,
  }) : super(key: key);
  
  @override
  State<WishlistButtonExample> createState() => _WishlistButtonExampleState();
}

class _WishlistButtonExampleState extends State<WishlistButtonExample> {
  bool _isWishlisted = false;
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _checkWishlistStatus();
  }
  
  Future<void> _checkWishlistStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final response = await ApiService.instance.wishlist.checkWishlist(widget.productId);
      if (response.isSuccess && response.data != null) {
        setState(() => _isWishlisted = response.data!);
      }
    } catch (e) {
      debugPrint('Error checking wishlist status: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  Future<void> _toggleWishlist() async {
    setState(() => _isLoading = true);
    
    try {
      final response = await ApiService.instance.wishlist.toggleWishlist(widget.productId);
      
      if (response.isSuccess && response.data != null) {
        setState(() => _isWishlisted = response.data!.isWishlisted);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.data!.wasAdded 
                ? 'Added to wishlist' 
                : 'Removed from wishlist'),
            backgroundColor: response.data!.wasAdded ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error toggling wishlist: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating wishlist'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: _isLoading ? null : _toggleWishlist,
      icon: _isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(
              _isWishlisted ? Icons.favorite : Icons.favorite_border,
              color: _isWishlisted ? Colors.red : Colors.grey,
            ),
    );
  }
}
