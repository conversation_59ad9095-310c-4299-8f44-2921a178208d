import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_config.dart';

/// Base API Service for handling HTTP requests
class BaseApiService {
  static BaseApiService? _instance;
  late Dio _dio;
  String? _authToken;
  String? _guestId;
  
  BaseApiService._internal() {
    _initializeDio();
  }
  
  static BaseApiService get instance {
    _instance ??= BaseApiService._internal();
    return _instance!;
  }
  
  /// Initialize Dio with configuration
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.currentBaseUrl,
      connectTimeout: Duration(milliseconds: ApiConfig.connectTimeout),
      receiveTimeout: Duration(milliseconds: ApiConfig.receiveTimeout),
      sendTimeout: Duration(milliseconds: ApiConfig.sendTimeout),
      headers: ApiConfig.defaultHeaders,
    ));
    
    // Add interceptors
    _dio.interceptors.add(_createAuthInterceptor());
    _dio.interceptors.add(_createLoggingInterceptor());
    _dio.interceptors.add(_createRetryInterceptor());
  }
  
  /// Create authentication interceptor
  Interceptor _createAuthInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }
        
        // Add guest ID for guest users
        if (_guestId != null && _authToken == null) {
          options.headers['guest-id'] = _guestId;
        }
        
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle 401 unauthorized errors
        if (error.response?.statusCode == ApiConfig.statusUnauthorized) {
          await _handleUnauthorized();
        }
        handler.next(error);
      },
    );
  }
  
  /// Create logging interceptor for debugging
  Interceptor _createLoggingInterceptor() {
    return LogInterceptor(
      requestBody: kDebugMode,
      responseBody: kDebugMode,
      requestHeader: kDebugMode,
      responseHeader: false,
      error: kDebugMode,
      logPrint: (object) {
        if (kDebugMode) {
          debugPrint('API: $object');
        }
      },
    );
  }
  
  /// Create retry interceptor for failed requests
  Interceptor _createRetryInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) async {
        if (_shouldRetry(error)) {
          try {
            await Future.delayed(ApiConfig.retryDelay);
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
            return;
          } catch (e) {
            // If retry fails, continue with original error
          }
        }
        handler.next(error);
      },
    );
  }
  
  /// Check if request should be retried
  bool _shouldRetry(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
           error.type == DioExceptionType.receiveTimeout ||
           error.type == DioExceptionType.sendTimeout ||
           (error.response?.statusCode != null && 
            error.response!.statusCode! >= 500);
  }
  
  /// Handle unauthorized access
  Future<void> _handleUnauthorized() async {
    await clearAuthToken();
    // You can add navigation to login screen here
    // NavigationService.navigateToLogin();
  }
  
  /// Set authentication token
  Future<void> setAuthToken(String token) async {
    _authToken = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(ApiConfig.tokenKey, token);
  }
  
  /// Get authentication token
  Future<String?> getAuthToken() async {
    if (_authToken != null) return _authToken;
    
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(ApiConfig.tokenKey);
    return _authToken;
  }
  
  /// Clear authentication token
  Future<void> clearAuthToken() async {
    _authToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ApiConfig.tokenKey);
    await prefs.remove(ApiConfig.userKey);
  }
  
  /// Set guest ID for guest users
  Future<void> setGuestId(String guestId) async {
    _guestId = guestId;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(ApiConfig.guestIdKey, guestId);
  }
  
  /// Get guest ID
  Future<String?> getGuestId() async {
    if (_guestId != null) return _guestId;
    
    final prefs = await SharedPreferences.getInstance();
    _guestId = prefs.getString(ApiConfig.guestIdKey);
    return _guestId;
  }
  
  /// Clear guest ID
  Future<void> clearGuestId() async {
    _guestId = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ApiConfig.guestIdKey);
  }
  
  /// Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Generic PATCH request
  Future<ApiResponse<T>> patch<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Handle API response
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    final data = response.data;

    if (data is Map<String, dynamic>) {
      T? parsedData;
      if (fromJson != null) {
        if (data['data'] != null) {
          // Standard API response format: {"success": true, "data": {...}}
          parsedData = fromJson(data['data']);
        } else {
          // Direct data format (like cart API): {"items": [], "total": 0}
          parsedData = fromJson(data);
        }
      } else if (data['data'] != null) {
        parsedData = data['data'] as T?;
      } else {
        // Direct data without wrapper
        parsedData = data as T?;
      }

      // Extract error message from errors array if present
      String? errorMessage;
      if (data['errors'] is List && (data['errors'] as List).isNotEmpty) {
        final errors = data['errors'] as List;
        final firstError = errors.first;
        if (firstError is Map<String, dynamic>) {
          errorMessage = firstError['msg'] ?? firstError['message'] ?? firstError.toString();
        } else {
          errorMessage = firstError.toString();
        }
      } else if (data['error'] != null) {
        errorMessage = data['error'].toString();
      }

      return ApiResponse<T>(
        success: data['success'] ?? true,
        message: data['message'] ?? 'Success',
        data: parsedData,
        error: errorMessage,
        statusCode: response.statusCode,
      );
    }

    return ApiResponse<T>(
      success: true,
      message: 'Success',
      data: data as T?,
      statusCode: response.statusCode,
    );
  }
  
  /// Handle API errors
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return const NetworkException('Connection timeout');
        
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final data = error.response?.data;

          String message = ApiConfig.serverError;
          if (data is Map<String, dynamic>) {
            // Check for errors array first (your API format)
            if (data['errors'] is List && (data['errors'] as List).isNotEmpty) {
              final errors = data['errors'] as List;
              final firstError = errors.first;
              if (firstError is Map<String, dynamic>) {
                message = firstError['msg'] ?? firstError['message'] ?? message;
              } else {
                message = firstError.toString();
              }
            } else {
              // Fallback to standard error/message fields
              message = data['error'] ?? data['message'] ?? message;
            }
          }

          return ApiException(
            message: message,
            statusCode: statusCode,
            error: data?['error'],
          );
        
        case DioExceptionType.cancel:
          return const NetworkException('Request cancelled');
        
        case DioExceptionType.unknown:
          if (error.error is SocketException) {
            return const NetworkException('No internet connection');
          }
          return NetworkException(error.message ?? 'Unknown error');
        
        default:
          return NetworkException(error.message ?? 'Network error');
      }
    }
    
    return NetworkException(error.toString());
  }
  
  /// Upload file with multipart form data
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    Map<String, dynamic>? data,
    String fieldName = 'file',
    T Function(dynamic)? fromJson,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final formData = FormData();
      
      // Add file
      formData.files.add(MapEntry(
        fieldName,
        await MultipartFile.fromFile(file.path),
      ));
      
      // Add other data
      if (data != null) {
        data.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }
      
      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(
          headers: ApiConfig.multipartHeaders,
        ),
        onSendProgress: onSendProgress,
      );
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      throw _handleError(e);
    }
  }
}
