import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// API Configuration for Ghanshyam Murti Bhandar
class ApiConfig {
  // Base URLs
  static const String productionUrl =
      'https://server.ghanshyammurtibhandar.com/api';

  // 🔧 DEVELOPMENT SETUP INSTRUCTIONS:
  // 1. Find your local IP address:
  //    - macOS: Run `ipconfig getifaddr en0` in Terminal
  //    - Windows: Run `ipconfig` and look for IPv4 Address
  //    - Linux: Run `hostname -I` or `ip addr show`
  // 2. Replace the IP in localIpUrl above
  // 3. Make sure your backend server is running on port 8080
  // 4. For iOS simulator, NEVER use localhost - always use IP address

  // 🚀 ENVIRONMENT SELECTION:
  static const String currentBaseUrl = productionUrl;

  // 🐛 DEBUG HELPERS:
  static String get environmentName {
    if (currentBaseUrl == productionUrl) return '🌐 PRODUCTION';
    return '❓ UNKNOWN';
  }

  static void printCurrentEnvironment() {
    if (kDebugMode) {
      debugPrint('🔗 API Environment: $environmentName');
      debugPrint('🔗 Base URL: $currentBaseUrl');
    }
  }

  // API Endpoints
  static const String auth = '/auth';
  static const String products = '/products';
  static const String categories = '/categories';
  static const String cart = '/cart';
  static const String wishlist = '/wishlist';
  static const String orders = '/orders';
  static const String reviews = '/reviews';
  static const String users = '/users';
  static const String payments = '/payments';
  static const String coupons = '/coupons';
  static const String admin = '/admin';

  // Auth Endpoints
  static const String signup = '$auth/register';
  static const String login = '$auth/login';
  static const String logout = '$auth/logout';
  static const String profile = '$auth/profile';
  static const String updateProfile = '$auth/profile';
  static const String refreshToken = '$auth/refresh-token';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';

  // Product Endpoints
  static const String allProducts = products;
  static const String featuredProducts = '$products/featured';
  static const String singleProduct = '$products/'; // append ID
  static const String createProduct = products;
  static const String updateProduct = '$products/'; // append ID
  static const String deleteProduct = '$products/'; // append ID
  static const String updateInventory = '$products/'; // append ID/inventory

  // Category Endpoints
  static const String allCategories = categories;
  static const String featuredCategories = '$categories/featured';
  static const String categoryTree = '$categories/tree';
  static const String createCategory = categories;
  static const String updateCategory = '$categories/'; // append ID
  static const String deleteCategory = '$categories/'; // append ID

  // Cart Endpoints
  static const String getCart = cart;
  static const String addToCart = '$cart/add';
  static const String updateCartItem = '$cart/update';
  static const String removeCartItem = '$cart/item/'; // append itemId
  static const String clearCart = '$cart/clear';
  static const String mergeCart = '$cart/merge';

  // Wishlist Endpoints
  static const String getWishlist = wishlist;
  static const String addToWishlist = '$wishlist/add';
  static const String removeFromWishlist =
      '$wishlist/remove/'; // append productId
  static const String clearWishlist = '$wishlist/clear';
  static const String checkWishlist = '$wishlist/check/'; // append productId

  // Order Endpoints
  static const String createOrder = orders;
  static const String myOrders = '$orders/my-orders';
  static const String singleOrder = '$orders/'; // append orderId
  static const String cancelOrder = '$orders/'; // append orderId/cancel
  static const String allOrders = orders; // admin only
  static const String updateOrderStatus = '$orders/'; // append ID (admin)

  // Review Endpoints
  static const String productReviews = '$reviews/product/'; // append productId
  static const String addReview = reviews;
  static const String updateReview = '$reviews/'; // append reviewId
  static const String deleteReview = '$reviews/'; // append reviewId
  static const String markReviewHelpful =
      '$reviews/'; // append reviewId/helpful
  static const String reportReview = '$reviews/'; // append reviewId/report
  static const String reviewVisibility =
      '$reviews/'; // append reviewId/visibility

  // Address Endpoints
  static const String addresses = '/addresses';
  static const String userAddresses = addresses;
  static const String addAddress = addresses;
  static const String updateAddress = '$addresses/'; // append addressId
  static const String deleteAddress = '$addresses/'; // append addressId
  static const String setDefaultAddress =
      '$addresses/'; // append addressId/default
  static const String validateAddress =
      '$addresses/'; // append addressId/validate

  // User Management Endpoints
  static const String changePassword = '$users/change-password';
  static const String allUsers = users; // admin only
  static const String singleUser = '$users/'; // append userId (admin)

  // Payment Endpoints
  static const String createPaymentOrder = '$payments/create-order';
  static const String verifyPayment = '$payments/verify';
  static const String paymentFailure = '$payments/failure';
  static const String paymentDetails = '$payments/details/'; // append paymentId
  static const String refundPayment = '$payments/refund';
  static const String paymentWebhook = '$payments/webhook';

  // Coupon Endpoints
  static const String validateCoupon = '$coupons/validate';
  static const String applyCoupon = '$coupons/apply';
  static const String allCoupons = coupons; // admin only
  static const String createCoupon = coupons; // admin only
  static const String updateCoupon = '$coupons/'; // append couponId (admin)
  static const String deleteCoupon = '$coupons/'; // append couponId (admin)

  // Admin Endpoints
  static const String adminStats = '$admin/stats';

  // Request timeout
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static const Map<String, String> multipartHeaders = {
    'Content-Type': 'multipart/form-data',
    'Accept': 'application/json',
  };

  // HTTP Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusConflict = 409;
  static const int statusInternalServerError = 500;

  // Error Messages
  static const String networkError = 'Network error occurred';
  static const String serverError = 'Server error occurred';
  static const String unauthorizedError = 'Unauthorized access';
  static const String notFoundError = 'Resource not found';
  static const String validationError = 'Validation error';
  static const String conflictError = 'Data conflict occurred';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String guestIdKey = 'guest_id';
  static const String cartKey = 'cart_data';
  static const String wishlistKey = 'wishlist_data';

  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;

  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageFormats = [
    'jpeg',
    'jpg',
    'png',
    'gif',
    'webp',
  ];

  // Cache Duration
  static const Duration cacheExpiry = Duration(minutes: 15);
  static const Duration longCacheExpiry = Duration(hours: 1);

  // Retry Configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
}

/// API Response wrapper
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final String? error;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
    this.statusCode,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T? data) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: data,
      error: json['error'],
      statusCode: json['statusCode'],
    );
  }

  bool get isSuccess => success && error == null;
  bool get isError => !success || error != null;
}

/// API Exception class
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? error;

  const ApiException({required this.message, this.statusCode, this.error});

  @override
  String toString() => message;
}

/// Network Exception class
class NetworkException implements Exception {
  final String message;

  const NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}
