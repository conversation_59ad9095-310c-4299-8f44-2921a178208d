import 'dart:io';
import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/product_model.dart';

/// Product Service for handling product-related API calls
class ProductService {
  static ProductService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  ProductService._internal();
  
  static ProductService get instance {
    _instance ??= ProductService._internal();
    return _instance!;
  }
  
  /// Get all products with filtering and pagination
  Future<ApiResponse<ProductListResponse>> getProducts({
    int page = 1,
    int limit = 10,
    String? category,
    String? search,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
    bool? featured,
    bool? bestseller,
    bool? newArrival,
    bool? onSale,
    bool? inStock,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (minPrice != null) queryParams['minPrice'] = minPrice;
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice;
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (sortOrder != null) queryParams['sortOrder'] = sortOrder;
      if (featured != null) queryParams['featured'] = featured;
      if (bestseller != null) queryParams['bestseller'] = bestseller;
      if (newArrival != null) queryParams['new_arrival'] = newArrival;
      if (onSale != null) queryParams['on_sale'] = onSale;
      if (inStock != null) queryParams['in_stock'] = inStock;

      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.allProducts,
        queryParameters: queryParams,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        // The BaseApiService already extracts the 'data' field from the API response
        final dataSection = response.data!;

        final productsJson = dataSection['products'] as List<dynamic>? ?? [];
        final paginationData = dataSection['pagination'] as Map<String, dynamic>? ?? {};

        final products = productsJson
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();

        final productListResponse = ProductListResponse(
          products: products,
          totalProducts: paginationData['total'] ?? products.length,
          totalPages: paginationData['total_pages'] ?? 1,
          currentPage: paginationData['current_page'] ?? page,
          hasNextPage: paginationData['has_next_page'] ?? false,
          hasPrevPage: paginationData['has_prev_page'] ?? false,
          pagination: PaginationModel.fromJson(paginationData),
          filters: dataSection['filters'] != null
              ? FiltersModel.fromJson(dataSection['filters'] as Map<String, dynamic>)
              : null,
          meta: dataSection['meta'] != null
              ? MetaModel.fromJson(dataSection['meta'] as Map<String, dynamic>)
              : null,
        );

        return ApiResponse<ProductListResponse>(
          success: response.success,
          message: response.message,
          data: productListResponse,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<ProductListResponse>(
        success: false,
        message: response.error ?? 'Failed to get products',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }



  /// Get single product by ID
  Future<ApiResponse<ProductModel>> getProduct(String productId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.singleProduct}$productId',
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final dataSection = response.data!;

        // The single product API returns data.product (not data.products array)
        if (dataSection['product'] != null) {
          final productJson = dataSection['product'] as Map<String, dynamic>;
          final product = ProductModel.fromJson(productJson);

          return ApiResponse<ProductModel>(
            success: response.success,
            message: response.message,
            data: product,
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse<ProductModel>(
        success: false,
        message: response.error ?? 'Failed to get product',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Create new product (Admin only)
  Future<ApiResponse<ProductModel>> createProduct({
    required String name,
    required String description,
    required double price,
    double? originalPrice,
    required String categoryId,
    required int stock,
    List<File>? images,
    List<ProductVariant>? variants,
  }) async {
    try {
      final data = <String, dynamic>{
        'name': name,
        'description': description,
        'price': price,
        'category': categoryId,
        'stock': stock,
      };
      
      if (originalPrice != null) data['originalPrice'] = originalPrice;
      if (variants != null) {
        data['variants'] = variants.map((v) => v.toJson()).toList();
      }
      
      ApiResponse<ProductModel> response;
      
      if (images != null && images.isNotEmpty) {
        // Upload with images using multipart
        response = await _apiService.uploadFile<ProductModel>(
          ApiConfig.createProduct,
          images.first, // Upload first image as main file
          data: data,
          fieldName: 'images',
          fromJson: (json) => ProductModel.fromJson(json),
        );
      } else {
        // Create without images
        response = await _apiService.post<ProductModel>(
          ApiConfig.createProduct,
          data: data,
          fromJson: (json) => ProductModel.fromJson(json),
        );
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Update product (Admin only)
  Future<ApiResponse<ProductModel>> updateProduct({
    required String productId,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? categoryId,
    int? stock,
    List<File>? images,
    List<ProductVariant>? variants,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (price != null) data['price'] = price;
      if (originalPrice != null) data['originalPrice'] = originalPrice;
      if (categoryId != null) data['category'] = categoryId;
      if (stock != null) data['stock'] = stock;
      if (variants != null) {
        data['variants'] = variants.map((v) => v.toJson()).toList();
      }
      
      ApiResponse<ProductModel> response;
      
      if (images != null && images.isNotEmpty) {
        // Update with new images
        response = await _apiService.uploadFile<ProductModel>(
          '${ApiConfig.updateProduct}$productId',
          images.first,
          data: data,
          fieldName: 'images',
          fromJson: (json) => ProductModel.fromJson(json),
        );
      } else {
        // Update without images
        response = await _apiService.put<ProductModel>(
          '${ApiConfig.updateProduct}$productId',
          data: data,
          fromJson: (json) => ProductModel.fromJson(json),
        );
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Delete product (Admin only)
  Future<ApiResponse<void>> deleteProduct(String productId) async {
    try {
      final response = await _apiService.delete<void>(
        '${ApiConfig.deleteProduct}$productId',
      );
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Update product inventory (Admin only)
  Future<ApiResponse<ProductModel>> updateInventory({
    required String productId,
    required int stock,
  }) async {
    try {
      final response = await _apiService.patch<ProductModel>(
        '${ApiConfig.updateInventory}$productId/inventory',
        data: {'stock': stock},
        fromJson: (json) => ProductModel.fromJson(json),
      );
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Search products
  Future<ApiResponse<ProductListResponse>> searchProducts({
    required String query,
    int page = 1,
    int limit = 10,
    String? category,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
  }) async {
    return getProducts(
      page: page,
      limit: limit,
      search: query,
      category: category,
      minPrice: minPrice,
      maxPrice: maxPrice,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }
  
  /// Get products by category
  Future<ApiResponse<ProductListResponse>> getProductsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 10,
    String? sortBy,
    String? sortOrder,
  }) async {
    return getProducts(
      page: page,
      limit: limit,
      category: categoryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }

  /// Get featured products
  Future<ApiResponse<List<ProductModel>>> getFeaturedProducts({
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
      };

      final response = await _apiService.get<List<dynamic>>(
        ApiConfig.featuredProducts,
        queryParameters: queryParams,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final products = response.data!
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();

        return ApiResponse<List<ProductModel>>(
          success: response.success,
          message: response.message,
          data: products,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<List<ProductModel>>(
        success: false,
        message: response.error ?? 'Failed to get featured products',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get related products (products in same category)
  Future<ApiResponse<ProductListResponse>> getRelatedProducts({
    required String productId,
    required String categoryId,
    int limit = 5,
  }) async {
    return getProducts(
      page: 1,
      limit: limit,
      category: categoryId,
      sortBy: 'rating',
      sortOrder: 'desc',
    );
  }
}

/// Product list response model
class ProductListResponse {
  final List<ProductModel> products;
  final int totalProducts;
  final int totalPages;
  final int currentPage;
  final bool hasNextPage;
  final bool hasPrevPage;
  final PaginationModel? pagination;
  final FiltersModel? filters;
  final MetaModel? meta;

  const ProductListResponse({
    required this.products,
    required this.totalProducts,
    required this.totalPages,
    required this.currentPage,
    this.hasNextPage = false,
    this.hasPrevPage = false,
    this.pagination,
    this.filters,
    this.meta,
  });

  factory ProductListResponse.fromJson(Map<String, dynamic> json) {
    return ProductListResponse(
      products: (json['products'] as List<dynamic>?)
          ?.map((product) => ProductModel.fromJson(product))
          .toList() ?? [],
      totalProducts: json['totalProducts'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
      pagination: json['pagination'] != null
          ? PaginationModel.fromJson(json['pagination'])
          : null,
      filters: json['filters'] != null
          ? FiltersModel.fromJson(json['filters'])
          : null,
      meta: json['meta'] != null
          ? MetaModel.fromJson(json['meta'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'products': products.map((product) => product.toJson()).toList(),
      'totalProducts': totalProducts,
      'totalPages': totalPages,
      'currentPage': currentPage,
      'hasNextPage': hasNextPage,
      'hasPrevPage': hasPrevPage,
      if (pagination != null) 'pagination': pagination!.toJson(),
      if (filters != null) 'filters': filters!.toJson(),
      if (meta != null) 'meta': meta!.toJson(),
    };
  }
}

/// Pagination model for API responses
class PaginationModel {
  final int currentPage;
  final int perPage;
  final int total;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPrevPage;

  const PaginationModel({
    required this.currentPage,
    required this.perPage,
    required this.total,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory PaginationModel.fromJson(Map<String, dynamic> json) {
    return PaginationModel(
      currentPage: json['current_page'] ?? 1,
      perPage: json['per_page'] ?? 10,
      total: json['total'] ?? 0,
      totalPages: json['total_pages'] ?? 1,
      hasNextPage: json['has_next_page'] ?? false,
      hasPrevPage: json['has_prev_page'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'per_page': perPage,
      'total': total,
      'total_pages': totalPages,
      'has_next_page': hasNextPage,
      'has_prev_page': hasPrevPage,
    };
  }
}

/// Filters model for API responses
class FiltersModel {
  final String? category;
  final String? subcategory;
  final String? search;
  final String? priceRange;
  final String? rating;
  final String? brand;
  final String? tags;
  final String? sortBy;
  final String? sortOrder;
  final FeaturesModel? features;

  const FiltersModel({
    this.category,
    this.subcategory,
    this.search,
    this.priceRange,
    this.rating,
    this.brand,
    this.tags,
    this.sortBy,
    this.sortOrder,
    this.features,
  });

  factory FiltersModel.fromJson(Map<String, dynamic> json) {
    return FiltersModel(
      category: json['category'],
      subcategory: json['subcategory'],
      search: json['search'],
      priceRange: json['price_range'],
      rating: json['rating'],
      brand: json['brand'],
      tags: json['tags'],
      sortBy: json['sort_by'],
      sortOrder: json['sort_order'],
      features: json['features'] != null
          ? FeaturesModel.fromJson(json['features'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (category != null) 'category': category,
      if (subcategory != null) 'subcategory': subcategory,
      if (search != null) 'search': search,
      if (priceRange != null) 'price_range': priceRange,
      if (rating != null) 'rating': rating,
      if (brand != null) 'brand': brand,
      if (tags != null) 'tags': tags,
      if (sortBy != null) 'sort_by': sortBy,
      if (sortOrder != null) 'sort_order': sortOrder,
      if (features != null) 'features': features!.toJson(),
    };
  }
}

/// Features model for filters
class FeaturesModel {
  final bool featured;
  final bool bestseller;
  final bool newArrival;
  final bool onSale;
  final bool inStock;

  const FeaturesModel({
    required this.featured,
    required this.bestseller,
    required this.newArrival,
    required this.onSale,
    required this.inStock,
  });

  factory FeaturesModel.fromJson(Map<String, dynamic> json) {
    return FeaturesModel(
      featured: json['featured'] ?? false,
      bestseller: json['bestseller'] ?? false,
      newArrival: json['new_arrival'] ?? false,
      onSale: json['on_sale'] ?? false,
      inStock: json['in_stock'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'featured': featured,
      'bestseller': bestseller,
      'new_arrival': newArrival,
      'on_sale': onSale,
      'in_stock': inStock,
    };
  }
}

/// Meta model for API responses
class MetaModel {
  final int totalFound;
  final int showing;
  final String? searchQuery;

  const MetaModel({
    required this.totalFound,
    required this.showing,
    this.searchQuery,
  });

  factory MetaModel.fromJson(Map<String, dynamic> json) {
    return MetaModel(
      totalFound: json['total_found'] ?? 0,
      showing: json['showing'] ?? 0,
      searchQuery: json['search_query'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_found': totalFound,
      'showing': showing,
      if (searchQuery != null) 'search_query': searchQuery,
    };
  }
}
