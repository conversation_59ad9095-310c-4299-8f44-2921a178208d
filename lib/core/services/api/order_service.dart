import 'package:flutter/foundation.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/order_model.dart';

/// Order Service for handling order-related API calls
class OrderService {
  static OrderService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  OrderService._internal();
  
  static OrderService get instance {
    _instance ??= OrderService._internal();
    return _instance!;
  }
  
  /// Create new order with address and payment info
  Future<ApiResponse<OrderModel>> createOrder({
    required Map<String, dynamic> address,
    required Map<String, dynamic> paymentInfo,
    String? notes,
    String? couponCode,
  }) async {
    try {
      final requestData = {
        'address': address,
        'paymentInfo': paymentInfo,
        if (notes != null) 'notes': notes,
        if (couponCode != null) 'couponCode': couponCode,
      };

      debugPrint('🛒 Creating order with data: $requestData');

      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.createOrder,
        data: requestData,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        // Extract the order data from the nested response structure
        final orderData = response.data!['order'] ?? response.data!;
        final order = OrderModel.fromJson(orderData);

        return ApiResponse<OrderModel>(
          success: response.success,
          message: response.message,
          data: order,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderModel>(
        success: false,
        message: response.error ?? 'Failed to create order',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Legacy method for backward compatibility (deprecated)
  @Deprecated('Use createOrder with address and paymentInfo instead')
  Future<ApiResponse<OrderModel>> createOrderLegacy({
    required String shippingAddressId,
    String? billingAddressId,
    String? paymentMethod,
    String? shippingMethod,
    String? notes,
    String? couponCode,
  }) async {
    // This method is kept for backward compatibility
    // In practice, you would need to fetch the address details and convert them
    throw UnimplementedError('Legacy method deprecated. Use createOrder with address and paymentInfo.');
  }
  
  /// Get user's orders
  Future<ApiResponse<OrderListResponse>> getMyOrders({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (status != null) queryParams['status'] = status;
      
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.myOrders,
        queryParameters: queryParams,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final orderListResponse = OrderListResponse.fromJson(response.data!);
        
        return ApiResponse<OrderListResponse>(
          success: response.success,
          message: response.message,
          data: orderListResponse,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderListResponse>(
        success: false,
        message: response.error ?? 'Failed to get orders',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get single order by ID
  Future<ApiResponse<OrderModel>> getOrder(String orderId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.singleOrder}$orderId',
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        debugPrint('🛒 Single Order API Response: ${response.data}');

        // Extract the actual order data from the response wrapper
        final responseData = response.data!;
        final orderData = responseData['data'] ?? responseData;

        debugPrint('🛒 Parsing single order data: ${orderData.keys.toList()}');
        final order = OrderModel.fromJson(orderData);

        return ApiResponse<OrderModel>(
          success: response.success,
          message: response.message,
          data: order,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<OrderModel>(
        success: false,
        message: response.error ?? 'Failed to get order',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Cancel order
  Future<ApiResponse<OrderModel>> cancelOrder({
    required String orderId,
    String? reason,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '${ApiConfig.cancelOrder}$orderId/cancel',
        data: {
          if (reason != null) 'reason': reason,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final order = OrderModel.fromJson(response.data!);
        
        return ApiResponse<OrderModel>(
          success: response.success,
          message: response.message,
          data: order,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderModel>(
        success: false,
        message: response.error ?? 'Failed to cancel order',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get all orders (Admin only)
  Future<ApiResponse<OrderListResponse>> getAllOrders({
    int page = 1,
    int limit = 10,
    String? status,
    String? userId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (status != null) queryParams['status'] = status;
      if (userId != null) queryParams['userId'] = userId;
      
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.allOrders,
        queryParameters: queryParams,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final orderListResponse = OrderListResponse.fromJson(response.data!);
        
        return ApiResponse<OrderListResponse>(
          success: response.success,
          message: response.message,
          data: orderListResponse,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderListResponse>(
        success: false,
        message: response.error ?? 'Failed to get orders',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Update order status (Admin only)
  Future<ApiResponse<OrderModel>> updateOrderStatus({
    required String orderId,
    required String status,
    String? notes,
  }) async {
    try {
      final response = await _apiService.put<Map<String, dynamic>>(
        '${ApiConfig.updateOrderStatus}$orderId',
        data: {
          'status': status,
          if (notes != null) 'notes': notes,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final order = OrderModel.fromJson(response.data!);
        
        return ApiResponse<OrderModel>(
          success: response.success,
          message: response.message,
          data: order,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderModel>(
        success: false,
        message: response.error ?? 'Failed to update order status',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Track order status
  Future<ApiResponse<OrderTrackingModel>> trackOrder(String orderId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.singleOrder}$orderId/tracking',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final tracking = OrderTrackingModel.fromJson(response.data!);
        
        return ApiResponse<OrderTrackingModel>(
          success: response.success,
          message: response.message,
          data: tracking,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<OrderTrackingModel>(
        success: false,
        message: response.error ?? 'Failed to track order',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
}

/// Order list response model
class OrderListResponse {
  final List<OrderModel> orders;
  final int totalOrders;
  final int totalPages;
  final int currentPage;
  
  const OrderListResponse({
    required this.orders,
    required this.totalOrders,
    required this.totalPages,
    required this.currentPage,
  });
  
  factory OrderListResponse.fromJson(Map<String, dynamic> json) {
    return OrderListResponse(
      orders: (json['orders'] as List<dynamic>?)
          ?.map((order) => OrderModel.fromJson(order))
          .toList() ?? [],
      totalOrders: json['totalOrders'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'orders': orders.map((order) => order.toJson()).toList(),
      'totalOrders': totalOrders,
      'totalPages': totalPages,
      'currentPage': currentPage,
    };
  }
}

/// Order item request model
class OrderItemRequest {
  final String productId;
  final int quantity;
  final String? variant;
  
  const OrderItemRequest({
    required this.productId,
    required this.quantity,
    this.variant,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'quantity': quantity,
      if (variant != null) 'variant': variant,
    };
  }
}
