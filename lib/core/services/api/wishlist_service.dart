import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/wishlist_model.dart';

/// Wishlist Service for handling wishlist-related API calls
class WishlistService {
  static WishlistService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  WishlistService._internal();
  
  static WishlistService get instance {
    _instance ??= WishlistService._internal();
    return _instance!;
  }
  
  /// Get user's wishlist
  Future<ApiResponse<WishlistModel>> getWishlist() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.getWishlist,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        // The API returns data.data.items structure
        final dataSection = response.data!;
        final nestedData = dataSection['data'] as Map<String, dynamic>?;

        if (nestedData != null) {
          // Create wishlist from the nested data structure
          final wishlist = WishlistModel.fromJson({
            'items': nestedData['items'] ?? [],
            'itemCount': nestedData['count'] ?? 0,
          });

          // Cache wishlist data locally
          await _cacheWishlistData(wishlist);

          return ApiResponse<WishlistModel>(
            success: response.success,
            message: response.message,
            data: wishlist,
            statusCode: response.statusCode,
          );
        }
      }
      
      // If API fails, try to get cached wishlist
      final cachedWishlist = await _getCachedWishlist();
      if (cachedWishlist != null) {
        return ApiResponse<WishlistModel>(
          success: true,
          message: 'Wishlist retrieved from cache',
          data: cachedWishlist,
        );
      }
      
      return ApiResponse<WishlistModel>(
        success: false,
        message: response.error ?? 'Failed to get wishlist',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      // Fallback to cached wishlist on error
      final cachedWishlist = await _getCachedWishlist();
      if (cachedWishlist != null) {
        return ApiResponse<WishlistModel>(
          success: true,
          message: 'Wishlist retrieved from cache',
          data: cachedWishlist,
        );
      }
      rethrow;
    }
  }
  
  /// Add product to wishlist
  Future<ApiResponse<WishlistModel>> addToWishlist(String productId) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.addToWishlist,
        data: {'productId': productId},
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final wishlist = WishlistModel.fromJson(response.data!);
        await _cacheWishlistData(wishlist);
        
        return ApiResponse<WishlistModel>(
          success: response.success,
          message: response.message,
          data: wishlist,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<WishlistModel>(
        success: false,
        message: response.error ?? 'Failed to add to wishlist',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Remove product from wishlist
  Future<ApiResponse<WishlistModel>> removeFromWishlist(String productId) async {
    try {
      final response = await _apiService.delete<Map<String, dynamic>>(
        '${ApiConfig.removeFromWishlist}$productId',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final wishlist = WishlistModel.fromJson(response.data!);
        await _cacheWishlistData(wishlist);
        
        return ApiResponse<WishlistModel>(
          success: response.success,
          message: response.message,
          data: wishlist,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<WishlistModel>(
        success: false,
        message: response.error ?? 'Failed to remove from wishlist',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Clear entire wishlist
  Future<ApiResponse<void>> clearWishlist() async {
    try {
      final response = await _apiService.delete<void>(
        ApiConfig.clearWishlist,
      );
      
      if (response.isSuccess) {
        await _clearCachedWishlist();
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Check if product is in wishlist
  Future<ApiResponse<bool>> checkWishlist(String productId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.checkWishlist}$productId',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        // API returns 'isInWishlist' not 'isWishlisted'
        final isWishlisted = response.data!['isInWishlist'] ?? response.data!['isWishlisted'] ?? false;

        return ApiResponse<bool>(
          success: response.success,
          message: response.message,
          data: isWishlisted,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<bool>(
        success: false,
        message: response.error ?? 'Failed to check wishlist',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Toggle product in wishlist (add if not present, remove if present)
  Future<ApiResponse<WishlistToggleResult>> toggleWishlist(String productId) async {
    try {
      // First check if product is in wishlist
      final checkResponse = await checkWishlist(productId);
      
      if (checkResponse.isSuccess && checkResponse.data != null) {
        final isWishlisted = checkResponse.data!;
        
        if (isWishlisted) {
          // Remove from wishlist
          final removeResponse = await removeFromWishlist(productId);
          return ApiResponse<WishlistToggleResult>(
            success: removeResponse.success,
            message: removeResponse.message,
            data: WishlistToggleResult(
              isWishlisted: false,
              action: 'removed',
              wishlist: removeResponse.data,
            ),
            statusCode: removeResponse.statusCode,
          );
        } else {
          // Add to wishlist
          final addResponse = await addToWishlist(productId);
          return ApiResponse<WishlistToggleResult>(
            success: addResponse.success,
            message: addResponse.message,
            data: WishlistToggleResult(
              isWishlisted: true,
              action: 'added',
              wishlist: addResponse.data,
            ),
            statusCode: addResponse.statusCode,
          );
        }
      }
      
      return ApiResponse<WishlistToggleResult>(
        success: false,
        message: checkResponse.error ?? 'Failed to toggle wishlist',
        error: checkResponse.error,
        statusCode: checkResponse.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get wishlist item count
  Future<int> getWishlistItemCount() async {
    try {
      final wishlistResponse = await getWishlist();
      if (wishlistResponse.isSuccess && wishlistResponse.data != null) {
        return wishlistResponse.data!.itemCount;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }
  
  /// Check if product is in wishlist (local check)
  Future<bool> isProductInWishlist(String productId) async {
    try {
      final wishlistResponse = await getWishlist();
      if (wishlistResponse.isSuccess && wishlistResponse.data != null) {
        return wishlistResponse.data!.items.any(
          (item) => item.product.id == productId,
        );
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Get wishlist product IDs
  Future<List<String>> getWishlistProductIds() async {
    try {
      final wishlistResponse = await getWishlist();
      if (wishlistResponse.isSuccess && wishlistResponse.data != null) {
        return wishlistResponse.data!.items
            .map((item) => item.product.id)
            .toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }
  
  /// Cache wishlist data locally
  Future<void> _cacheWishlistData(WishlistModel wishlist) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(ApiConfig.wishlistKey, jsonEncode(wishlist.toJson()));
    } catch (e) {
      // Ignore cache errors
    }
  }
  
  /// Get cached wishlist data
  Future<WishlistModel?> _getCachedWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistJson = prefs.getString(ApiConfig.wishlistKey);
      
      if (wishlistJson != null) {
        final wishlistMap = jsonDecode(wishlistJson) as Map<String, dynamic>;
        return WishlistModel.fromJson(wishlistMap);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// Clear cached wishlist data
  Future<void> _clearCachedWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(ApiConfig.wishlistKey);
    } catch (e) {
      // Ignore cache errors
    }
  }
}

/// Wishlist toggle result model
class WishlistToggleResult {
  final bool isWishlisted;
  final String action;
  final WishlistModel? wishlist;
  
  const WishlistToggleResult({
    required this.isWishlisted,
    required this.action,
    this.wishlist,
  });
  
  bool get wasAdded => action == 'added';
  bool get wasRemoved => action == 'removed';
}
