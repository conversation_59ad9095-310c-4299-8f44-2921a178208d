import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/cart_model.dart';

/// Cart Service for handling cart-related API calls
class CartService {
  static CartService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  CartService._internal();
   
  static CartService get instance {
    _instance ??= CartService._internal();
    return _instance!;
  }
  
  /// Get user's cart (supports both authenticated users and guests)
  Future<ApiResponse<CartModel>> getCart() async {
    try {
      // Ensure guest ID exists for guest users
      await _ensureGuestId();

      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.getCart,
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        // The API returns data.cart structure
        final dataSection = response.data!;
        final cartData = dataSection['cart'] as Map<String, dynamic>?;

        if (cartData != null) {
          final cart = CartModel.fromJson(cartData);

          // Cache cart data locally
          await _cacheCartData(cart);

          return ApiResponse<CartModel>(
            success: response.success,
            message: response.message,
            data: cart,
            statusCode: response.statusCode,
          );
        }
      }

      // If API fails, try to get cached cart
      final cachedCart = await _getCachedCart();
      if (cachedCart != null) {
        return ApiResponse<CartModel>(
          success: true,
          message: 'Cart retrieved from cache',
          data: cachedCart,
        );
      }

      return ApiResponse<CartModel>(
        success: false,
        message: response.error ?? 'Failed to get cart',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      // Fallback to cached cart on error
      final cachedCart = await _getCachedCart();
      if (cachedCart != null) {
        return ApiResponse<CartModel>(
          success: true,
          message: 'Cart retrieved from cache',
          data: cachedCart,
        );
      }
      rethrow;
    }
  }
  
  /// Add item to cart
  Future<ApiResponse<CartModel>> addToCart({
    required String productId,
    int quantity = 1,
    String? variant,
  }) async {
    try {
      await _ensureGuestId();
      
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.addToCart,
        data: {
          'productId': productId,
          'quantity': quantity,
          if (variant != null) 'variant': variant,
        },
        fromJson: (data) => data,
      );

      if (response.isSuccess) {
        // After successful add to cart, get the updated cart
        final cartResponse = await getCart();
        if (cartResponse.isSuccess && cartResponse.data != null) {
          return ApiResponse<CartModel>(
            success: true,
            message: response.message,
            data: cartResponse.data!,
            statusCode: response.statusCode,
          );
        }

        // If getting cart fails, still return success
        return ApiResponse<CartModel>(
          success: true,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<CartModel>(
        success: false,
        message: response.error ?? 'Failed to add item to cart',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Update cart item quantity
  Future<ApiResponse<CartModel>> updateCartItem({
    required String productId,
    required int quantity,
  }) async {
    try {
      await _ensureGuestId();

      final response = await _apiService.put<Map<String, dynamic>>(
        ApiConfig.updateCartItem,
        data: {
          'productId': productId,
          'quantity': quantity,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess) {
        // After successful update, get the updated cart with full product data
        final cartResponse = await getCart();
        if (cartResponse.isSuccess && cartResponse.data != null) {
          return ApiResponse<CartModel>(
            success: true,
            message: response.message,
            data: cartResponse.data!,
            statusCode: response.statusCode,
          );
        }

        // If getting cart fails, still return success but without cart data
        return ApiResponse<CartModel>(
          success: true,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<CartModel>(
        success: false,
        message: response.error ?? 'Failed to update cart item',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Remove item from cart
  Future<ApiResponse<CartModel>> removeCartItem(String itemId) async {
    try {
      final response = await _apiService.delete<Map<String, dynamic>>(
        '${ApiConfig.removeCartItem}$itemId',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess) {
        // After successful deletion, get the updated cart with full product data
        final cartResponse = await getCart();
        if (cartResponse.isSuccess && cartResponse.data != null) {
          return ApiResponse<CartModel>(
            success: true,
            message: response.message,
            data: cartResponse.data!,
            statusCode: response.statusCode,
          );
        }

        // If getting cart fails, still return success but without cart data
        return ApiResponse<CartModel>(
          success: true,
          message: response.message,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<CartModel>(
        success: false,
        message: response.error ?? 'Failed to remove cart item',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Clear entire cart
  Future<ApiResponse<void>> clearCart() async {
    try {
      final response = await _apiService.delete<void>(
        ApiConfig.clearCart,
      );
      
      if (response.isSuccess) {
        await _clearCachedCart();
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Merge guest cart with user cart after login
  Future<ApiResponse<CartModel>> mergeCart(String guestId) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.mergeCart,
        data: {'guestId': guestId},
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final cart = CartModel.fromJson(response.data!);
        await _cacheCartData(cart);
        
        return ApiResponse<CartModel>(
          success: response.success,
          message: response.message,
          data: cart,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<CartModel>(
        success: false,
        message: response.error ?? 'Failed to merge cart',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get cart item count
  Future<int> getCartItemCount() async {
    try {
      final cartResponse = await getCart();
      if (cartResponse.isSuccess && cartResponse.data != null) {
        return cartResponse.data!.itemCount;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }
  
  /// Check if product is in cart
  Future<bool> isProductInCart(String productId) async {
    try {
      final cartResponse = await getCart();
      if (cartResponse.isSuccess && cartResponse.data != null) {
        return cartResponse.data!.items.any(
          (item) => item.product.id == productId,
        );
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Get cart item for specific product
  Future<CartItemModel?> getCartItem(String productId) async {
    try {
      final cartResponse = await getCart();
      if (cartResponse.isSuccess && cartResponse.data != null) {
        return cartResponse.data!.items.firstWhere(
          (item) => item.product.id == productId,
          orElse: () => throw StateError('Item not found'),
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// Ensure guest ID exists for guest users
  Future<void> _ensureGuestId() async {
    final isLoggedIn = await _apiService.getAuthToken() != null;
    if (!isLoggedIn) {
      final guestId = await _apiService.getGuestId();
      if (guestId == null) {
        // Generate a new guest ID
        final newGuestId = _generateGuestId();
        await _apiService.setGuestId(newGuestId);
      }
    }
  }
  
  /// Generate a unique guest ID
  String _generateGuestId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return 'guest_${timestamp}_$random';
  }
  
  /// Cache cart data locally
  Future<void> _cacheCartData(CartModel cart) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(ApiConfig.cartKey, jsonEncode(cart.toJson()));
    } catch (e) {
      // Ignore cache errors
    }
  }
  
  /// Get cached cart data
  Future<CartModel?> _getCachedCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getString(ApiConfig.cartKey);
      
      if (cartJson != null) {
        final cartMap = jsonDecode(cartJson) as Map<String, dynamic>;
        return CartModel.fromJson(cartMap);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// Clear cached cart data
  Future<void> _clearCachedCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(ApiConfig.cartKey);
    } catch (e) {
      // Ignore cache errors
    }
  }
}
