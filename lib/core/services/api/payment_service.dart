import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/payment_model.dart';

/// Payment Service for handling payment-related API calls
class PaymentService {
  static PaymentService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  PaymentService._internal();
  
  static PaymentService get instance {
    _instance ??= PaymentService._internal();
    return _instance!;
  }
  
  /// Create payment order (for Razorpay integration)
  Future<ApiResponse<PaymentOrderModel>> createPaymentOrder({
    required String orderId,
    required double amount,
    String currency = 'INR',
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.createPaymentOrder,
        data: {
          'orderId': orderId,
          'amount': amount,
          'currency': currency,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final paymentOrder = PaymentOrderModel.fromJson(response.data!);
        
        return ApiResponse<PaymentOrderModel>(
          success: response.success,
          message: response.message,
          data: paymentOrder,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<PaymentOrderModel>(
        success: false,
        message: response.error ?? 'Failed to create payment order',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Verify payment after successful payment
  Future<ApiResponse<PaymentVerificationModel>> verifyPayment({
    required String orderId,
    required String paymentId,
    required String signature,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.verifyPayment,
        data: {
          'razorpay_order_id': orderId,
          'razorpay_payment_id': paymentId,
          'razorpay_signature': signature,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final verification = PaymentVerificationModel.fromJson(response.data!);
        
        return ApiResponse<PaymentVerificationModel>(
          success: response.success,
          message: response.message,
          data: verification,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<PaymentVerificationModel>(
        success: false,
        message: response.error ?? 'Payment verification failed',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Handle payment failure
  Future<ApiResponse<void>> handlePaymentFailure({
    required String orderId,
    required String paymentId,
    String? errorCode,
    String? errorDescription,
  }) async {
    try {
      final response = await _apiService.post<void>(
        ApiConfig.paymentFailure,
        data: {
          'orderId': orderId,
          'paymentId': paymentId,
          if (errorCode != null) 'errorCode': errorCode,
          if (errorDescription != null) 'errorDescription': errorDescription,
        },
      );
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get payment details
  Future<ApiResponse<PaymentModel>> getPaymentDetails(String paymentId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.paymentDetails}$paymentId',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final payment = PaymentModel.fromJson(response.data!);
        
        return ApiResponse<PaymentModel>(
          success: response.success,
          message: response.message,
          data: payment,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<PaymentModel>(
        success: false,
        message: response.error ?? 'Failed to get payment details',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Request refund
  Future<ApiResponse<RefundModel>> requestRefund({
    required String paymentId,
    required double amount,
    String? reason,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.refundPayment,
        data: {
          'paymentId': paymentId,
          'amount': amount,
          if (reason != null) 'reason': reason,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final refund = RefundModel.fromJson(response.data!);
        
        return ApiResponse<RefundModel>(
          success: response.success,
          message: response.message,
          data: refund,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<RefundModel>(
        success: false,
        message: response.error ?? 'Failed to request refund',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get supported payment methods
  Future<ApiResponse<List<PaymentMethodModel>>> getPaymentMethods() async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        '${ApiConfig.payments}/methods',
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final paymentMethods = response.data!
            .map((method) => PaymentMethodModel.fromJson(method))
            .toList();
        
        return ApiResponse<List<PaymentMethodModel>>(
          success: response.success,
          message: response.message,
          data: paymentMethods,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<List<PaymentMethodModel>>(
        success: false,
        message: response.error ?? 'Failed to get payment methods',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Calculate payment fees
  Future<ApiResponse<PaymentFeesModel>> calculatePaymentFees({
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        '${ApiConfig.payments}/calculate-fees',
        data: {
          'amount': amount,
          'paymentMethod': paymentMethod,
        },
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final fees = PaymentFeesModel.fromJson(response.data!);
        
        return ApiResponse<PaymentFeesModel>(
          success: response.success,
          message: response.message,
          data: fees,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<PaymentFeesModel>(
        success: false,
        message: response.error ?? 'Failed to calculate payment fees',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Get user's payment history
  Future<ApiResponse<PaymentHistoryResponse>> getPaymentHistory({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (status != null) queryParams['status'] = status;
      
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiConfig.payments}/history',
        queryParameters: queryParams,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        final paymentHistory = PaymentHistoryResponse.fromJson(response.data!);
        
        return ApiResponse<PaymentHistoryResponse>(
          success: response.success,
          message: response.message,
          data: paymentHistory,
          statusCode: response.statusCode,
        );
      }
      
      return ApiResponse<PaymentHistoryResponse>(
        success: false,
        message: response.error ?? 'Failed to get payment history',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      rethrow;
    }
  }
}

/// Payment history response model
class PaymentHistoryResponse {
  final List<PaymentModel> payments;
  final int totalPayments;
  final int totalPages;
  final int currentPage;
  
  const PaymentHistoryResponse({
    required this.payments,
    required this.totalPayments,
    required this.totalPages,
    required this.currentPage,
  });
  
  factory PaymentHistoryResponse.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryResponse(
      payments: (json['payments'] as List<dynamic>?)
          ?.map((payment) => PaymentModel.fromJson(payment))
          .toList() ?? [],
      totalPayments: json['totalPayments'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'payments': payments.map((payment) => payment.toJson()).toList(),
      'totalPayments': totalPayments,
      'totalPages': totalPages,
      'currentPage': currentPage,
    };
  }
}
