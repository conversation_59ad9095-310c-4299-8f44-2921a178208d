import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../logging_service.dart';
import '../../models/user_model.dart';
import 'api_config.dart';
import 'api_service.dart';

/// 👤 User Service
/// 
/// Handles user profile management, addresses, and user-related operations
class UserService {
  static UserService? _instance;
  final ApiService _apiService;

  UserService._internal() : _apiService = ApiService.instance;

  static UserService get instance {
    _instance ??= UserService._internal();
    return _instance!;
  }

  /// Get current user from storage
  Future<UserModel?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(ApiConfig.userKey);
      
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        return UserModel.fromJson(userData);
      }
      return null;
    } catch (e) {
      Log.e('Error getting current user from storage', tag: 'USER_SERVICE', error: e);
      return null;
    }
  }

  /// Get user addresses
  Future<ApiResponse<List<Map<String, dynamic>>>> getAddresses() async {
    Log.data('Fetching user addresses', entity: 'addresses', data: {
      'action': 'GET_ADDRESSES',
    });

    try {
      // Get the response as Map (object response with data.addresses structure)
      final response = await _apiService.base.get<Map<String, dynamic>>(
        ApiConfig.userAddresses,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        // Extract addresses directly from response.data (which already contains the addresses)
        final addressesList = response.data!['addresses'] as List<dynamic>? ?? [];

        final addresses = addressesList
            .map((addr) => addr as Map<String, dynamic>)
            .toList();

        Log.data('Addresses fetched successfully', entity: 'addresses', data: {
          'count': addresses.length,
        });

        return ApiResponse<List<Map<String, dynamic>>>(
          success: response.success,
          message: response.message,
          data: addresses,
          statusCode: response.statusCode,
        );
      }

      // If that fails, try nested structure
      final mapResponse = await _apiService.base.get<Map<String, dynamic>>(
        ApiConfig.userAddresses,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (mapResponse.isSuccess && mapResponse.data != null) {
        // Extract addresses from nested structure: data.addresses
        final addressesData = mapResponse.data!['addresses'] as List<dynamic>?;

        if (addressesData != null) {
          final addresses = addressesData
              .map((addr) => addr as Map<String, dynamic>)
              .toList();

          Log.data('Addresses fetched successfully (nested)', entity: 'addresses', data: {
            'count': addresses.length,
          });

          return ApiResponse<List<Map<String, dynamic>>>(
            success: mapResponse.success,
            message: mapResponse.message,
            data: addresses,
            statusCode: mapResponse.statusCode,
          );
        }
      }

      return ApiResponse<List<Map<String, dynamic>>>(
        success: false,
        message: 'Failed to fetch addresses',
        error: 'No valid response received',
        statusCode: mapResponse.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Get addresses API exception', tag: 'USER_SERVICE', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Add new address
  Future<ApiResponse<Map<String, dynamic>>> addAddress({
    required String type, // 'home', 'office', 'other'
    required String name,
    required String phone,
    required String addressLine1,
    String? addressLine2,
    required String city,
    required String state,
    required String postalCode,
    required String country,
    bool isDefault = false,
  }) async {
    Log.data('Adding new address', entity: 'address', data: {
      'action': 'ADD_ADDRESS',
      'type': type,
      'name': name,
      'city': city,
      'state': state,
      'country': country,
      'isDefault': isDefault,
    });

    try {
      // Split name into firstName and lastName
      final nameParts = name.trim().split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      final addressData = {
        'type': type,
        'firstName': firstName,
        'lastName': lastName,
        'phone': phone,
        'addressLine1': addressLine1,
        if (addressLine2 != null && addressLine2.isNotEmpty) 'addressLine2': addressLine2,
        'city': city,
        'state': state,
        'postalCode': postalCode,
        'country': country,
        'isDefault': isDefault,
      };

      final response = await _apiService.base.post<Map<String, dynamic>>(
        ApiConfig.addAddress,
        data: addressData,
        fromJson: (data) => data,
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        // Backend returns: {"message": "Address added successfully", "addresses": [...]}
        if (response.data != null) {
          final message = response.data!['message'] as String?;
          final addresses = response.data!['addresses'] as List<dynamic>?;

          if (message != null && addresses != null && addresses.isNotEmpty) {
            // Get the newly added address (likely the last one)
            final newAddress = addresses.last as Map<String, dynamic>;

            Log.data('Address added successfully', entity: 'address', data: {
              'addressId': newAddress['_id'],
              'type': type,
              'message': message,
            });

            // Return success response - HTTP 200 means success regardless of response.success
            return ApiResponse<Map<String, dynamic>>(
              success: true,
              message: message,
              data: newAddress,
              error: null,
              statusCode: response.statusCode,
            );
          }
        }

        // If we reach here, HTTP was successful but data format was unexpected
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: 'Address added successfully',
          data: {},
          error: null,
          statusCode: response.statusCode,
        );
      }

      Log.data('Add address API failed', entity: 'address', data: {
        'error': response.error,
        'statusCode': response.statusCode,
      });

      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: response.error ?? 'Failed to add address',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Add address API exception', tag: 'USER_SERVICE', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update existing address
  Future<ApiResponse<Map<String, dynamic>>> updateAddress({
    required String addressId,
    String? type,
    String? name,
    String? phone,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    bool? isDefault,
  }) async {
    Log.data('Updating address', entity: 'address', data: {
      'action': 'UPDATE_ADDRESS',
      'addressId': addressId,
    });

    try {
      final updateData = <String, dynamic>{};
      if (type != null) updateData['type'] = type;
      if (name != null) {
        // Split name into firstName and lastName for API
        final nameParts = name.trim().split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : '';
        final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';
        updateData['firstName'] = firstName;
        updateData['lastName'] = lastName;
      }
      if (phone != null) updateData['phone'] = phone;
      if (addressLine1 != null) updateData['addressLine1'] = addressLine1;
      if (addressLine2 != null) updateData['addressLine2'] = addressLine2;
      if (city != null) updateData['city'] = city;
      if (state != null) updateData['state'] = state;
      if (postalCode != null) updateData['postalCode'] = postalCode;
      if (country != null) updateData['country'] = country;
      if (isDefault != null) updateData['isDefault'] = isDefault;

      final response = await _apiService.base.put<Map<String, dynamic>>(
        '${ApiConfig.updateAddress}$addressId',
        data: updateData,
        fromJson: (data) => data,
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        // Backend returns: {"message": "Address updated successfully", "address": {...}}
        if (response.data != null) {
          final message = response.data!['message'] as String?;
          final addressData = response.data!['address'] as Map<String, dynamic>?;

          if (addressData != null) {
            Log.data('Address updated successfully', entity: 'address', data: {
              'addressId': addressId,
              'message': message,
            });

            // Return success response - HTTP 200 means success regardless of response.success
            return ApiResponse<Map<String, dynamic>>(
              success: true,
              message: message ?? 'Address updated successfully',
              data: addressData,
              error: null,
              statusCode: response.statusCode,
            );
          }
        }

        // If we reach here, HTTP was successful but data format was unexpected
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: 'Address updated successfully',
          data: {},
          error: null,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: response.error ?? 'Failed to update address',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Update address API exception', tag: 'USER_SERVICE', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Set address as default
  Future<ApiResponse<Map<String, dynamic>>> setDefaultAddress(String addressId) async {
    Log.data('Setting default address', entity: 'address', data: {
      'action': 'SET_DEFAULT_ADDRESS',
      'addressId': addressId,
    });

    try {
      final response = await _apiService.base.patch<Map<String, dynamic>>(
        '${ApiConfig.setDefaultAddress}$addressId/default',
        fromJson: (data) => data,
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.data('Default address set successfully', entity: 'address', data: {
          'addressId': addressId,
        });

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response.data?['message'] ?? 'Default address set successfully',
          data: response.data,
          statusCode: response.statusCode,
        );
      }

      // If we get here, the status code indicates failure
      final errorMessage = response.data?['message'] ?? 'Failed to set default address';

      Log.data('Set default address failed', entity: 'address', data: {
        'addressId': addressId,
        'error': errorMessage,
        'statusCode': response.statusCode,
      });

      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: errorMessage,
        data: response.data,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.data('Set default address API exception', entity: 'address', data: {
        'addressId': addressId,
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// Delete address
  Future<ApiResponse<void>> deleteAddress(String addressId) async {
    Log.data('Deleting address', entity: 'address', data: {
      'action': 'DELETE_ADDRESS',
      'addressId': addressId,
    });

    try {
      final response = await _apiService.base.delete<void>(
        '${ApiConfig.deleteAddress}$addressId',
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.data('Address deleted successfully', entity: 'address', data: {
          'addressId': addressId,
        });

        // Return success response - HTTP 200 means success regardless of response.success
        return ApiResponse<void>(
          success: true,
          message: 'Address deleted successfully',
          error: null,
          statusCode: response.statusCode,
        );
      } else {
        Log.data('Delete address API failed', entity: 'address', data: {
          'addressId': addressId,
          'error': response.error,
          'statusCode': response.statusCode,
        });

        return response;
      }
    } catch (e, stackTrace) {
      Log.e('Delete address API exception', tag: 'USER_SERVICE', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Validate address completeness
  Future<ApiResponse<Map<String, dynamic>>> validateAddress(String addressId) async {
    Log.data('Validating address', entity: 'address', data: {
      'action': 'VALIDATE_ADDRESS',
      'addressId': addressId,
    });

    try {
      final response = await _apiService.base.post<Map<String, dynamic>>(
        '${ApiConfig.validateAddress}$addressId/validate',
        fromJson: (data) => data,
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.data('Address validation completed', entity: 'address', data: {
          'addressId': addressId,
          'isValid': response.data?['isValid'] ?? false,
        });

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response.data?['message'] ?? 'Address validation completed',
          data: response.data,
          statusCode: response.statusCode,
        );
      }

      // If we get here, the status code indicates failure
      final errorMessage = response.data?['message'] ?? 'Failed to validate address';

      Log.data('Address validation failed', entity: 'address', data: {
        'addressId': addressId,
        'error': errorMessage,
        'statusCode': response.statusCode,
      });

      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: errorMessage,
        data: response.data,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.data('Validate address API exception', entity: 'address', data: {
        'addressId': addressId,
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    Log.auth('Changing password', action: 'CHANGE_PASSWORD_REQUEST');

    try {
      final response = await _apiService.base.put<void>(
        ApiConfig.changePassword,
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );

      if (response.isSuccess) {
        Log.auth('Password changed successfully', action: 'CHANGE_PASSWORD_SUCCESS');
      } else {
        Log.auth('Change password API failed', action: 'CHANGE_PASSWORD_FAILED', data: {
          'error': response.error,
          'statusCode': response.statusCode,
        });
      }

      return response;
    } catch (e, stackTrace) {
      Log.e('Change password API exception', tag: 'USER_SERVICE', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get user statistics (for profile display)
  Future<Map<String, dynamic>?> getUserStats() async {
    try {
      // This could include order count, wishlist count, etc.
      // For now, return basic info
      final user = await getCurrentUser();
      if (user == null) return null;

      return {
        'name': user.name,
        'email': user.email,
        'phone': user.phone,
        'joinDate': user.createdAt,
        'role': user.role,
      };
    } catch (e) {
      Log.e('Error getting user stats', tag: 'USER_SERVICE', error: e);
      return null;
    }
  }

  /// Clear all user data (for logout)
  Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(ApiConfig.userKey);
      Log.data('User data cleared from storage', entity: 'user');
    } catch (e) {
      Log.e('Error clearing user data', tag: 'USER_SERVICE', error: e);
    }
  }
}
