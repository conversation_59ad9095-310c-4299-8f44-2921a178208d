import '../../models/review_model.dart';
import 'api_service.dart';

/// Reviews API service for managing product reviews
class ReviewsService {
  final BaseApiService _baseApiService;

  ReviewsService(ApiService apiService) : _baseApiService = BaseApiService.instance;

  /// Get reviews for a specific product
  Future<ApiResponse<ProductReviewsModel>> getProductReviews(
    String productId, {
    int page = 1,
    int limit = 10,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (sortBy != null) 'sortBy': sortBy,
        if (sortOrder != null) 'sortOrder': sortOrder,
      };

      final response = await _baseApiService.get<Map<String, dynamic>>(
        '${ApiConfig.productReviews}$productId',
        queryParameters: queryParams,
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        // The API returns data.data structure
        final dataSection = response.data!['data'] as Map<String, dynamic>?;
        
        if (dataSection != null) {
          final reviews = ProductReviewsModel.fromJson(dataSection);
          
          return ApiResponse<ProductReviewsModel>(
            success: response.success,
            message: response.message,
            data: reviews,
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse<ProductReviewsModel>(
        success: false,
        message: response.error ?? 'Failed to get product reviews',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse<ProductReviewsModel>(
        success: false,
        message: 'Error loading reviews: ${e.toString()}',
        error: e.toString(),
      );
    }
  }

  /// Add a review for a product
  Future<ApiResponse<ReviewModel>> addReview({
    required String productId,
    required int rating,
    required String comment,
    String? title,
    List<String>? images,
  }) async {
    try {
      final data = {
        'productId': productId,
        'rating': rating,
        'comment': comment,
        if (title != null) 'title': title,
        if (images != null && images.isNotEmpty) 'images': images,
      };

      final response = await _baseApiService.post<Map<String, dynamic>>(
        ApiConfig.addReview,
        data: data,
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        final review = ReviewModel.fromJson(response.data!);
        
        return ApiResponse<ReviewModel>(
          success: response.success,
          message: response.message,
          data: review,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<ReviewModel>(
        success: false,
        message: response.error ?? 'Failed to add review',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse<ReviewModel>(
        success: false,
        message: 'Error adding review: ${e.toString()}',
        error: e.toString(),
      );
    }
  }

  /// Update a review
  Future<ApiResponse<ReviewModel>> updateReview({
    required String reviewId,
    int? rating,
    String? comment,
    String? title,
    List<String>? images,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (rating != null) data['rating'] = rating;
      if (comment != null) data['comment'] = comment;
      if (title != null) data['title'] = title;
      if (images != null) data['images'] = images;

      final response = await _baseApiService.put<Map<String, dynamic>>(
        '${ApiConfig.updateReview}$reviewId',
        data: data,
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        final review = ReviewModel.fromJson(response.data!);
        
        return ApiResponse<ReviewModel>(
          success: response.success,
          message: response.message,
          data: review,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<ReviewModel>(
        success: false,
        message: response.error ?? 'Failed to update review',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse<ReviewModel>(
        success: false,
        message: 'Error updating review: ${e.toString()}',
        error: e.toString(),
      );
    }
  }

  /// Delete a review
  Future<ApiResponse<bool>> deleteReview(String reviewId) async {
    try {
      final response = await _baseApiService.delete<Map<String, dynamic>>(
        '${ApiConfig.deleteReview}$reviewId',
        fromJson: (data) => data,
      );

      return ApiResponse<bool>(
        success: response.success,
        message: response.message,
        data: response.isSuccess,
        statusCode: response.statusCode,
        error: response.error,
      );
    } catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: 'Error deleting review: ${e.toString()}',
        error: e.toString(),
        data: false,
      );
    }
  }

  /// Mark review as helpful
  Future<ApiResponse<bool>> markReviewHelpful(String reviewId) async {
    try {
      final response = await _baseApiService.post<Map<String, dynamic>>(
        '${ApiConfig.markReviewHelpful}$reviewId',
        fromJson: (data) => data,
      );

      return ApiResponse<bool>(
        success: response.success,
        message: response.message,
        data: response.isSuccess,
        statusCode: response.statusCode,
        error: response.error,
      );
    } catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: 'Error marking review as helpful: ${e.toString()}',
        error: e.toString(),
        data: false,
      );
    }
  }
}
