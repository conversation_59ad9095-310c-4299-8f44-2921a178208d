import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/user_model.dart';
import '../logging_service.dart';

/// Authentication Service for handling user authentication
class AuthService {
  static AuthService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;
  
  AuthService._internal();
  
  static AuthService get instance {
    _instance ??= AuthService._internal();
    return _instance!;
  }
  
  /// Register new user
  Future<ApiResponse<AuthResponse>> signup({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    String? phone,
  }) async {
    Log.auth('Starting user signup', action: 'SIGNUP_REQUEST', data: {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'passwordLength': password.length,
      'hasPhone': phone != null && phone.isNotEmpty,
    });

    try {
      final requestData = {
        'name': '$firstName $lastName', // Combined name for backward compatibility
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'password': password,
      };

      // Add phone if provided
      if (phone != null && phone.isNotEmpty) {
        requestData['phone'] = phone;
      }

      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.signup,
        data: requestData,
      );

      Log.api('Signup API response received', endpoint: 'signup', data: {
        'success': response.isSuccess,
        'statusCode': response.statusCode,
        'hasData': response.data != null,
      });
      
      if (response.isSuccess && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);

        Log.auth('Signup successful, saving auth data', action: 'SIGNUP_SUCCESS', data: {
          'userId': authResponse.user.id,
          'userName': authResponse.user.name,
          'userEmail': authResponse.user.email,
          'hasToken': authResponse.token.isNotEmpty,
        });

        // Save token and user data
        await _saveAuthData(authResponse);

        Log.data('Auth data saved successfully', entity: 'user', data: {
          'userId': authResponse.user.id,
        });

        return ApiResponse<AuthResponse>(
          success: response.success,
          message: response.message,
          data: authResponse,
          statusCode: response.statusCode,
        );
      }

      Log.auth('Signup API failed', action: 'SIGNUP_FAILED', data: {
        'error': response.error,
        'statusCode': response.statusCode,
        'message': response.message,
      });

      return ApiResponse<AuthResponse>(
        success: false,
        message: response.error ?? 'Registration failed',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Signup API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }
  
  /// Login user
  Future<ApiResponse<AuthResponse>> login({
    required String email,
    required String password,
  }) async {
    Log.auth('Starting user login', action: 'LOGIN_REQUEST', data: {
      'email': email,
      'passwordLength': password.length,
    });

    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConfig.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      Log.api('Login API response received', endpoint: 'login', data: {
        'success': response.isSuccess,
        'statusCode': response.statusCode,
        'hasData': response.data != null,
      });
      
      if (response.isSuccess && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);

        Log.auth('Login successful, saving auth data', action: 'LOGIN_SUCCESS', data: {
          'userId': authResponse.user.id,
          'userName': authResponse.user.name,
          'userEmail': authResponse.user.email,
          'hasToken': authResponse.token.isNotEmpty,
        });

        // Save token and user data
        await _saveAuthData(authResponse);

        Log.data('Auth data saved successfully', entity: 'user', data: {
          'userId': authResponse.user.id,
        });

        // Merge guest cart if exists
        Log.auth('Merging guest cart after login', action: 'CART_MERGE_START');
        await _mergeGuestCart();
        Log.auth('Guest cart merge completed', action: 'CART_MERGE_COMPLETE');

        return ApiResponse<AuthResponse>(
          success: response.success,
          message: response.message,
          data: authResponse,
          statusCode: response.statusCode,
        );
      }

      Log.auth('Login API failed', action: 'LOGIN_FAILED', data: {
        'error': response.error,
        'statusCode': response.statusCode,
        'message': response.message,
      });

      return ApiResponse<AuthResponse>(
        success: false,
        message: response.error ?? 'Login failed',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Login API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }
  
  /// Get stored authentication token
  Future<String?> getStoredToken() async {
    try {
      return await _apiService.getAuthToken();
    } catch (e) {
      Log.e('Error getting stored token', tag: 'AUTH_SERVICE', error: e);
      return null;
    }
  }

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated() async {
    try {
      final token = await getStoredToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get user profile
  Future<ApiResponse<UserModel>> getProfile() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiConfig.profile,
        fromJson: (data) => data,
      );
      
      if (response.isSuccess && response.data != null) {
        // Extract user data from nested structure: data.user
        final userData = response.data!['user'] as Map<String, dynamic>?;

        if (userData != null) {
          final user = UserModel.fromJson(userData);

          // Update stored user data
          await _saveUserData(user);

          return ApiResponse<UserModel>(
            success: response.success,
            message: response.message,
            data: user,
            statusCode: response.statusCode,
          );
        } else {
          Log.w('Profile response missing user data', tag: 'AUTH_SERVICE', data: {
            'responseData': response.data,
          });

          return ApiResponse<UserModel>(
            success: false,
            message: 'Invalid profile response format',
            error: 'User data not found in response',
            statusCode: response.statusCode,
          );
        }
      }
      
      return ApiResponse<UserModel>(
        success: false,
        message: response.error ?? 'Failed to get profile',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Get profile API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }

  /// Update user profile
  Future<ApiResponse<UserModel>> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    Map<String, dynamic>? address,
  }) async {
    Log.auth('Starting profile update', action: 'UPDATE_PROFILE_REQUEST', data: {
      'hasName': name != null,
      'hasEmail': email != null,
      'hasPhone': phone != null,
      'hasDateOfBirth': dateOfBirth != null,
      'hasGender': gender != null,
      'hasAddress': address != null,
    });

    try {
      // Prepare update data (only include non-null values)
      final Map<String, dynamic> updateData = {};
      if (name != null) updateData['name'] = name;
      if (email != null) updateData['email'] = email;
      if (phone != null) updateData['phone'] = phone;
      if (dateOfBirth != null) updateData['dateOfBirth'] = dateOfBirth;
      if (gender != null) updateData['gender'] = gender;
      if (address != null) updateData['address'] = address;

      final response = await _apiService.put<Map<String, dynamic>>(
        ApiConfig.updateProfile,
        data: updateData,
        fromJson: (data) => data,
      );

      Log.api('Update profile API response received', endpoint: 'profile', data: {
        'success': response.isSuccess,
        'statusCode': response.statusCode,
        'hasData': response.data != null,
      });

      if (response.isSuccess && response.data != null) {
        final user = UserModel.fromJson(response.data!['user'] ?? response.data!);

        Log.auth('Profile updated successfully', action: 'UPDATE_PROFILE_SUCCESS', data: {
          'userId': user.id,
          'userName': user.name,
          'userEmail': user.email,
        });

        // Update stored user data
        await _saveUserData(user);

        return ApiResponse<UserModel>(
          success: response.success,
          message: response.message,
          data: user,
          statusCode: response.statusCode,
        );
      }

      Log.auth('Profile update API failed', action: 'UPDATE_PROFILE_FAILED', data: {
        'error': response.error,
        'statusCode': response.statusCode,
        'message': response.message,
      });

      return ApiResponse<UserModel>(
        success: false,
        message: response.error ?? 'Failed to update profile',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Update profile API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }

  /// Update only phone number (separate endpoint for convenience)
  Future<ApiResponse<UserModel>> updatePhone(String phone) async {
    return updateProfile(phone: phone);
  }

  /// Update user address
  Future<ApiResponse<UserModel>> updateAddress({
    required String street,
    required String city,
    required String state,
    required String zipCode,
    required String country,
    String? landmark,
    String? type, // 'home', 'work', 'other'
  }) async {
    final address = {
      'street': street,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
      if (landmark != null) 'landmark': landmark,
      if (type != null) 'type': type,
    };

    return updateProfile(address: address);
  }
  
  /// Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiService.put<void>(
        ApiConfig.changePassword,
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );

      return response;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  /// Send forgot password email
  Future<ApiResponse<void>> forgotPassword({
    required String email,
  }) async {
    Log.auth('Starting forgot password request', action: 'FORGOT_PASSWORD_REQUEST', data: {
      'email': email,
    });

    try {
      final response = await _apiService.post<void>(
        ApiConfig.forgotPassword,
        data: {
          'email': email,
        },
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.auth('Forgot password email sent successfully', action: 'FORGOT_PASSWORD_SUCCESS', data: {
          'email': email,
        });

        return ApiResponse<void>(
          success: true,
          message: response.message ?? 'Password reset link sent to your email',
          error: null,
          statusCode: response.statusCode,
        );
      }

      Log.auth('Forgot password API failed', action: 'FORGOT_PASSWORD_FAILED', data: {
        'error': response.error,
        'statusCode': response.statusCode,
        'message': response.message,
      });

      return ApiResponse<void>(
        success: false,
        message: response.error ?? 'Failed to send reset email',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Forgot password API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }

  /// Reset password with token
  Future<ApiResponse<void>> resetPassword({
    required String email,
    required String token,
    required String password,
    required String passwordConfirmation,
  }) async {
    Log.auth('Starting reset password request', action: 'RESET_PASSWORD_REQUEST', data: {
      'email': email,
      'hasToken': token.isNotEmpty,
      'passwordLength': password.length,
    });

    try {
      final response = await _apiService.post<void>(
        ApiConfig.resetPassword,
        data: {
          'email': email,
          'token': token,
          'password': password,
          'password_confirmation': passwordConfirmation,
        },
      );

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.auth('Password reset successfully', action: 'RESET_PASSWORD_SUCCESS', data: {
          'email': email,
        });

        return ApiResponse<void>(
          success: true,
          message: response.message ?? 'Password reset successfully',
          error: null,
          statusCode: response.statusCode,
        );
      }

      Log.auth('Reset password API failed', action: 'RESET_PASSWORD_FAILED', data: {
        'error': response.error,
        'statusCode': response.statusCode,
        'message': response.message,
      });

      return ApiResponse<void>(
        success: false,
        message: response.error ?? 'Failed to reset password',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e, stackTrace) {
      Log.e('Reset password API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);
      throw _handleAuthError(e);
    }
  }
  
  /// Logout user
  Future<ApiResponse<void>> logout() async {
    Log.auth('Starting logout process', action: 'LOGOUT_START');

    try {
      // Call logout API endpoint first
      final response = await _apiService.post<void>(
        ApiConfig.logout,
      );

      // Clear all stored data regardless of API response
      await _apiService.clearAuthToken();
      await _apiService.clearGuestId();
      await _clearUserData();

      // Check HTTP status code first - if 200-299, it's successful
      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        Log.auth('Logout successful', action: 'LOGOUT_SUCCESS');

        return ApiResponse<void>(
          success: true,
          message: 'Logged out successfully',
          error: null,
          statusCode: response.statusCode,
        );
      } else {
        Log.auth('Logout API failed but local data cleared', action: 'LOGOUT_API_FAILED', data: {
          'error': response.error,
          'statusCode': response.statusCode,
        });

        // Still return success since local data is cleared
        return ApiResponse<void>(
          success: true,
          message: 'Logged out successfully',
          error: null,
          statusCode: 200,
        );
      }
    } catch (e, stackTrace) {
      Log.e('Logout API exception', tag: 'AUTH_SERVICE', error: e, stackTrace: stackTrace);

      // Even if API call fails, clear local data
      try {
        await _apiService.clearAuthToken();
        await _apiService.clearGuestId();
        await _clearUserData();

        Log.auth('Local data cleared despite API error', action: 'LOGOUT_LOCAL_CLEARED');
      } catch (clearError) {
        Log.e('Error clearing local data during logout', tag: 'AUTH_SERVICE', error: clearError);
      }

      // Return success since the important part (clearing local data) succeeded
      return ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
        error: null,
        statusCode: 200,
      );
    }
  }
  
  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _apiService.getAuthToken();
    return token != null && token.isNotEmpty;
  }
  
  /// Get current user from storage
  Future<UserModel?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(ApiConfig.userKey);
      
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// Save authentication data
  Future<void> _saveAuthData(AuthResponse authResponse) async {
    await _apiService.setAuthToken(authResponse.token);
    await _saveUserData(authResponse.user);

    // Mark that user has had a token (for onboarding logic)
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_ever_had_token', true);
    } catch (e) {
      // Don't fail auth if this fails
      Log.e('Error marking user has had token', tag: 'AUTH_SERVICE', error: e);
    }
  }
  
  /// Save user data to storage
  Future<void> _saveUserData(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(ApiConfig.userKey, jsonEncode(user.toJson()));
  }
  
  /// Clear user data from storage
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ApiConfig.userKey);
  }
  
  /// Merge guest cart after login
  Future<void> _mergeGuestCart() async {
    try {
      final guestId = await _apiService.getGuestId();
      if (guestId != null) {
        await _apiService.post(
          ApiConfig.mergeCart,
          data: {'guestId': guestId},
        );
        await _apiService.clearGuestId();
      }
    } catch (e) {
      // Ignore merge cart errors
    }
  }
  
  /// Handle authentication errors
  Exception _handleAuthError(dynamic error) {
    if (error is ApiException) {
      switch (error.statusCode) {
        case ApiConfig.statusUnauthorized:
          return const ApiException(
            message: 'Invalid credentials',
            statusCode: ApiConfig.statusUnauthorized,
          );
        case ApiConfig.statusConflict:
          return const ApiException(
            message: 'User already exists',
            statusCode: ApiConfig.statusConflict,
          );
        default:
          return error;
      }
    }
    return error as Exception;
  }
}

/// Authentication response model
class AuthResponse {
  final String message;
  final UserModel user;
  final String token;
  
  const AuthResponse({
    required this.message,
    required this.user,
    required this.token,
  });
  
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      message: json['message'] ?? '',
      user: UserModel.fromJson(json['user']),
      token: json['token'] ?? '',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'user': user.toJson(),
      'token': token,
    };
  }
}
