// Export all API services and configurations
export 'api_config.dart';
export 'base_api_service.dart';
export 'auth_service.dart';
export 'product_service.dart';
export 'cart_service.dart';
export 'category_service.dart';
export 'wishlist_service.dart';
export 'reviews_service.dart';

export 'order_service.dart';
export 'payment_service.dart';
export 'coupon_service.dart';

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'api_config.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import 'user_service.dart';
import 'product_service.dart';
import 'cart_service.dart';
import 'category_service.dart';
import 'wishlist_service.dart';
import 'reviews_service.dart';

import 'order_service.dart';
import 'payment_service.dart';
import 'coupon_service.dart';

/// Main API Service class that provides access to all services
class ApiService {
  static ApiService? _instance;

  // Service instances
  late final BaseApiService _baseService;
  late final AuthService _authService;
  late final ProductService _productService;
  late final CartService _cartService;
  late final CategoryService _categoryService;
  late final WishlistService _wishlistService;
  late final ReviewsService _reviewsService;

  late final OrderService _orderService;
  late final PaymentService _paymentService;
  late final CouponService _couponService;

  // Private constructor
  ApiService._internal() {
    _initializeServices();
  }

  /// Singleton instance
  static ApiService get instance {
    _instance ??= ApiService._internal();
    return _instance!;
  }

  /// Initialize all services
  void _initializeServices() {
    _baseService = BaseApiService.instance;
    _authService = AuthService.instance;
    _productService = ProductService.instance;
    _cartService = CartService.instance;
    _categoryService = CategoryService.instance;
    _wishlistService = WishlistService.instance;
    _reviewsService = ReviewsService(this);

    _orderService = OrderService.instance;
    _paymentService = PaymentService.instance;
    _couponService = CouponService(this);
  }

  // Service getters
  BaseApiService get base => _baseService;
  AuthService get auth => _authService;
  UserService get user => UserService.instance;
  ProductService get products => _productService;
  CartService get cart => _cartService;
  CategoryService get categories => _categoryService;
  WishlistService get wishlist => _wishlistService;
  ReviewsService get reviews => _reviewsService;

  OrderService get orders => _orderService;
  PaymentService get payments => _paymentService;
  CouponService get coupons => _couponService;
  
  /// Initialize API service with configuration
  Future<void> initialize({
    String? baseUrl,
    Map<String, String>? defaultHeaders,
    Duration? timeout,
  }) async {
    try {
      // Load saved authentication token
      await _baseService.getAuthToken();
      
      // Load guest ID if not authenticated
      final token = await _baseService.getAuthToken();
      if (token == null) {
        await _baseService.getGuestId();
      }
      
      if (kDebugMode) {
        debugPrint('API Service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('API Service initialization error: $e');
      }
    }
  }
  
  /// Check API connectivity
  Future<bool> checkConnectivity() async {
    try {
      final response = await _baseService.get('/health');
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }
  
  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      await _baseService.clearAuthToken();
      await _baseService.clearGuestId();
      
      if (kDebugMode) {
        debugPrint('API cache cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing API cache: $e');
      }
    }
  }
  
  /// Get API status information
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _instance != null,
      'base_url': _baseService.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// API Service Provider for dependency injection
class ApiServiceProvider {
  static final Map<Type, dynamic> _services = {};
  
  /// Register a service
  static void register<T>(T service) {
    _services[T] = service;
  }
  
  /// Get a service
  static T get<T>() {
    final service = _services[T];
    if (service == null) {
      throw Exception('Service of type $T not registered');
    }
    return service as T;
  }
  
  /// Check if service is registered
  static bool isRegistered<T>() {
    return _services.containsKey(T);
  }
  
  /// Clear all services
  static void clear() {
    _services.clear();
  }
  
  /// Initialize all services
  static void initializeServices() {
    register<BaseApiService>(BaseApiService.instance);
    register<AuthService>(AuthService.instance);
    register<UserService>(UserService.instance);
    register<ProductService>(ProductService.instance);
    register<CartService>(CartService.instance);
    register<CategoryService>(CategoryService.instance);
    register<WishlistService>(WishlistService.instance);
    register<OrderService>(OrderService.instance);
    register<PaymentService>(PaymentService.instance);
  }
}

/// API Error Handler
class ApiErrorHandler {
  /// Handle API errors globally
  static String handleError(dynamic error) {
    if (error is ApiException) {
      // Always prefer the actual API error message if it exists and is meaningful
      if (error.message.isNotEmpty &&
          !error.message.toLowerCase().contains('exception') &&
          !error.message.toLowerCase().contains('error occurred')) {
        return error.message;
      }

      // Fallback to status code specific messages only if API message is empty/generic
      switch (error.statusCode) {
        case 400:
          return 'Invalid request. Please check your input and try again.';
        case 401:
          return 'Invalid credentials. Please check your email and password.';
        case 403:
          return 'Access denied. You don\'t have permission to perform this action.';
        case 404:
          return 'Resource not found. Please check your information and try again.';
        case 409:
          // Handle specific 409 conflicts for different contexts
          if (error.message.toLowerCase().contains('email')) {
            return 'This email is already registered. Please use a different email or try logging in.';
          } else if (error.message.toLowerCase().contains('user')) {
            return 'User already exists. Please try logging in instead.';
          }
          return 'This information is already in use. Please try with different details.';
        case 422:
          return 'Please check your input and try again.';
        case 429:
          return 'Too many requests. Please wait a moment and try again.';
        case 500:
          return 'Server error. Please try again later.';
        case 502:
        case 503:
        case 504:
          return 'Service temporarily unavailable. Please try again later.';
        default:
          return 'An error occurred. Please try again.';
      }
    } else if (error is NetworkException) {
      return error.message;
    } else {
      return 'An unexpected error occurred. Please check your connection and try again.';
    }
  }
  
  /// Check if error is recoverable
  static bool isRecoverableError(dynamic error) {
    if (error is ApiException) {
      return error.statusCode != null && 
             error.statusCode! >= 500 && 
             error.statusCode! < 600;
    } else if (error is NetworkException) {
      return true;
    }
    return false;
  }
  
  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    final message = handleError(error);
    
    // Customize messages for better UX
    if (message.toLowerCase().contains('network')) {
      return 'Please check your internet connection and try again';
    } else if (message.toLowerCase().contains('server')) {
      return 'Our servers are temporarily unavailable. Please try again in a few minutes';
    } else if (message.toLowerCase().contains('timeout')) {
      return 'Request timed out. Please try again';
    }
    
    return message;
  }
}

/// API Response Extensions
extension ApiResponseExtensions<T> on ApiResponse<T> {
  /// Check if response has data
  bool get hasData => data != null;
  
  /// Get data or throw exception
  T get dataOrThrow {
    if (isSuccess && hasData) {
      return data!;
    }
    throw ApiException(
      message: error ?? message,
      statusCode: statusCode,
    );
  }
  
  /// Get data or return default value
  T dataOr(T defaultValue) {
    return hasData ? data! : defaultValue;
  }
  
  /// Transform data
  ApiResponse<R> map<R>(R Function(T) transform) {
    if (hasData && data != null) {
      return ApiResponse<R>(
        success: success,
        message: message,
        data: transform(data as T),
        error: error,
        statusCode: statusCode,
      );
    }

    return ApiResponse<R>(
      success: success,
      message: message,
      error: error,
      statusCode: statusCode,
    );
  }
}

/// API Constants
class ApiConstants {
  // HTTP Methods
  static const String get = 'GET';
  static const String post = 'POST';
  static const String put = 'PUT';
  static const String patch = 'PATCH';
  static const String delete = 'DELETE';
  
  // Content Types
  static const String jsonContentType = 'application/json';
  static const String formDataContentType = 'multipart/form-data';
  static const String urlEncodedContentType = 'application/x-www-form-urlencoded';
  
  // Common Headers
  static const String authorizationHeader = 'Authorization';
  static const String contentTypeHeader = 'Content-Type';
  static const String acceptHeader = 'Accept';
  static const String userAgentHeader = 'User-Agent';
  static const String guestIdHeader = 'guest-id';
  
  // Cache Control
  static const String cacheControlHeader = 'Cache-Control';
  static const String noCacheValue = 'no-cache';
  static const String maxAgeValue = 'max-age=';
  
  // API Versions
  static const String apiVersion1 = 'v1';
  static const String apiVersion2 = 'v2';
}
