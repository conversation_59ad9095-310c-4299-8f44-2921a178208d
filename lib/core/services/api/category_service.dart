import 'base_api_service.dart';
import 'api_config.dart';
import '../../models/category_model.dart';
import '../../services/logging_service.dart';

/// Category Service for handling category-related API calls
class CategoryService {
  static CategoryService? _instance;
  final BaseApiService _apiService = BaseApiService.instance;

  CategoryService._internal();

  static CategoryService get instance {
    _instance ??= CategoryService._internal();
    return _instance!;
  }

  /// Get all categories - {{baseUrl}}/api/categories
  Future<ApiResponse<List<CategoryModel>>> getCategories() async {
    try {
      Log.api('Calling get categories endpoint', endpoint: 'categories');

      // Use no type parameter to get raw response
      final response = await _apiService.get(
        ApiConfig.allCategories,
      );

      if (response.isSuccess && response.data != null) {
        // The data should be a List<dynamic> directly from the API
        final categoriesData = response.data as List<dynamic>;

        final categories = categoriesData
            .map((categoryJson) => CategoryModel.fromJson(categoryJson as Map<String, dynamic>))
            .toList();

        Log.api('Categories parsed successfully', endpoint: 'categories', data: {
          'count': categories.length,
        });

        return ApiResponse<List<CategoryModel>>(
          success: true,
          message: 'Categories retrieved successfully',
          data: categories,
          error: null,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: response.error ?? 'Failed to get categories',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.api('Categories API exception', endpoint: 'categories', data: {
        'error': e.toString(),
      });
      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: 'Error loading categories: $e',
        error: e.toString(),
        statusCode: null,
      );
    }
  }

  /// Get root categories (categories without parent)
  Future<ApiResponse<List<CategoryModel>>> getRootCategories() async {
    try {
      final categoriesResponse = await getCategories();

      if (categoriesResponse.isSuccess && categoriesResponse.data != null) {
        final rootCategories = categoriesResponse.data!
            .where((category) => category.parent == null)
            .toList();

        return ApiResponse<List<CategoryModel>>(
          success: true,
          message: 'Root categories retrieved successfully',
          data: rootCategories,
        );
      }

      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: categoriesResponse.error ?? 'Failed to get root categories',
        error: categoriesResponse.error,
      );
    } catch (e) {
      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: 'Error loading root categories: $e',
        error: e.toString(),
      );
    }
  }

  /// Get category tree - {{baseUrl}}/api/categories/tree
  Future<ApiResponse<CategoryTreeResponse>> getCategoryTree() async {
    try {
      Log.api('Calling get category tree endpoint', endpoint: 'categories/tree');

      final response = await _apiService.get(
        ApiConfig.categoryTree,
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final treeData = responseData['data'] as Map<String, dynamic>;

          // Parse categories and meta separately
          final categoriesData = treeData['categories'] as List<dynamic>? ?? [];
          final metaData = treeData['meta'] as Map<String, dynamic>? ?? {};

          final categories = categoriesData
              .map((cat) => CategoryTreeModel.fromJson(cat as Map<String, dynamic>))
              .toList();

          final meta = CategoryTreeMeta.fromJson(metaData);

          final categoryTree = CategoryTreeResponse(
            categories: categories,
            meta: meta,
          );

          Log.api('Category tree parsed successfully', endpoint: 'categories/tree', data: {
            'categoriesCount': categoryTree.categories.length,
            'totalCategories': categoryTree.meta.totalCategories,
          });

          return ApiResponse<CategoryTreeResponse>(
            success: true,
            message: 'Category tree retrieved successfully',
            data: categoryTree,
            error: null,
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse<CategoryTreeResponse>(
        success: false,
        message: response.error ?? 'Failed to get category tree',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.api('Category tree API exception', endpoint: 'categories/tree', data: {
        'error': e.toString(),
      });
      return ApiResponse<CategoryTreeResponse>(
        success: false,
        message: 'Error loading category tree: $e',
        error: e.toString(),
        statusCode: null,
      );
    }
  }

  /// Get category by ID - {{baseUrl}}/api/categories/{{categoryId}}
  Future<ApiResponse<CategoryModel>> getCategoryById(String categoryId) async {
    try {
      Log.api('Calling get category by ID endpoint', endpoint: 'categories/$categoryId');

      final response = await _apiService.get(
        '${ApiConfig.allCategories}/$categoryId',
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          // API returns single category object in data field
          final categoryData = responseData['data'] as Map<String, dynamic>;
          final category = CategoryModel.fromJson(categoryData);

          Log.api('Category by ID parsed successfully', endpoint: 'categories/$categoryId', data: {
            'categoryId': category.id,
            'categoryName': category.name,
            'hasSubcategories': category.subcategories.isNotEmpty,
          });

          return ApiResponse<CategoryModel>(
            success: true,
            message: responseData['message'] as String? ?? 'Category retrieved successfully',
            data: category,
            error: null,
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse<CategoryModel>(
        success: false,
        message: response.error ?? 'Failed to get category',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.api('Category by ID API exception', endpoint: 'categories/$categoryId', data: {
        'error': e.toString(),
      });
      return ApiResponse<CategoryModel>(
        success: false,
        message: 'Error loading category: $e',
        error: e.toString(),
        statusCode: null,
      );
    }
  }

  /// Get category with its related categories (parent and children) by ID
  Future<ApiResponse<CategoryWithRelated>> getCategoryWithRelated(String categoryId) async {
    try {
      Log.api('Calling get category with related endpoint', endpoint: 'categories/$categoryId');

      final response = await _apiService.get(
        '${ApiConfig.allCategories}/$categoryId',
      );

      if (response.isSuccess && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final data = responseData['data'];

          // Handle both array and single object responses
          List<CategoryModel> categories = [];

          if (data is List) {
            // Array response - parse all categories
            categories = data
                .map((categoryJson) => CategoryModel.fromJson(categoryJson as Map<String, dynamic>))
                .toList();
          } else if (data is Map<String, dynamic>) {
            // Single object response - just parse the one category
            categories = [CategoryModel.fromJson(data)];
          }

          if (categories.isNotEmpty) {
            // First category is the main requested category
            final mainCategory = categories.first;

            // Find parent and children from the response or from the category data itself
            CategoryModel? parentCategory;
            List<CategoryModel> childCategories = [];

            // If we have multiple categories, find relationships
            if (categories.length > 1) {
              for (final category in categories) {
                if (category.id != mainCategory.id) {
                  // Check if this is a parent (main category has this as parent)
                  if (mainCategory.parent?.id == category.id) {
                    parentCategory = category;
                  }
                  // Check if this is a child (has main category as parent)
                  else if (category.parent?.id == mainCategory.id) {
                    childCategories.add(category);
                  }
                }
              }
            } else {
              // Single category response - convert subcategories to CategoryModel
              childCategories = mainCategory.subcategories.map((sub) => CategoryModel(
                id: sub.id,
                name: sub.name,
                slug: sub.slug,
                description: null,
                image: null,
                parent: CategoryParent(
                  id: mainCategory.id,
                  name: mainCategory.name,
                  slug: mainCategory.slug,
                ),
                productCount: 0,
                subcategories: [],
                createdAt: DateTime.now().toIso8601String(),
                updatedAt: DateTime.now().toIso8601String(),
              )).toList();

              // If the category has a parent reference, convert it to CategoryModel
              if (mainCategory.parent != null) {
                final parent = mainCategory.parent!;
                parentCategory = CategoryModel(
                  id: parent.id,
                  name: parent.name,
                  slug: parent.slug,
                  description: null,
                  image: null,
                  parent: null,
                  productCount: 0,
                  subcategories: [],
                  createdAt: DateTime.now().toIso8601String(),
                  updatedAt: DateTime.now().toIso8601String(),
                );
              }
            }

            final categoryWithRelated = CategoryWithRelated(
              category: mainCategory,
              parent: parentCategory,
              children: childCategories,
            );

            Log.api('Category with related parsed successfully', endpoint: 'categories/$categoryId', data: {
              'categoryId': mainCategory.id,
              'categoryName': mainCategory.name,
              'hasParent': parentCategory != null,
              'childrenCount': childCategories.length,
              'responseType': data is List ? 'array' : 'single',
            });

            return ApiResponse<CategoryWithRelated>(
              success: true,
              message: responseData['message'] as String? ?? 'Category with related data retrieved successfully',
              data: categoryWithRelated,
              error: null,
              statusCode: response.statusCode,
            );
          }
        }
      }

      return ApiResponse<CategoryWithRelated>(
        success: false,
        message: response.error ?? 'Failed to get category with related data',
        error: response.error,
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.api('Category with related API exception', endpoint: 'categories/$categoryId', data: {
        'error': e.toString(),
      });
      return ApiResponse<CategoryWithRelated>(
        success: false,
        message: 'Error loading category with related data: $e',
        error: e.toString(),
        statusCode: null,
      );
    }
  }

  /// Get subcategories for a parent category
  Future<ApiResponse<List<CategoryModel>>> getSubcategories(String parentId) async {
    try {
      final categoriesResponse = await getCategories();

      if (categoriesResponse.isSuccess && categoriesResponse.data != null) {
        final subcategories = categoriesResponse.data!
            .where((category) => category.parent?.id == parentId)
            .toList();

        return ApiResponse<List<CategoryModel>>(
          success: true,
          message: 'Subcategories retrieved successfully',
          data: subcategories,
        );
      }

      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: categoriesResponse.error ?? 'Failed to get subcategories',
        error: categoriesResponse.error,
      );
    } catch (e) {
      return ApiResponse<List<CategoryModel>>(
        success: false,
        message: 'Error loading subcategories: $e',
        error: e.toString(),
      );
    }
  }
}