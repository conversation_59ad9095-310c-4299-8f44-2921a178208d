import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../logging_service.dart';
import '../../models/coupon_model.dart';
import 'api_service.dart';
import 'api_config.dart';

/// 🎫 Coupon Service
/// Handles all coupon-related API operations
class CouponService {
  final ApiService _apiService;

  CouponService(this._apiService);

  /// Get all available coupons
  Future<ApiResponse<List<CouponModel>>> getAvailableCoupons() async {
    Log.data('Fetching available coupons', entity: 'coupons', data: {
      'action': 'GET_AVAILABLE_COUPONS',
    });

    try {
      final response = await _apiService.base.get<Map<String, dynamic>>(
        ApiConfig.coupons,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        debugPrint('🎫 Coupons API response: ${response.data}');

        // The response.data contains the parsed response, check if it has nested data
        final responseData = response.data!;
        List<dynamic> couponsList;

        if (responseData['data'] is List) {
          // Direct array in data field
          couponsList = responseData['data'] as List<dynamic>;
        } else if (responseData['data'] is Map && responseData['data']['data'] is List) {
          // Nested data.data structure
          couponsList = responseData['data']['data'] as List<dynamic>;
        } else {
          couponsList = [];
        }
        
        final coupons = couponsList
            .map((couponData) => CouponModel.fromJson(couponData))
            .toList();

        Log.data('Coupons fetched successfully', entity: 'coupons', data: {
          'count': coupons.length,
        });

        return ApiResponse<List<CouponModel>>(
          success: response.success,
          message: response.message,
          data: coupons,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse<List<CouponModel>>(
        success: false,
        message: response.error ?? 'Failed to fetch coupons',
        data: [],
        statusCode: response.statusCode,
      );
    } catch (e) {
      Log.data('Get coupons API exception', entity: 'coupons', data: {
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// Validate a coupon code
  Future<ApiResponse<CouponValidationResult>> validateCoupon({
    required String couponCode,
    required double orderAmount,
  }) async {
    Log.data('Validating coupon', entity: 'coupon', data: {
      'action': 'VALIDATE_COUPON',
      'couponCode': couponCode,
      'orderAmount': orderAmount,
    });

    try {
      // Make the API call and handle both success and error responses
      final response = await _apiService.base.post<Map<String, dynamic>>(
        ApiConfig.validateCoupon,
        data: {
          'code': couponCode,
          'orderAmount': orderAmount,
        },
        fromJson: (data) => data as Map<String, dynamic>,
      );

      debugPrint('🎫 Coupon validation response: ${response.data}');
      debugPrint('🎫 Response status code: ${response.statusCode}');
      debugPrint('🎫 Response success: ${response.isSuccess}');
      debugPrint('🎫 Response error: ${response.error}');
      debugPrint('🎫 Response message: ${response.message}');

      // Check if this is an error response (when API returns 404 with error message)
      if (!response.isSuccess && response.error != null) {
        // This is an error response, create an invalid coupon result
        final errorMessage = response.error ?? response.message ?? 'Invalid coupon code';

        final validationResult = CouponValidationResult(
          isValid: false,
          discountAmount: 0,
          finalAmount: 0,
          errorMessage: errorMessage,
        );

        Log.data('Coupon validation failed', entity: 'coupon', data: {
          'couponCode': couponCode,
          'isValid': false,
          'errorMessage': errorMessage,
        });

        return ApiResponse<CouponValidationResult>(
          success: false,
          message: errorMessage,
          data: validationResult,
          statusCode: response.statusCode,
        );
      }

      // Extract the validation data from the response (success case)
      final responseData = response.data ?? {};
      final validationData = responseData['data'] as Map<String, dynamic>? ?? {};

      // Parse the validation result from the extracted data
      final validationResult = CouponValidationResult.fromJson(validationData);

      Log.data('Coupon validation completed', entity: 'coupon', data: {
        'couponCode': couponCode,
        'isValid': validationResult.isValid,
        'discountAmount': validationResult.discountAmount,
        'finalAmount': validationResult.finalAmount,
        'errorMessage': validationResult.errorMessage,
      });

      // Return the result with the exact API message
      return ApiResponse<CouponValidationResult>(
        success: validationResult.isValid,
        message: validationResult.isValid
            ? (responseData['message'] ?? 'Coupon applied successfully')
            : validationResult.errorMessage,
        data: validationResult,
        statusCode: response.statusCode,
      );
    } on DioException catch (dioError) {
      // Handle DioException specifically to extract error message from response
      debugPrint('🎫 DioException caught: ${dioError.response?.data}');
      debugPrint('🎫 DioException status code: ${dioError.response?.statusCode}');

      String errorMessage = 'Invalid coupon code';
      if (dioError.response?.data != null) {
        final errorData = dioError.response!.data;
        debugPrint('🎫 Error data type: ${errorData.runtimeType}');
        debugPrint('🎫 Error data: $errorData');

        if (errorData is Map<String, dynamic> && errorData['message'] != null) {
          errorMessage = errorData['message'];
          debugPrint('🎫 Extracted error message: $errorMessage');
        } else if (errorData is String) {
          // Sometimes the response might be a JSON string
          try {
            final Map<String, dynamic> parsedData = jsonDecode(errorData);
            if (parsedData['message'] != null) {
              errorMessage = parsedData['message'];
              debugPrint('🎫 Extracted error message from JSON string: $errorMessage');
            }
          } catch (e) {
            debugPrint('🎫 Failed to parse error data as JSON: $e');
          }
        }
      }

      Log.data('Validate coupon API error', entity: 'coupon', data: {
        'couponCode': couponCode,
        'error': errorMessage,
        'statusCode': dioError.response?.statusCode,
      });

      debugPrint('🎫 Returning error response with message: $errorMessage');

      return ApiResponse<CouponValidationResult>(
        success: false,
        message: errorMessage,
        data: CouponValidationResult(
          isValid: false,
          discountAmount: 0,
          finalAmount: 0,
          errorMessage: errorMessage,
        ),
        statusCode: dioError.response?.statusCode,
      );
    } catch (e) {
      debugPrint('🎫 General exception caught: $e');

      Log.data('Validate coupon API exception', entity: 'coupon', data: {
        'couponCode': couponCode,
        'error': e.toString(),
      });

      // Try to extract error message from the exception
      String errorMessage = 'Failed to validate coupon';

      // Check if it's an ApiException (thrown by base API service)
      if (e is ApiException) {
        errorMessage = e.message;
        debugPrint('🎫 Extracted message from ApiException: $errorMessage');
      } else if (e.toString().contains('ApiException:')) {
        // Extract message from ApiException string representation
        final exceptionString = e.toString();
        final messageStart = exceptionString.indexOf('ApiException: ') + 'ApiException: '.length;
        if (messageStart > 'ApiException: '.length - 1) {
          errorMessage = exceptionString.substring(messageStart);
          debugPrint('🎫 Extracted message from ApiException string: $errorMessage');
        }
      } else if (e.toString().contains('404') || e.toString().contains('bad response')) {
        errorMessage = 'Invalid coupon code';
      }

      debugPrint('🎫 Using error message: $errorMessage');

      // Return invalid result for exceptions
      return ApiResponse<CouponValidationResult>(
        success: false,
        message: errorMessage,
        data: CouponValidationResult(
          isValid: false,
          discountAmount: 0,
          finalAmount: 0,
          errorMessage: errorMessage,
        ),
      );
    }
  }

  /// Apply coupon to order (for checkout)
  Future<ApiResponse<CouponValidationResult>> applyCoupon({
    required String couponCode,
    required double orderAmount,
    Map<String, dynamic>? additionalData,
  }) async {
    Log.data('Applying coupon to order', entity: 'coupon', data: {
      'action': 'APPLY_COUPON',
      'couponCode': couponCode,
      'orderAmount': orderAmount,
    });

    // For now, apply coupon is the same as validate coupon
    // But this can be extended for order-specific logic
    return validateCoupon(
      couponCode: couponCode,
      orderAmount: orderAmount,
    );
  }

  /// Remove applied coupon
  Future<ApiResponse<void>> removeCoupon() async {
    Log.data('Removing applied coupon', entity: 'coupon', data: {
      'action': 'REMOVE_COUPON',
    });

    // This is a local operation for now
    // Can be extended to call API if needed
    return const ApiResponse<void>(
      success: true,
      message: 'Coupon removed successfully',
      data: null,
    );
  }
}
