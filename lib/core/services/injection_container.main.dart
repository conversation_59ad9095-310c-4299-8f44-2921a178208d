part of 'injection_container.dart';

final sl = GetIt.instance;

Future<void> init() async {
  final prefs = await SharedPreferences.getInstance();
  sl
    ..registerLazySingleton(() => CacheHelper(sl()))
    ..registerLazySingleton(() => prefs);

  // Load saved theme mode on app startup
  final cacheHelper = CacheHelper(prefs);
  cacheHelper.getThemeMode(); // This will load and set the theme in Cache.instance
}
