import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';

/// Service for handling QR code generation and validation for orders
class QRService {
  static QRService? _instance;
  
  QRService._internal();
  
  static QRService get instance {
    _instance ??= QRService._internal();
    return _instance!;
  }
  
  /// Generate a unique QR code data for an order
  /// Format: GMB_ORDER_{orderId}_{timestamp}_{hash}
  String generateOrderQRData(String orderId, String orderNumber) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final baseData = 'GMB_ORDER_${orderId}_${orderNumber}_$timestamp';
    
    // Create a hash for security
    final bytes = utf8.encode(baseData);
    final hash = sha256.convert(bytes).toString().substring(0, 8);
    
    final qrData = '${baseData}_$hash';
    
    debugPrint('🔍 Generated QR Data: $qrData');
    return qrData;
  }
  
  /// Validate and extract order information from QR code data
  QROrderInfo? validateOrderQRData(String qrData) {
    try {
      debugPrint('🔍 Validating QR Data: $qrData');
      
      // Check if it's our format
      if (!qrData.startsWith('GMB_ORDER_')) {
        debugPrint('🔍 Invalid QR format - not GMB order');
        return null;
      }
      
      // Split the data
      final parts = qrData.split('_');
      if (parts.length < 6) {
        debugPrint('🔍 Invalid QR format - insufficient parts');
        return null;
      }
      
      final orderId = parts[2];
      final orderNumber = parts[3];
      final timestamp = int.tryParse(parts[4]);
      final providedHash = parts[5];
      
      if (timestamp == null) {
        debugPrint('🔍 Invalid QR format - invalid timestamp');
        return null;
      }
      
      // Verify hash
      final baseData = 'GMB_ORDER_${orderId}_${orderNumber}_$timestamp';
      final bytes = utf8.encode(baseData);
      final expectedHash = sha256.convert(bytes).toString().substring(0, 8);
      
      if (providedHash != expectedHash) {
        debugPrint('🔍 Invalid QR - hash mismatch');
        return null;
      }
      
      debugPrint('🔍 Valid QR Data - OrderID: $orderId, OrderNumber: $orderNumber');
      
      return QROrderInfo(
        orderId: orderId,
        orderNumber: orderNumber,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
      );
    } catch (e) {
      debugPrint('🔍 Error validating QR data: $e');
      return null;
    }
  }
  
  /// Check if QR data is for an order
  bool isOrderQR(String qrData) {
    return qrData.startsWith('GMB_ORDER_');
  }
}

/// Information extracted from order QR code
class QROrderInfo {
  final String orderId;
  final String orderNumber;
  final DateTime timestamp;
  
  QROrderInfo({
    required this.orderId,
    required this.orderNumber,
    required this.timestamp,
  });
  
  @override
  String toString() {
    return 'QROrderInfo(orderId: $orderId, orderNumber: $orderNumber, timestamp: $timestamp)';
  }
}
