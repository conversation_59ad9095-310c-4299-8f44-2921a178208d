import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';

/// Service for handling QR code generation and validation for orders
class QRService {
  static QRService? _instance;
  
  QRService._internal();
  
  static QRService get instance {
    _instance ??= QRService._internal();
    return _instance!;
  }
  
  /// Generate a unique QR code data for an order
  /// Format: Web URL that works with or without app
  String generateOrderQRData(String orderId, String orderNumber) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final baseData = 'GMB_ORDER_${orderId}_${orderNumber}_$timestamp';

    // Create a hash for security
    final bytes = utf8.encode(baseData);
    final hash = sha256.convert(bytes).toString().substring(0, 8);

    // Generate a web URL that can handle both app and web users
    // This URL should point to your website/web app
    final qrData = 'https://ghanshyam-murti-bhandar.com/order/$orderId?verify=$hash&t=$timestamp';

    debugPrint('🔍 Generated QR Data: $qrData');
    return qrData;
  }
  
  /// Validate and extract order information from QR code data
  QROrderInfo? validateOrderQRData(String qrData) {
    try {
      debugPrint('🔍 Validating QR Data: $qrData');

      // Check if it's a URL format (new format)
      if (qrData.startsWith('https://ghanshyam-murti-bhandar.com/order/')) {
        return _validateUrlFormat(qrData);
      }

      // Check if it's the old format
      if (qrData.startsWith('GMB_ORDER_')) {
        return _validateOldFormat(qrData);
      }

      debugPrint('🔍 Invalid QR format - not recognized');
      return null;
    } catch (e) {
      debugPrint('🔍 Error validating QR data: $e');
      return null;
    }
  }

  /// Validate URL format QR code
  QROrderInfo? _validateUrlFormat(String qrData) {
    try {
      final uri = Uri.parse(qrData);
      final pathSegments = uri.pathSegments;

      if (pathSegments.length < 2 || pathSegments[0] != 'order') {
        debugPrint('🔍 Invalid URL format - wrong path');
        return null;
      }

      final orderId = pathSegments[1];
      final verify = uri.queryParameters['verify'];
      final timestampStr = uri.queryParameters['t'];

      if (verify == null || timestampStr == null) {
        debugPrint('🔍 Invalid URL format - missing parameters');
        return null;
      }

      final timestamp = int.tryParse(timestampStr);
      if (timestamp == null) {
        debugPrint('🔍 Invalid URL format - invalid timestamp');
        return null;
      }

      // For URL format, we'll use orderId as orderNumber for now
      // In a real implementation, you might want to fetch this from API
      debugPrint('🔍 Valid URL QR Data - OrderID: $orderId');

      return QROrderInfo(
        orderId: orderId,
        orderNumber: orderId, // Using orderId as orderNumber for URL format
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
      );
    } catch (e) {
      debugPrint('🔍 Error validating URL format: $e');
      return null;
    }
  }

  /// Validate old format QR code (backward compatibility)
  QROrderInfo? _validateOldFormat(String qrData) {
    try {
      // Split the data
      final parts = qrData.split('_');
      if (parts.length < 6) {
        debugPrint('🔍 Invalid old format - insufficient parts');
        return null;
      }

      final orderId = parts[2];
      final orderNumber = parts[3];
      final timestamp = int.tryParse(parts[4]);
      final providedHash = parts[5];

      if (timestamp == null) {
        debugPrint('🔍 Invalid old format - invalid timestamp');
        return null;
      }

      // Verify hash
      final baseData = 'GMB_ORDER_${orderId}_${orderNumber}_$timestamp';
      final bytes = utf8.encode(baseData);
      final expectedHash = sha256.convert(bytes).toString().substring(0, 8);

      if (providedHash != expectedHash) {
        debugPrint('🔍 Invalid old format - hash mismatch');
        return null;
      }

      debugPrint('🔍 Valid old format QR Data - OrderID: $orderId, OrderNumber: $orderNumber');

      return QROrderInfo(
        orderId: orderId,
        orderNumber: orderNumber,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
      );
    } catch (e) {
      debugPrint('🔍 Error validating old format: $e');
      return null;
    }
  }
  
  /// Check if QR data is for an order
  bool isOrderQR(String qrData) {
    return qrData.startsWith('GMB_ORDER_') ||
           qrData.startsWith('https://ghanshyam-murti-bhandar.com/order/');
  }
}

/// Information extracted from order QR code
class QROrderInfo {
  final String orderId;
  final String orderNumber;
  final DateTime timestamp;
  
  QROrderInfo({
    required this.orderId,
    required this.orderNumber,
    required this.timestamp,
  });
  
  @override
  String toString() {
    return 'QROrderInfo(orderId: $orderId, orderNumber: $orderNumber, timestamp: $timestamp)';
  }
}
