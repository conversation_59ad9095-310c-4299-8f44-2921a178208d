import 'package:flutter/foundation.dart';
import 'api/api_service.dart';
import 'logging_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 🔐 Authentication Guard Service
/// 
/// Handles authentication state checking and token validation
class AuthGuard {
  static AuthGuard? _instance;
  
  AuthGuard._internal();
  
  static AuthGuard get instance {
    _instance ??= AuthGuard._internal();
    return _instance!;
  }
  
  /// Check if user is currently authenticated
  Future<bool> isAuthenticated() async {
    try {
      Log.auth('Checking authentication status', action: 'AUTH_CHECK_START');
      
      // Get stored token
      final token = await ApiService.instance.auth.getStoredToken();
      
      if (token == null || token.isEmpty) {
        Log.auth('No token found', action: 'AUTH_CHECK_NO_TOKEN');
        return false;
      }
      
      Log.auth('Token found, validating', action: 'AUTH_CHECK_TOKEN_FOUND', data: {
        'hasToken': true,
        'tokenLength': token.length,
      });
      
      // Validate token by trying to get user profile
      final profileResponse = await ApiService.instance.auth.getProfile();
      
      if (profileResponse.isSuccess) {
        Log.auth('Token is valid', action: 'AUTH_CHECK_VALID', data: {
          'userId': profileResponse.data?.id,
          'userEmail': profileResponse.data?.email,
        });
        return true;
      } else {
        Log.auth('Token is invalid', action: 'AUTH_CHECK_INVALID', data: {
          'error': profileResponse.error,
          'statusCode': profileResponse.statusCode,
        });
        
        // Clear invalid token
        await ApiService.instance.auth.logout();
        return false;
      }
    } catch (e, stackTrace) {
      Log.e('Authentication check failed', 
            tag: 'AUTH_GUARD', 
            error: e, 
            stackTrace: stackTrace);
      return false;
    }
  }
  
  /// Check if user should see onboarding
  Future<bool> isFirstTime() async {
    try {
      // Check if user has completed onboarding before
      // We use a separate flag for this, not just token presence
      final prefs = await SharedPreferences.getInstance();
      final hasCompletedOnboarding = prefs.getBool('has_completed_onboarding') ?? false;

      // If user has completed onboarding before, never show it again
      if (hasCompletedOnboarding) {
        return false;
      }

      // For new users, check if they have ever had a token (signed up before)
      final hasEverHadToken = prefs.getBool('has_ever_had_token') ?? false;

      // Only show onboarding for truly first-time users
      return !hasEverHadToken;
    } catch (e) {
      Log.e('Error checking first time status', tag: 'AUTH_GUARD', error: e);
      return false; // Default to not first time to avoid onboarding issues
    }
  }

  /// Mark onboarding as completed
  Future<void> markOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_onboarding', true);
      await prefs.setBool('has_ever_had_token', true);
    } catch (e) {
      Log.e('Error marking onboarding completed', tag: 'AUTH_GUARD', error: e);
    }
  }

  /// Mark that user has had a token (for tracking first-time users)
  Future<void> markUserHasHadToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_ever_had_token', true);
    } catch (e) {
      Log.e('Error marking user has had token', tag: 'AUTH_GUARD', error: e);
    }
  }
  
  /// Get authentication redirect path
  Future<String?> getAuthRedirect() async {
    try {
      final isAuth = await isAuthenticated();
      final isFirst = await isFirstTime();
      
      Log.auth('Determining auth redirect', action: 'AUTH_REDIRECT', data: {
        'isAuthenticated': isAuth,
        'isFirstTime': isFirst,
      });
      
      if (isFirst) {
        return '/onboarding';
      } else if (!isAuth) {
        return '/login';
      } else {
        return '/home'; // User is authenticated, go to home
      }
    } catch (e) {
      Log.e('Error determining auth redirect', tag: 'AUTH_GUARD', error: e);
      return '/login'; // Default to login on error
    }
  }
  
  /// Check if route requires authentication
  bool requiresAuth(String path) {
    const publicRoutes = [
      '/',
      '/onboarding',
      '/login',
      '/signup',
      '/forgot-password',
    ];
    
    return !publicRoutes.contains(path);
  }
  
  /// Handle authentication state changes
  void onAuthStateChanged(bool isAuthenticated) {
    Log.auth('Auth state changed', action: 'AUTH_STATE_CHANGE', data: {
      'isAuthenticated': isAuthenticated,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Quick token check without API call (for performance)
  Future<bool> hasStoredToken() async {
    try {
      final token = await ApiService.instance.auth.getStoredToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  /// Get current user info if authenticated
  Future<Map<String, dynamic>?> getCurrentUser() async {
    try {
      if (!await isAuthenticated()) return null;
      
      final profileResponse = await ApiService.instance.auth.getProfile();
      if (profileResponse.isSuccess && profileResponse.data != null) {
        final user = profileResponse.data!;
        return {
          'id': user.id,
          'name': user.name,
          'email': user.email,
          'role': user.role,
        };
      }
      return null;
    } catch (e) {
      Log.e('Error getting current user', tag: 'AUTH_GUARD', error: e);
      return null;
    }
  }
  
  /// Force logout and clear all auth data
  Future<void> forceLogout() async {
    try {
      Log.auth('Force logout initiated', action: 'FORCE_LOGOUT');
      final response = await ApiService.instance.auth.logout();

      if (response.isSuccess) {
        Log.auth('Force logout successful', action: 'FORCE_LOGOUT_SUCCESS');
      } else {
        Log.auth('Force logout API failed but local data cleared', action: 'FORCE_LOGOUT_API_FAILED');
      }

      onAuthStateChanged(false);
    } catch (e) {
      Log.e('Error during force logout', tag: 'AUTH_GUARD', error: e);
      // Still trigger auth state change since local data should be cleared
      onAuthStateChanged(false);
    }
  }
}

/// 🛡️ Route Guard Helper
class RouteGuard {
  /// Check if user can access a route
  static Future<String?> checkAccess(String path) async {
    final authGuard = AuthGuard.instance;
    
    Log.ui('Route access check', context: {
      'path': path,
      'requiresAuth': authGuard.requiresAuth(path),
    });
    
    // Public routes - always allow
    if (!authGuard.requiresAuth(path)) {
      // But if user is already authenticated and tries to access login/signup,
      // redirect to home
      if ((path == '/login' || path == '/signup') && await authGuard.isAuthenticated()) {
        Log.ui('Authenticated user accessing auth page, redirecting to home');
        return '/home';
      }
      return null; // Allow access
    }
    
    // Protected routes - check authentication
    final isAuthenticated = await authGuard.isAuthenticated();
    if (!isAuthenticated) {
      Log.ui('Unauthenticated user accessing protected route, redirecting to login');
      return '/login';
    }
    
    return null; // Allow access
  }
  
  /// Get initial route based on auth state
  static Future<String> getInitialRoute() async {
    final authGuard = AuthGuard.instance;
    final redirect = await authGuard.getAuthRedirect();
    
    Log.lifecycle('Initial route determined', context: {
      'route': redirect,
    });
    
    return redirect ?? '/login';
  }
}
