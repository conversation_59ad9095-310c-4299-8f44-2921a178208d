import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// 📝 Logging Service for the application
/// 
/// Provides structured logging with different levels and contexts
class LoggingService {
  static LoggingService? _instance;
  late final Logger _logger;
  
  LoggingService._internal() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 3,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.info : Level.warning,
    );
  }
  
  static LoggingService get instance {
    _instance ??= LoggingService._internal();
    return _instance!;
  }
  
  /// 🐛 Debug logs - for development only
  void debug(String message, {String? tag, dynamic data}) {
    _logger.d(_formatMessage(message, tag), error: data);
  }
  
  /// ℹ️ Info logs - general information
  void info(String message, {String? tag, dynamic data}) {
    _logger.i(_formatMessage(message, tag), error: data);
  }
  
  /// ⚠️ Warning logs - potential issues
  void warning(String message, {String? tag, dynamic data}) {
    _logger.w(_formatMessage(message, tag), error: data);
  }
  
  /// ❌ Error logs - errors that occurred
  void error(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _logger.e(_formatMessage(message, tag), error: error, stackTrace: stackTrace);
  }
  
  /// 💥 Fatal logs - critical errors
  void fatal(String message, {String? tag, dynamic error, StackTrace? stackTrace}) {
    _logger.f(_formatMessage(message, tag), error: error, stackTrace: stackTrace);
  }
  
  /// 🔐 Auth-specific logging
  void auth(String message, {String? action, dynamic data}) {
    info(message, tag: 'AUTH${action != null ? ':$action' : ''}', data: data);
  }
  
  /// 🛒 API-specific logging
  void api(String message, {String? endpoint, dynamic data}) {
    info(message, tag: 'API${endpoint != null ? ':$endpoint' : ''}', data: data);
  }
  
  /// 🎯 User action logging
  void userAction(String action, {Map<String, dynamic>? context}) {
    info('User action: $action', tag: 'USER_ACTION', data: context);
  }
  
  /// 📊 Performance logging
  void performance(String operation, Duration duration, {dynamic data}) {
    info('$operation completed in ${duration.inMilliseconds}ms', 
         tag: 'PERFORMANCE', data: data);
  }
  
  /// 🔄 Network request logging
  void networkRequest(String method, String url, {Map<String, dynamic>? headers, dynamic body}) {
    debug('$method $url', tag: 'NETWORK_REQUEST', data: {
      'headers': headers,
      'body': body,
    });
  }
  
  /// 📥 Network response logging
  void networkResponse(String method, String url, int statusCode, {dynamic response}) {
    if (statusCode >= 200 && statusCode < 300) {
      debug('$method $url -> $statusCode', tag: 'NETWORK_RESPONSE', data: response);
    } else {
      warning('$method $url -> $statusCode', tag: 'NETWORK_RESPONSE', data: response);
    }
  }
  
  /// 🚨 Security event logging
  void security(String event, {Map<String, dynamic>? context}) {
    warning('Security event: $event', tag: 'SECURITY', data: context);
  }
  
  /// Format message with tag
  String _formatMessage(String message, String? tag) {
    return tag != null ? '[$tag] $message' : message;
  }
  
  /// 📈 Log app lifecycle events
  void lifecycle(String event, {Map<String, dynamic>? context}) {
    info('App lifecycle: $event', tag: 'LIFECYCLE', data: context);
  }
  
  /// 🎨 UI event logging
  void ui(String event, {String? screen, Map<String, dynamic>? context}) {
    debug('UI event: $event${screen != null ? ' on $screen' : ''}', 
          tag: 'UI', data: context);
  }
  
  /// 💾 Data operation logging
  void data(String operation, {String? entity, dynamic data}) {
    debug('Data operation: $operation${entity != null ? ' for $entity' : ''}', 
          tag: 'DATA', data: data);
  }
}

/// 🎯 Convenient logging extensions
extension LoggingExtensions on Object {
  /// Quick debug log
  void logDebug(String message, {String? tag}) {
    LoggingService.instance.debug(message, tag: tag, data: this);
  }
  
  /// Quick info log
  void logInfo(String message, {String? tag}) {
    LoggingService.instance.info(message, tag: tag, data: this);
  }
  
  /// Quick error log
  void logError(String message, {String? tag, StackTrace? stackTrace}) {
    LoggingService.instance.error(message, tag: tag, error: this, stackTrace: stackTrace);
  }
}

/// 📝 Logging helper functions
class Log {
  static final LoggingService _service = LoggingService.instance;
  
  static void d(String message, {String? tag, dynamic data}) => 
      _service.debug(message, tag: tag, data: data);
  
  static void i(String message, {String? tag, dynamic data}) => 
      _service.info(message, tag: tag, data: data);
  
  static void w(String message, {String? tag, dynamic data}) => 
      _service.warning(message, tag: tag, data: data);
  
  static void e(String message, {String? tag, dynamic error, StackTrace? stackTrace}) => 
      _service.error(message, tag: tag, error: error, stackTrace: stackTrace);
  
  static void f(String message, {String? tag, dynamic error, StackTrace? stackTrace}) => 
      _service.fatal(message, tag: tag, error: error, stackTrace: stackTrace);
  
  // Specialized logging methods
  static void auth(String message, {String? action, dynamic data}) => 
      _service.auth(message, action: action, data: data);
  
  static void api(String message, {String? endpoint, dynamic data}) => 
      _service.api(message, endpoint: endpoint, data: data);
  
  static void userAction(String action, {Map<String, dynamic>? context}) => 
      _service.userAction(action, context: context);
  
  static void performance(String operation, Duration duration, {dynamic data}) => 
      _service.performance(operation, duration, data: data);
  
  static void network(String method, String url, int statusCode, {dynamic data}) => 
      _service.networkResponse(method, url, statusCode, response: data);
  
  static void security(String event, {Map<String, dynamic>? context}) => 
      _service.security(event, context: context);
  
  static void lifecycle(String event, {Map<String, dynamic>? context}) => 
      _service.lifecycle(event, context: context);
  
  static void ui(String event, {String? screen, Map<String, dynamic>? context}) => 
      _service.ui(event, screen: screen, context: context);
  
  static void data(String operation, {String? entity, dynamic data}) => 
      _service.data(operation, entity: entity, data: data);
}
