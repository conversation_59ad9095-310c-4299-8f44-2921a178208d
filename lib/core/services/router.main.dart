part of 'router.dart';

final router = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (_, __) {
        // Show splash screen which will handle navigation
        return const SplashView();
      },
    ),
    // Onboarding route
    GoRoute(
      path: OnBoardingView.path,
      redirect: (context, state) async {
        // If user is already authenticated, redirect to home
        final isAuthenticated = await AuthGuard.instance.isAuthenticated();
        if (isAuthenticated) {
          return '/home';
        }
        return null;
      },
      builder: (_, __) => OnBoardingView(),
    ),
    // Auth routes
    GoRoute(
      path: LoginView.path,
      redirect: (context, state) async {
        // If user is already authenticated, redirect to home
        final isAuthenticated = await AuthGuard.instance.isAuthenticated();
        if (isAuthenticated) {
          return '/home';
        }
        return null;
      },
      builder: (_, __) => LoginView(),
    ),
    GoRoute(
      path: SignupView.path,
      redirect: (context, state) async {
        // If user is already authenticated, redirect to home
        final isAuthenticated = await AuthGuard.instance.isAuthenticated();
        if (isAuthenticated) {
          return '/home';
        }
        return null;
      },
      builder: (_, __) => SignupView(),
    ),
    GoRoute(
      path: ForgotPasswordView.path,
      redirect: (context, state) async {
        // If user is already authenticated, redirect to home
        final isAuthenticated = await AuthGuard.instance.isAuthenticated();
        if (isAuthenticated) {
          return '/home';
        }
        return null;
      },
      builder: (_, __) => ForgotPasswordView(),
    ),

    // Dashboard with nested routes
    ShellRoute(
      redirect: (context, state) async {
        // Check if user is authenticated for all dashboard routes
        final isAuthenticated = await AuthGuard.instance.isAuthenticated();
        if (!isAuthenticated) {
          return '/login';
        }
        return null;
      },
      builder: (context, state, child) {
        return DashboardView(state: state, child: child);
      },
      routes: [
        GoRoute(path: HomeView.path, builder: (_, __) => HomeView()),
        GoRoute(
          path: SearchView.path,
          builder: (context, state) {
            final category = state.uri.queryParameters['category'];
            final query = state.uri.queryParameters['query'];
            return SearchView(
              initialCategory: category,
              initialQuery: query,
            );
          },
        ),
        GoRoute(path: CategoriesView.path, builder: (_, __) => CategoriesView()),
        GoRoute(path: CartView.path, builder: (_, __) => CartView()),
        GoRoute(path: FavoritesView.path, builder: (_, __) => FavoritesView()),
        GoRoute(path: ProfileView.path, builder: (_, __) => ProfileView()),
      ],
    ),

    // Profile sub-routes (outside shell to have their own app bar)
    GoRoute(path: EditProfileView.path, builder: (_, __) => EditProfileView()),
    GoRoute(path: ChangePasswordView.path, builder: (_, __) => ChangePasswordView()),
    GoRoute(path: MyOrdersView.path, builder: (_, __) => MyOrdersView()),
    GoRoute(path: AddressesView.path, builder: (_, __) => AddressesView()),
    GoRoute(path: PaymentMethodsView.path, builder: (_, __) => PaymentMethodsView()),
    GoRoute(path: NotificationsView.path, builder: (_, __) => NotificationsView()),
    GoRoute(path: HelpSupportView.path, builder: (_, __) => HelpSupportView()),
    GoRoute(path: AboutView.path, builder: (_, __) => AboutView()),
    GoRoute(path: SettingsView.path, builder: (_, __) => SettingsView()),
    GoRoute(path: QRScannerView.path, builder: (_, __) => QRScannerView()),

    // Web order view for QR code fallback
    GoRoute(
      path: '/order/:orderId',
      builder: (context, state) {
        final orderId = state.pathParameters['orderId']!;
        final verify = state.uri.queryParameters['verify'];
        final timestamp = state.uri.queryParameters['t'];

        return WebOrderView(
          orderId: orderId,
          verify: verify,
          timestamp: timestamp,
        );
      },
    ),

    // Add new address and payment method routes
    GoRoute(path: AddAddressView.path, builder: (_, __) => AddAddressView()),
    GoRoute(path: AddPaymentMethodView.path, builder: (_, __) => AddPaymentMethodView()),
  ],
);
