{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e025ea8168e7f361777a2c24e316569906b", "buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CODE_SIGN_IDENTITY": "-", "COPY_PHASE_STRIP": "NO", "DEAD_CODE_STRIPPING": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "MACOSX_DEPLOYMENT_TARGET": "10.14", "MTL_ENABLE_DEBUG_INFO": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02491ce0b95e267568e25ae7780a1372cb", "name": "Debug"}, {"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e023b4e622549547751db70167aba3359c6", "buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CODE_SIGN_IDENTITY": "-", "COPY_PHASE_STRIP": "NO", "DEAD_CODE_STRIPPING": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "MACOSX_DEPLOYMENT_TARGET": "10.14", "MTL_ENABLE_DEBUG_INFO": "NO", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O"}, "guid": "18c1723432283e0cc55f10a6dcfd9e027768264cd287a1e0904457fceae1a862", "name": "Release"}, {"baseConfigurationFileReference": "18c1723432283e0cc55f10a6dcfd9e023b4e622549547751db70167aba3359c6", "buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CODE_SIGN_IDENTITY": "-", "COPY_PHASE_STRIP": "NO", "DEAD_CODE_STRIPPING": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "MACOSX_DEPLOYMENT_TARGET": "10.14", "MTL_ENABLE_DEBUG_INFO": "NO", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02bd86a90f1c36dbd8aefd3aaeb694545c", "name": "Profile"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "18c1723432283e0cc55f10a6dcfd9e0290d3d1ed97e73aa53c07fd80de982e98", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "18c1723432283e0cc55f10a6dcfd9e0224b190b0ed104f7b1fa5480db83abd20", "path": "MainFlutterWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "18c1723432283e0cc55f10a6dcfd9e026174d6df16af839588ab7942437f2f71", "path": "DebugProfile.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "text.plist.entitlements", "guid": "18c1723432283e0cc55f10a6dcfd9e02f8c88c09355594ab81c43ca5184ccc3c", "path": "Release.entitlements", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "18c1723432283e0cc55f10a6dcfd9e02896112a93f2a09f72db0bec985ef4c64", "path": "Runner/Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.xib", "guid": "18c1723432283e0cc55f10a6dcfd9e02763f538027d5d06f7f7a9adf4a25ba48", "path": "Base.lproj/MainMenu.xib", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "18c1723432283e0cc55f10a6dcfd9e028c91953baeab2ee7d542aceae751d8da", "name": "MainMenu.xib", "path": "Runner", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "text.plist.xml", "guid": "18c1723432283e0cc55f10a6dcfd9e0252d81aa16345ac96bed37c28cf286c96", "path": "Runner/Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "18c1723432283e0cc55f10a6dcfd9e0299518cee7385cdc4e83ab27b28daa3a5", "name": "Resources", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e02c4902511cd7de506ddcb4d4fe24c1302", "path": "AppInfo.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e025ea8168e7f361777a2c24e316569906b", "path": "Debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e023b4e622549547751db70167aba3359c6", "path": "Release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e020a4669ed274c6c98bfb089e33e31f774", "path": "Warnings.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "18c1723432283e0cc55f10a6dcfd9e024574b2ccd21665db30e863fff96c66a3", "name": "Configs", "path": "Configs", "sourceTree": "<group>", "type": "group"}], "guid": "18c1723432283e0cc55f10a6dcfd9e026e18656a8cc965ad690967d031085e97", "name": "Runner", "path": "Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "18c1723432283e0cc55f10a6dcfd9e02763b891d23f1b765a6d89a076d8c809a", "path": "GeneratedPluginRegistrant.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e02de8f22173ac02076f5e1a4037a7a022d", "path": "Flutter-Debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e02cbbce583d8fa2f589e5976aaa6ca4838", "path": "Flutter-Release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "18c1723432283e0cc55f10a6dcfd9e02ab1b4c8075174045e5db4076dff6212a", "path": "ephemeral/Flutter-Generated.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "18c1723432283e0cc55f10a6dcfd9e02269bece1d8d8e3a750b7445e01eaaa26", "name": "Flutter", "path": "Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "18c1723432283e0cc55f10a6dcfd9e021be7331c41a267304d507f2f7ede3183", "path": "RunnerTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "18c1723432283e0cc55f10a6dcfd9e023be7f6069b95e917b68b999c925dc718", "name": "RunnerTests", "path": "RunnerTests", "sourceTree": "<group>", "type": "group"}, {"guid": "18c1723432283e0cc55f10a6dcfd9e024a77263bfa3206c1680d175d55f390a4", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "18c1723432283e0cc55f10a6dcfd9e0241f010674bf28d8d1dbb94f79bb86245", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "18c1723432283e0cc55f10a6dcfd9e02074de2f2f27c856d1f92cc4593463861", "name": "Runner", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "18c1723432283e0cc55f10a6dcfd9e02", "path": "/Users/<USER>/StudioProjects/ghanshyam_murti_bhandar/macos/Runner.xcodeproj", "projectDirectory": "/Users/<USER>/StudioProjects/ghanshyam_murti_bhandar/macos", "targets": ["TARGET@v11_hash=04d229fb7faa86e705f319426eea0fa5", "TARGET@v11_hash=b1e9d54d06ec119da89cdec11da88f49", "TARGET@v11_hash=ae8e777923fb91324939a7378b9c75e1"]}