PODS:
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
  - razorpay-pod (1.4.0)
  - razorpay_flutter (1.1.10):
    - Flutter
    - razorpay-pod
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - razorpay_flutter (from `.symlinks/plugins/razorpay_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - razorpay-pod

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  razorpay_flutter:
    :path: ".symlinks/plugins/razorpay_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  razorpay-pod: cb5b5439697bdda9c57d68a92b3e5b0ce1d2f2f3
  razorpay_flutter: 0e98e4fcaae27ad50e011d85f66d85e0a008754a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
